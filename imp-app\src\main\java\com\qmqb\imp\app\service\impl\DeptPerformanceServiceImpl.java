package com.qmqb.imp.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzed.structure.tool.enums.BoolFlagEnum;
import com.qmqb.imp.app.domain.bo.DateBO;
import com.qmqb.imp.app.domain.vo.MessageInfoVO;
import com.qmqb.imp.app.domain.vo.PerformancePublicListVO;
import com.qmqb.imp.app.domain.vo.PerformanceReportVO;
import com.qmqb.imp.app.service.DeptPerformanceService;
import com.qmqb.imp.common.constant.UserConstants;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.enums.RoleKeyEnum;
import com.qmqb.imp.common.enums.WarnLevelEnum;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.system.domain.GitStatisticsInfo;
import com.qmqb.imp.system.domain.UserKqStat;
import com.qmqb.imp.system.domain.WarnRecord;
import com.qmqb.imp.system.domain.WorkStat;
import com.qmqb.imp.system.domain.bo.PerformanceRegistrationBo;
import com.qmqb.imp.system.domain.dto.WarnStatDTO;
import com.qmqb.imp.system.domain.vo.PerformanceRegistrationVo;
import com.qmqb.imp.system.mapper.GitStatisticsInfoMapper;
import com.qmqb.imp.system.mapper.SysUserMapper;
import com.qmqb.imp.system.mapper.WarnRecordMapper;
import com.qmqb.imp.system.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeptPerformanceServiceImpl implements DeptPerformanceService {

    private final IPerformanceRegistrationService performanceRegistrationService;
    private final IWarnRecordService warnRecordService;
    private final ISysUserService sysUserService;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final IUserKqStatService userKqStatService;
    private final IWorkStatService workStatService;
    private final IZtDocService ztDocService;
    private final GitStatisticsInfoMapper gitStatisticsInfoMapper;
    private final WarnRecordMapper warnRecordMapper;
    private final SysUserMapper sysUserMapper;

    /**
     * 消息处理情况
     *
     * @return
     */
    @Override
    public R<MessageInfoVO> messageInfo() {
        List<Long> userIds = sysUserService.selectAllUser().stream().map(SysUser::getUserId).collect(Collectors.toList());
        List<WarnStatDTO> dtos = warnRecordService.listByUserIdListAndStatus(userIds);
        long groupP0Count = dtos.stream().filter(dto -> Objects.equals(WarnLevelEnum.P0.getValue(), dto.getWarnLevel())).count();
        long groupP1Count = dtos.stream().filter(dto -> Objects.equals(WarnLevelEnum.P1.getValue(), dto.getWarnLevel())).count();
        long groupP2Count = dtos.stream().filter(dto -> Objects.equals(WarnLevelEnum.P2.getValue(), dto.getWarnLevel())).count();
        return R.ok(MessageInfoVO.builder().group(Math.addExact(Math.addExact(groupP0Count, groupP1Count), groupP2Count))
            .p0(MessageInfoVO.Count.builder().group(groupP0Count).build())
            .p1(MessageInfoVO.Count.builder().group(groupP1Count).build())
            .p2(MessageInfoVO.Count.builder().group(groupP2Count).build())
            .build());
    }

    /**
     * 部门绩效报告
     *
     * @param bo
     * @return
     */
    @Override
    public R<PerformanceReportVO> deptPerformanceReport(DateBO bo) {
        int year = DateUtil.year(bo.getDate());
        int month = DateUtil.month(bo.getDate()) + 1;
        LoginUser loginUser = LoginHelper.getLoginUser();
        SysUser sysUser = sysUserService.selectUserById(loginUser.getUserId());
        List<SysUser> sysUsers = sysUserService.selectAllUser();
        List<String> nickNameList = sysUsers.stream().map(SysUser::getNickName).collect(Collectors.toList());
        PerformanceReportVO vo = PerformanceReportVO.builder().name("技术中心").build();
        // 考勤数据
        CompletableFuture<List<UserKqStat>> userKqStatCompletableFuture = CompletableFuture.supplyAsync(() -> {
            List<UserKqStat> userKqStats = userKqStatService.listByUserNamesAndKqYearAndKqMonth(nickNameList, String.valueOf(year), String.valueOf(month));
            if (CollectionUtil.isEmpty(userKqStats)) {
                PerformanceReportVO.ManHour manHour = PerformanceReportVO.ManHour.builder().day(BigDecimal.ZERO).total(BigDecimal.ZERO).build();
                vo.setManHour(manHour);
                vo.setAttendanceDay(BigDecimal.ZERO);
            } else {
                BigDecimal allKqAttendanceWorkTime = userKqStats.stream().map(userKqStat -> BigDecimal.valueOf(userKqStat.getKqAttendanceWorkTime())).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal allKqAttendanceWorkDay = userKqStats.stream().map(userKqStat -> BigDecimal.valueOf(userKqStat.getKqAttendanceDays())).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal averWorkTimeDay = NumberUtil.div(allKqAttendanceWorkTime, BigDecimal.valueOf(userKqStats.size())).divide(BigDecimal.valueOf(60), 2, RoundingMode.UP);
                BigDecimal averKqAttendanceWorkDay = NumberUtil.div(allKqAttendanceWorkDay, BigDecimal.valueOf(userKqStats.size()), 2, RoundingMode.UP);
                PerformanceReportVO.ManHour manHour = PerformanceReportVO.ManHour.builder().total(averWorkTimeDay).day(averKqAttendanceWorkDay).build();
                vo.setManHour(manHour);
                vo.setAttendanceDay(averKqAttendanceWorkDay);
            }
            return userKqStats;
        }, threadPoolTaskExecutor);
        // 文档数据
        DateTime beginOfMonth = DateUtil.beginOfMonth(bo.getDate());
        DateTime endOfMonth = DateUtil.endOfMonth(bo.getDate());
        List<String> userNameList = sysUsers.stream().map(SysUser::getUserName).collect(Collectors.toList());
        CompletableFuture<Long> docFuture = CompletableFuture.supplyAsync(() -> {
            Long count = ztDocService.countByAddedByAndAddedDate(userNameList, beginOfMonth, endOfMonth);
            vo.setDocs(NumberUtil.div(BigDecimal.valueOf(count), BigDecimal.valueOf(userNameList.size()), 2, RoundingMode.UP));
            return count;
        }, threadPoolTaskExecutor);
        // 任务数据
        CompletableFuture<List<WorkStat>> workStatCompletableFuture = CompletableFuture.supplyAsync(() -> {

            List<WorkStat> workStats = workStatService.listByUserNameAndYearAndMonth(nickNameList, year, month);
            if (CollectionUtil.isEmpty(workStats)) {
                vo.setCompletedTasks(BigDecimal.ZERO);
                vo.setCases(BigDecimal.ZERO);
                vo.setCreatedBugs(BigDecimal.ZERO);
                vo.setResolvedBugs(BigDecimal.ZERO);
            } else {
                BigDecimal personCount = BigDecimal.valueOf(workStats.size());
                BigDecimal allWorkDoneClosedTaskCount = workStats.stream().map(workStat -> BigDecimal.valueOf(workStat.getWorkDoneClosedTaskCount())).reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setCompletedTasks(NumberUtil.div(allWorkDoneClosedTaskCount, personCount, 2, RoundingMode.UP));
            }
            return workStats;
        }, threadPoolTaskExecutor);
        CompletableFuture<List<WorkStat>> testAndBugStatCompletableFuture = CompletableFuture.supplyAsync(() -> {
            List<WorkStat> workStats = workStatService.listByUserNameAndYearAndMonth(nickNameList, year, month);
            if (CollectionUtil.isEmpty(workStats)) {
                vo.setCompletedTasks(BigDecimal.ZERO);
                vo.setCases(BigDecimal.ZERO);
                vo.setCreatedBugs(BigDecimal.ZERO);
                vo.setResolvedBugs(BigDecimal.ZERO);
            } else {
                QueryWrapper<SysUser> testerWrapper = new QueryWrapper<>();
                testerWrapper.eq("u.status", UserConstants.USER_NORMAL);
                testerWrapper.eq("u.del_flag", UserConstants.NORMAL);
                testerWrapper.eq("r.role_key", RoleKeyEnum.TESTER.getCode());
                List<SysUser> testerUsers = sysUserMapper.selectUserVoList(testerWrapper);
                QueryWrapper<SysUser> developerWrapper = new QueryWrapper<>();
                developerWrapper.eq("u.status", UserConstants.USER_NORMAL);
                developerWrapper.eq("r.role_key", RoleKeyEnum.DEVELOPER.getCode());
                developerWrapper.eq("u.del_flag", UserConstants.NORMAL);
                List<SysUser> developerUsers = sysUserMapper.selectUserVoList(developerWrapper);

                BigDecimal allWorkCaseCount = workStats.stream().map(workStat -> BigDecimal.valueOf(workStat.getWorkCaseCount())).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal allCreateBugCount = workStats.stream().map(workStat -> BigDecimal.valueOf(workStat.getCreateBugCount())).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal allWorkResolveBugCount = workStats.stream().map(workStat -> BigDecimal.valueOf(workStat.getWorkResolveBugCount())).reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setCases(NumberUtil.div(allWorkCaseCount, BigDecimal.valueOf(testerUsers.size()), 2, RoundingMode.UP));
                vo.setCreatedBugs(NumberUtil.div(allCreateBugCount, BigDecimal.valueOf(developerUsers.size()), 2, RoundingMode.UP));
                vo.setResolvedBugs(NumberUtil.div(allWorkResolveBugCount, BigDecimal.valueOf(developerUsers.size()), 2, RoundingMode.UP));
            }
            return workStats;
        }, threadPoolTaskExecutor);

        // 代码数据
        CompletableFuture<List<GitStatisticsInfo>> codesFuture = CompletableFuture.supplyAsync(() -> {
            QueryWrapper<SysUser> wrapper = new QueryWrapper<>();
            wrapper.eq("u.status", UserConstants.USER_NORMAL);
            wrapper.eq("u.del_flag", UserConstants.NORMAL);
            wrapper.eq("r.role_key", RoleKeyEnum.DEVELOPER.getCode());
            List<SysUser> developerUsers = sysUserMapper.selectUserVoList(wrapper);
            List<String> gitCommitterNameList = developerUsers.stream().map(SysUser::getGitCommitterName).collect(Collectors.toList());
            List<String> gitAuthorNameList = developerUsers.stream().map(SysUser::getGitAuthorName).collect(Collectors.toList());
            gitCommitterNameList.addAll(gitAuthorNameList);
            List<GitStatisticsInfo> gitStatisticsInfos = gitStatisticsInfoMapper.selectList(Wrappers.lambdaQuery(GitStatisticsInfo.class)
                .in(GitStatisticsInfo::getCommitter, gitCommitterNameList)
                .le(GitStatisticsInfo::getAdditionsLine, 1000)
                .between(GitStatisticsInfo::getCommitDate, beginOfMonth, endOfMonth)
                .apply("message NOT LIKE '%Merge%'")
                .apply("message NOT LIKE '%merge%'")
                .apply("message NOT LIKE '%合并分支%'")
                .apply("message NOT LIKE '%Conflict%'"));
            if (CollUtil.isEmpty(gitStatisticsInfos)) {
                vo.setCodes(BigDecimal.ZERO);
            } else {
                BigDecimal codes = gitStatisticsInfos.stream().map(e -> BigDecimal.valueOf(e.getAdditionsLine())).reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setCodes(NumberUtil.div(codes, BigDecimal.valueOf(developerUsers.size()), 2, RoundingMode.UP));
            }
            return gitStatisticsInfos;
        }, threadPoolTaskExecutor);
        // 预警数据
        CompletableFuture<List<WarnRecord>> warnFuture = CompletableFuture.supplyAsync(() -> {
            QueryWrapper<WarnRecord> wrapper = Wrappers.query();
            List<Long> userIds = sysUsers.stream().map(SysUser::getUserId).collect(Collectors.toList());
            wrapper.in("t1.`user_id`", userIds);
            wrapper.between("t1.`create_time`", beginOfMonth, endOfMonth);
            List<WarnRecord> warnRecords = warnRecordMapper.selectWarnRecordList(wrapper);
            if (CollUtil.isEmpty(warnRecords)) {
                vo.setWarning(PerformanceReportVO.Warning.builder().total(BigDecimal.ZERO).unprocessed(BigDecimal.ZERO).build());
            } else {
                vo.setWarning(PerformanceReportVO.Warning.builder()
                    .total(BigDecimal.valueOf(warnRecords.size()))
                    .unprocessed(BigDecimal.valueOf(warnRecords.stream()
                        .filter(warnRecord -> Objects.equals(warnRecord.getDetail().getHandleStatus(), BoolFlagEnum.NO.getStatus()))
                        .count()))
                    .build());
            }
            return warnRecords;
        }, threadPoolTaskExecutor);
        try {
            CompletableFuture.allOf(userKqStatCompletableFuture, docFuture, workStatCompletableFuture, codesFuture, warnFuture, testAndBugStatCompletableFuture).get();
        } catch (InterruptedException | ExecutionException e) {
            log.error("获取数据异常", e);
            return R.fail("获取数据异常");
        }
        return R.ok(vo);
    }

    /**
     * 最新绩效公示名单
     *
     * @return
     */
    @Override
    public R<PerformancePublicListVO> performancePublicList() {
        DateTime now = DateUtil.date();
        PerformanceRegistrationBo bo = PerformanceRegistrationBo.builder().evalYear(String.valueOf(DateUtil.year(now))).evalMonth(String.valueOf(DateUtil.month(now))).build();
        if (bo.getEvalMonth().length() == 1) {
            bo.setEvalMonth("0" + bo.getEvalMonth());
        }
        List<PerformanceRegistrationVo> vos = performanceRegistrationService.queryList(bo);
        PerformancePublicListVO performancePublicListVO = PerformancePublicListVO.builder().build();
        if (CollectionUtil.isNotEmpty(vos)) {
            String content = vos.stream().map(PerformanceRegistrationVo::getUserName).collect(Collectors.joining(StringPool.SPACE));
            performancePublicListVO.setDate(DateUtil.format(vos.get(0).getCreateTime(), DatePattern.CHINESE_DATE_PATTERN));
            performancePublicListVO.setContent(content);
        }
        return R.ok(performancePublicListVO);
    }
}
