package com.qmqb.imp.system.domain.vo;

import com.qmqb.imp.system.domain.SlowQueryDbList;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;


/**
 * 分支详细信息对象 tb_branch
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
@Data
public class SlowQueryDbListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Schema(description = "id")
    private Long id;
    /**
     * dbCode
     */
    @Schema(description = "dbCode")
    private String dbCode;
    /**
     * dbName
     */
    @Schema(description = "dbName")
    private String dbName;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    public static SlowQueryDbListVO of(SlowQueryDbList slowQueryDbList) {
        if (slowQueryDbList == null) {
            return null;
        }
        SlowQueryDbListVO slowQueryDbListVO = new SlowQueryDbListVO();
        BeanUtils.copyProperties(slowQueryDbList, slowQueryDbListVO);
        return slowQueryDbListVO;
    }

}
