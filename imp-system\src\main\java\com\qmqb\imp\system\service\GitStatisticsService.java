package com.qmqb.imp.system.service;

import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.system.domain.vo.CodeStatisticsVO;

import java.util.List;

/**
 * <p>
 * git代码统计
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-12
 */
public interface GitStatisticsService {
    /**
     * 年度工作量曲线
     *
     * @param year         年份
     * @param deptId       开发组
     * @param isNotInclude 不包含技术经理：false-否，true-是
     * @return
     */
    R<List<CodeStatisticsVO>> statistics(Integer year, Long deptId, Boolean isNotInclude);


}
