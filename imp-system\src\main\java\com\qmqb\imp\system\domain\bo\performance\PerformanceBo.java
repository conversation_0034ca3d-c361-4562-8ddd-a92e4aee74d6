package com.qmqb.imp.system.domain.bo.performance;

import com.qmqb.imp.common.core.domain.BaseEntity;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 绩效点评主业务对象 tb_performance
 *
 * <AUTHOR>
 * @date 2025-07-01
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class PerformanceBo extends BaseEntity {

    /**
     *
     */
    private Long id;

    /**
     * 员工昵称
     */
    private String nickName;

    /**
     * 绩效年份
     */
    private Integer year;

    /**
     * 绩效月份，格式MM
     */
    private Integer month;

    /**
     * 所属组ID
     */
    private Long groupId;

    /**
     * 所属组
     */
    private String groupName;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 角色
     */
    private String role;

    /**
     * 总评等级S/A/B/C/D
     */
    private String totalLevel;

    /**
     * 总评等级S/A/B/C/D
     */
    private List<String> totalLevels;

    /**
     * 评审绩效等级S/A/B/C/D
     */
    private String reviewLevel;

    /**
     * 最终绩效等级S/A/B/C/D
     */
    private String finalLevel;

    /**
     * 核准时间
     */
    private Date approvalTime;

    /**
     * 是否邮件报送，0为不发，1为发送
     */
    private String emailSentFlag;

    /**
     * 邮件报送时间
     */
    private Date emailSentTime;

    /**
     * 备注
     */
    private String remark;


}
