package com.qmqb.imp.web.controller.business;

import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.system.domain.bo.ZtProductBo;
import com.qmqb.imp.system.domain.vo.ZtProductVo;
import com.qmqb.imp.system.service.IZtProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 禅道产品控制器
 */

@RestController
@RequestMapping("/product")
public class TzProductController extends BaseController {

    @Autowired
    private  IZtProductService productService;

    /**
     * 获取产品列表
     * @return
     */
    @GetMapping("/list")
    public R<List<ZtProductVo>> getProductList(){
        ZtProductBo ztProductBo = new ZtProductBo();
        ztProductBo.setDeleted("0");
        List<ZtProductVo> ztProductVos = productService.queryList(ztProductBo);
        return R.ok(ztProductVos);
    }

}
