package com.qmqb.imp.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.time.LocalDate;


/**
 * 【请填写功能名称】视图对象 tb_warning_record
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
@ExcelIgnoreUnannotated
public class WarningRecordVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 预警类型
     */
    @ExcelProperty(value = "预警类型")
    private String type;

    /**
     * 发送时间
     */
    @ExcelProperty(value = "发送时间")
    private LocalDate sendTime;

    /**
     * 预警内容
     */
    @ExcelProperty(value = "预警内容")
    private String context;

    /**
     * 预警参数
     */
    @ExcelProperty(value = "预警参数")
    private String params;


}
