package com.qmqb.imp.job.indicator.indicators.impl;

import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 工作效率情况指标等级计算
 * @date 2025/7/2 16:36
 */
@Service
public class DevEfficiencyIndicatorServiceImpl implements IndicatorLevelCalcService {
    @Override
    public String getIndicatorCode() {
        return PerformanceIndicatorEnum.DEV_EFFICIENCY.getCode();
    }

    @Override
    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults) {
        return ScoreLevelEnum.SCORE_B.getCode();
    }

    @Override
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level) {
        return "";
    }

    /**
     * 获取评级原因
     *
     * @param level
     * @return
     */
    private String getRatingReason(String level, Integer num) {
        switch (level) {
            case "S":
                return "独立设计技术方案\n" +
                        "按时完成重点开发任务\n" +
                        "发布后无回滚且上线一周无生产故障\n" +
                        "有重大创新成果";
            case "A":
                return "按时完成重点开发任务\n" +
                        "发布后无回滚且上线一周无生产故障\n" +
                        "有创新成果";
            case "B":
                return "按时完成开发任务";
            case "C":
                return String.format("不能按时完成开发任务数(%d)>=1", num);
            case "D":
                return String.format("不能按时完成开发任务数(%d)>2", num);
            default:
                return null;
        }
    }

}
