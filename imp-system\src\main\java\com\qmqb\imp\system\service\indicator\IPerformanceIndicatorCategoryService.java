package com.qmqb.imp.system.service.indicator;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceIndicatorCategoryBo;
import com.qmqb.imp.system.domain.performance.PerformanceIndicatorCategory;
import com.qmqb.imp.system.domain.performance.PerformanceIndicator;
import com.qmqb.imp.system.domain.vo.performance.PerformanceIndicatorCategoryVo;

import java.util.Collection;
import java.util.List;

/**
 * 绩效指标分类Service接口
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IPerformanceIndicatorCategoryService extends IService<PerformanceIndicatorCategory> {

    /**
     * 根据ID查询绩效指标分类
     *
     * @param id 绩效指标分类主键
     * @return 绩效指标分类视图对象
     */
    PerformanceIndicatorCategoryVo queryById(Long id);

    /**
     * 根据主表ID查询绩效指标分类
     *
     * @param ids 绩效主键集合
     * @return 绩效指标分类视图对象
     */
    List<PerformanceIndicatorCategoryVo> queryByPerformanceIds(List<Long> ids);

    /**
     * 分页查询绩效指标分类列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 分页结果数据
     */
    TableDataInfo<PerformanceIndicatorCategoryVo> queryPageList(PerformanceIndicatorCategoryBo bo, PageQuery pageQuery);

    /**
     * 查询绩效指标分类列表
     *
     * @param bo 查询条件
     * @return 绩效指标分类视图对象列表
     */
    List<PerformanceIndicatorCategoryVo> queryList(PerformanceIndicatorCategoryBo bo);

    /**
     * 新增绩效指标分类
     *
     * @param bo 绩效指标分类业务对象
     * @return 是否新增成功
     */
    Boolean insertByBo(PerformanceIndicatorCategoryBo bo);

    /**
     * 修改绩效指标分类
     *
     * @param bo 绩效指标分类业务对象
     * @return 是否修改成功
     */
    Boolean updateByBo(PerformanceIndicatorCategoryBo bo);

    /**
     * 校验并批量删除绩效指标分类信息
     *
     * @param ids     需要删除的绩效指标分类主键集合
     * @param isValid 是否校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 计算分类等级
     *
     * @param indicators     该分类下的所有指标
     * @param categoryCode   分类code
     * @param categoryName   分类名称
     * @param categoryWeight 分类权重（已弃用）
     * @return 分类等级计算结果
     */
    PerformanceIndicatorCategory calculateCategoryLevel(List<PerformanceIndicator> indicators, String categoryCode, String categoryName, Integer categoryWeight);

    /**
     * 根据绩效ID查询所有分类
     *
     * @param performanceId 绩效ID
     * @return 分类列表
     */
    List<PerformanceIndicatorCategory> getByPerformanceId(Long performanceId);
}
