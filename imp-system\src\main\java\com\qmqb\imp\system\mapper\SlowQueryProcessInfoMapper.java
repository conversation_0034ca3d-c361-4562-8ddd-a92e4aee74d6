package com.qmqb.imp.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.SlowQueryInfo;
import com.qmqb.imp.system.domain.SlowQueryProcessInfo;
import com.qmqb.imp.system.domain.vo.SlowQueryInfoVo;
import org.apache.ibatis.annotations.Param;

/**
 * 慢sql信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@DS(DataSource.SLOWSQL)
public interface SlowQueryProcessInfoMapper extends BaseMapperPlus<SlowQueryProcessInfoMapper, SlowQueryProcessInfo, SlowQueryInfoVo> {

    /**
     * 插入或更新处理信息
     *
     * @param slowQueryProcessInfo
     */
    void insertOrUpdateProcessInfo(@Param("info") SlowQueryProcessInfo slowQueryProcessInfo);
}
