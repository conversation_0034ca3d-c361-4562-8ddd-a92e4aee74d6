package com.qmqb.imp.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 绩效结果统计
 *
 * <AUTHOR>
 * @date 2024-04-16
 */
@Data
public class PerformanceStatisticVO  implements Serializable {

    /**
     * 所属组名
     */
    private String deptName;

    /**
     * 姓名
     */
    private String userName;
    /**
     * S次数
     */
    @JsonProperty("sCount")
    private Integer sCount;

    /**
     * A次数
     */
    @JsonProperty("aCount")
    private Integer aCount;

    /**
     * C次数
     */
    @JsonProperty("cCount")
    private Integer cCount;

}
