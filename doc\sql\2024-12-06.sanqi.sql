alter table tb_home_trend
    add task_closed int default 0 null comment '关闭任务数' after task_pause;

alter table tb_home_trend
    add task_cancel int default 0 null comment '取消任务数' after task_closed;

#自动同步请假时长数据
alter table tb_user_leave
    add constraint uidx_ding_talk_no unique (ding_talk_no);
alter table tb_user_leave
    modify create_by varchar (64) default 'admin' not null comment '创建者',
    modify create_time datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    modify update_by varchar (64) default 'admin' not null comment '更新者',
    modify update_time datetime default CURRENT_TIMESTAMP not null on
update CURRENT_TIMESTAMP comment '更新时间';

create table tb_scan_project
(
    id               bigint                             not null comment '主键id'
        primary key,
    p_id             int                                not null comment '项目id',
    scan_name        varchar(200) null comment '项目名称',
    scan_namespace   varchar(300) null comment '项目路径',
    scan_describe    varchar(300) null comment '项目描述',
    web_url          varchar(300) null comment 'web访问地址',
    last_commit_time datetime null comment '最后提交时间',
    dev_dept         bigint null comment '负责开发组',
    last_scan_time   datetime null comment '最新扫描时间',
    blocker_amount   int null comment '严重问题数',
    critical_amount  int null comment '一般问题数',
    major_amount     int null comment '轻微问题数',
    create_time      datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time      datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag         tinyint  default 0 null comment '删除标志（0代表存在 2代表删除）',
    scan_version     bigint   default 0                 not null comment '扫描版本号（每次往上加一）',
    last_scan_flag   tinyint null comment '最后扫描的记录（1新纪录，0旧记录）',
    constraint tb_scan_project_p_id_scan_version_uindex
        unique (p_id, scan_version)
) comment '扫描项目记录表';

create index tb_scan_project_last_scan_time_index
    on tb_scan_project (last_scan_time);

create index tb_scan_project_scan_name_index
    on tb_scan_project (scan_name);

create table tb_scan_project_detail
(
    id                bigint auto_increment comment '主键id'
        primary key,
    p_id              int null comment '项目id',
    is_handle         tinyint null comment '是否解决（0未解决，1已解决）',
    scan_file_url     varchar(300) null comment '问题文件路径',
    scan_begin_line   int null comment '开始行',
    scan_end_line     int null comment '结束行',
    scan_begin_column int null comment '开始列',
    scan_end_column   int null comment '结束列',
    scan_version      bigint null comment '扫描版本号',
    scan_rule         varchar(256) null comment '扫描规则',
    scan_rule_set     varchar(256) null comment '扫描规则集',
    scan_class        varchar(256) null comment '扫描类',
    scan_priority     int null comment '优先级',
    scan_method       varchar(256) null comment '扫描的方法',
    scan_variable     varchar(256) null comment '扫描的变量',
    scan_package      varchar(1024) null comment '扫描的包路径',
    scan_describe     varchar(2048) null comment '扫描问题描述',
    del_flag          tinyint  default 0 null comment '删除标志（0代表存在 2代表删除）',
    create_time       datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time       datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
) comment '扫描项目记录详情';

create index tb_scan_project_detail_p_id_is_handle_index
    on tb_scan_project_detail (p_id, is_handle);

begin;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
                        `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
                        `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (1866009468595687426, '代码质量管理', 1600371350308102145, 2, 'quality', 'code/quality/list', NULL, 1, 0, 'C',
        '0', '0', 'system:scanProject:list', 'rate', 'admin', '2024-12-09 14:38:21', 'admin', '2024-12-16 17:55:31',
        '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`,
                        `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`,
                        `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (1866033631083450369, '扫描报告', 1600371350308102145, 2, 'qualityDetail', 'code/quality/detail', NULL, 1, 1,
        'C', '1', '0', 'system:scanProjectDetail:list', '#', 'admin', '2024-12-09 16:14:22', 'admin',
        '2024-12-16 17:34:05', '');
commit;
