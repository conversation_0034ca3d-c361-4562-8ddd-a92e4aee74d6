package com.qmqb.imp.app.controller;

import com.qmqb.imp.app.domain.bo.DateBO;
import com.qmqb.imp.app.domain.vo.MessageInfoVO;
import com.qmqb.imp.app.domain.vo.PerformancePublicListVO;
import com.qmqb.imp.app.domain.vo.PerformanceReportVO;
import com.qmqb.imp.app.service.DeptPerformanceService;
import com.qmqb.imp.common.core.domain.R;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 部门绩效
 *
 * <AUTHOR> <PERSON>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/dept-performance")
public class DeptPerformanceController {

    private final DeptPerformanceService deptPerformanceService;

    /**
     * 消息处理情况
     *
     * @return 结果
     */
    @GetMapping("/message-info")
    public R<MessageInfoVO> messageInfo() {
        return deptPerformanceService.messageInfo();
    }

    /**
     * 部门绩效报告
     *
     * @return 结果
     */
    @GetMapping("/dept-performance-report")
    public R<PerformanceReportVO> deptPerformanceReport(@Valid DateBO bo) {
        return deptPerformanceService.deptPerformanceReport(bo);
    }

    /**
     * 最新绩效公示名单
     *
     * @return 结果
     */
    @GetMapping("/performance-public-list")
    public R<PerformancePublicListVO> performancePublicList() {
        return deptPerformanceService.performancePublicList();
    }

}
