package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.PersonTypeEnum;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.system.domain.DataBasePermission;
import com.qmqb.imp.system.domain.bo.DataBasePermissionCreateBo;
import com.qmqb.imp.system.domain.bo.DataBasePermissionQueryBo;
import com.qmqb.imp.system.domain.bo.DataBasePermissionUpdateBo;
import com.qmqb.imp.system.domain.vo.DataBasePermissionVo;
import com.qmqb.imp.system.mapper.DataBasePermissionMapper;
import com.qmqb.imp.system.service.IDataBasePermissionService;
import com.qmqb.imp.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 数据库权限Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@RequiredArgsConstructor
@Service
public class DataBasePermissionServiceImpl implements IDataBasePermissionService {

    private final DataBasePermissionMapper baseMapper;

    private final ISysUserService sysUserService;

    /**
     * 查询数据库权限
     */
    @Override
    public DataBasePermissionVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询数据库权限列表
     */
    @Override
    public TableDataInfo<DataBasePermissionVo> queryPageList(DataBasePermissionQueryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<DataBasePermission> lqw = buildQueryWrapper(bo);
        Page<DataBasePermissionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询数据库权限列表
     */
    @Override
    public List<DataBasePermissionVo> queryList(DataBasePermissionQueryBo bo) {
        LambdaQueryWrapper<DataBasePermission> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<DataBasePermission> buildQueryWrapper(DataBasePermissionQueryBo bo) {
        LambdaQueryWrapper<DataBasePermission> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getDbName()), DataBasePermission::getDbName, bo.getDbName());
        lqw.like(StringUtils.isNotBlank(bo.getGroupId()), DataBasePermission::getGroupIds, bo.getGroupId());
        lqw.like(StringUtils.isNotBlank(bo.getMemberName()), DataBasePermission::getMemberNames, bo.getMemberName());
        return lqw;
    }

    /**
     * 新增数据库权限
     */
    @Override
    public Boolean insertByBo(DataBasePermissionCreateBo bo) {
        DataBasePermission add = BeanUtil.toBean(bo, DataBasePermission.class);
        if (baseMapper.exists(new LambdaQueryWrapper<DataBasePermission>()
            .eq(DataBasePermission::getDbName, bo.getDbName()))) {
            throw new ServiceException("新增失败，数据库权限配置已经存在");
        }
        return baseMapper.insert(add) > 0;
    }

    /**
     * 修改数据库权限
     */
    @Override
    public Boolean updateByBo(DataBasePermissionUpdateBo bo) {
        DataBasePermission update = BeanUtil.toBean(bo, DataBasePermission.class);
        if (baseMapper.exists(new LambdaQueryWrapper<DataBasePermission>()
            .eq(DataBasePermission::getDbName, bo.getDbName())
            .ne(DataBasePermission::getId, bo.getId()))) {
            throw new ServiceException("修改失败，数据库权限配置已经存在");
        }
        return baseMapper.updateById(update) > 0;
    }


    /**
     * 批量删除数据库权限
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
