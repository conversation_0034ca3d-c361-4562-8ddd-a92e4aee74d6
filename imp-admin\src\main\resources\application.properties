# \u9879\u76EE\u76F8\u5173\u914D\u7F6E
# \u540D\u79F0
ruoyi.name=imp-admin
# \u7248\u672C
ruoyi.version=${imp.version}
# \u7248\u6743\u5E74\u4EFD
ruoyi.copyrightYear=2022
# \u5B9E\u4F8B\u6F14\u793A\u5F00\u5173
ruoyi.demoEnabled=true
# \u83B7\u53D6ip\u5730\u5740\u5F00\u5173
ruoyi.addressEnabled=true
# \u7F13\u5B58\u61D2\u52A0\u8F7D
ruoyi.cacheLazy=false

# \u9875\u9762 <\u53C2\u6570\u8BBE\u7F6E> \u53EF\u5F00\u542F\u5173\u95ED \u9A8C\u8BC1\u7801\u6821\u9A8C
# \u9A8C\u8BC1\u7801\u7C7B\u578B math \u6570\u7EC4\u8BA1\u7B97 char \u5B57\u7B26\u9A8C\u8BC1
captcha.type=MATH
# line \u7EBF\u6BB5\u5E72\u6270 circle \u5706\u5708\u5E72\u6270 shear \u626D\u66F2\u5E72\u6270
captcha.category=CIRCLE
# \u6570\u5B57\u9A8C\u8BC1\u7801\u4F4D\u6570
captcha.numberLength=1
# \u5B57\u7B26\u9A8C\u8BC1\u7801\u957F\u5EA6
captcha.charLength=4

# \u5F00\u53D1\u73AF\u5883\u914D\u7F6E
# \u670D\u52A1\u5668\u7684HTTP\u7AEF\u53E3\uFF0C\u9ED8\u8BA4\u4E3A8080
server.port=8080
# \u5E94\u7528\u7684\u8BBF\u95EE\u8DEF\u5F84
server.servlet.context-path=/imp-admin
# undertow \u914D\u7F6E
# HTTP post\u5185\u5BB9\u7684\u6700\u5927\u5927\u5C0F\u3002\u5F53\u503C\u4E3A-1\u65F6\uFF0C\u9ED8\u8BA4\u503C\u4E3A\u5927\u5C0F\u662F\u65E0\u9650\u7684
server.undertow.max-http-post-size=-1
# \u4EE5\u4E0B\u7684\u914D\u7F6E\u4F1A\u5F71\u54CDbuffer,\u8FD9\u4E9Bbuffer\u4F1A\u7528\u4E8E\u670D\u52A1\u5668\u8FDE\u63A5\u7684IO\u64CD\u4F5C,\u6709\u70B9\u7C7B\u4F3Cnetty\u7684\u6C60\u5316\u5185\u5B58\u7BA1\u7406
# \u6BCF\u5757buffer\u7684\u7A7A\u95F4\u5927\u5C0F,\u8D8A\u5C0F\u7684\u7A7A\u95F4\u88AB\u5229\u7528\u8D8A\u5145\u5206
server.undertow.buffer-size=512
# \u662F\u5426\u5206\u914D\u7684\u76F4\u63A5\u5185\u5B58
server.undertow.direct-buffers=true
# \u8BBE\u7F6EIO\u7EBF\u7A0B\u6570, \u5B83\u4E3B\u8981\u6267\u884C\u975E\u963B\u585E\u7684\u4EFB\u52A1,\u5B83\u4EEC\u4F1A\u8D1F\u8D23\u591A\u4E2A\u8FDE\u63A5, \u9ED8\u8BA4\u8BBE\u7F6E\u6BCF\u4E2ACPU\u6838\u5FC3\u4E00\u4E2A\u7EBF\u7A0B
server.undertow.threads.io=8
# \u963B\u585E\u4EFB\u52A1\u7EBF\u7A0B\u6C60, \u5F53\u6267\u884C\u7C7B\u4F3Cservlet\u8BF7\u6C42\u963B\u585E\u64CD\u4F5C, undertow\u4F1A\u4ECE\u8FD9\u4E2A\u7EBF\u7A0B\u6C60\u4E2D\u53D6\u5F97\u7EBF\u7A0B,\u5B83\u7684\u503C\u8BBE\u7F6E\u53D6\u51B3\u4E8E\u7CFB\u7EDF\u7684\u8D1F\u8F7D
server.undertow.threads.worker=256

# \u65E5\u5FD7\u914D\u7F6E
#logging.level.com.qmqb.imp=@logging.level@
#logging.level.org.springframework=warn
#logging.config=classpath:logback-spring.xml

# \u7528\u6237\u914D\u7F6E
# \u5BC6\u7801\u6700\u5927\u9519\u8BEF\u6B21\u6570
user.password.maxRetryCount=5
# \u5BC6\u7801\u9501\u5B9A\u65F6\u95F4\uFF08\u9ED8\u8BA410\u5206\u949F\uFF09
user.password.lockTime=10

# Spring\u914D\u7F6E
spring.application.name=${ruoyi.name}
spring.main.allow-bean-definition-overriding=true
# \u56FD\u9645\u5316\u8D44\u6E90\u6587\u4EF6\u8DEF\u5F84
spring.messages.basename=i18n/messages
spring.profiles.active=@profiles.active@
# \u6587\u4EF6\u4E0A\u4F20\u5355\u4E2A\u6587\u4EF6\u5927\u5C0F
spring.servlet.multipart.max-file-size=200MB
# \u8BBE\u7F6E\u603B\u4E0A\u4F20\u7684\u6587\u4EF6\u5927\u5C0F
spring.servlet.multipart.max-request-size=200MB
# \u65E5\u671F\u683C\u5F0F\u5316
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
# \u683C\u5F0F\u5316\u8F93\u51FA
spring.jackson.serialization.indent_output=false
# \u5FFD\u7565\u65E0\u6CD5\u8F6C\u6362\u7684\u5BF9\u8C61
spring.jackson.serialization.fail_on_empty_beans=false
# \u5141\u8BB8\u5BF9\u8C61\u5FFD\u7565json\u4E2D\u4E0D\u5B58\u5728\u7684\u5C5E\u6027
spring.jackson.deserialization.fail_on_unknown_properties=false

# Sa-Token\u914D\u7F6E
# token\u540D\u79F0 (\u540C\u65F6\u4E5F\u662Fcookie\u540D\u79F0)
sa-token.token-name=Authorization
# token\u6709\u6548\u671F \u8BBE\u4E3A\u4E00\u5929 (\u5FC5\u5B9A\u8FC7\u671F) \u5355\u4F4D: \u79D2
sa-token.timeout=86400
# token\u4E34\u65F6\u6709\u6548\u671F (\u6307\u5B9A\u65F6\u95F4\u65E0\u64CD\u4F5C\u5C31\u8FC7\u671F) \u5355\u4F4D: \u79D2
sa-token.activity-timeout=86400
# \u662F\u5426\u5141\u8BB8\u540C\u4E00\u8D26\u53F7\u5E76\u53D1\u767B\u5F55 (\u4E3Atrue\u65F6\u5141\u8BB8\u4E00\u8D77\u767B\u5F55, \u4E3Afalse\u65F6\u65B0\u767B\u5F55\u6324\u6389\u65E7\u767B\u5F55)
sa-token.is-concurrent=true
# \u5728\u591A\u4EBA\u767B\u5F55\u540C\u4E00\u8D26\u53F7\u65F6\uFF0C\u662F\u5426\u5171\u7528\u4E00\u4E2Atoken (\u4E3Atrue\u65F6\u6240\u6709\u767B\u5F55\u5171\u7528\u4E00\u4E2Atoken, \u4E3Afalse\u65F6\u6BCF\u6B21\u767B\u5F55\u65B0\u5EFA\u4E00\u4E2Atoken)
sa-token.is-share=false
# \u662F\u5426\u5C1D\u8BD5\u4ECEheader\u91CC\u8BFB\u53D6token
sa-token.is-read-header=true
# \u662F\u5426\u5C1D\u8BD5\u4ECEcookie\u91CC\u8BFB\u53D6token
sa-token.is-read-cookie=false
# token\u524D\u7F00
sa-token.token-prefix=Bearer
# jwt\u79D8\u94A5
sa-token.jwt-secret-key=cwhazxlkvyllkrfvbbhjtfvcxa

# security\u914D\u7F6E
# \u6392\u9664\u8DEF\u5F84
# \u9759\u6001\u8D44\u6E90
security.excludes.0=/*.html
security.excludes.1=/**/*.html
security.excludes.2=/**/*.css
security.excludes.3=/**/*.js
# swagger \u6587\u6863\u914D\u7F6E
security.excludes.4=/favicon.ico
security.excludes.5=/*/api-docs
security.excludes.6=/*/api-docs/**
security.excludes.7=/error
# swagger-ui \u6587\u6863\u914D\u7F6E
security.excludes.8=/swagger-ui/**
security.excludes.9=/swagger-ui.html
# knife4j \u6587\u6863\u914D\u7F6E
security.excludes.10=/doc.html
security.excludes.11=/webjars/**
security.excludes.12=/swagger-resources/**
security.excludes.13=/v3/api-docs/**
# actuator \u76D1\u63A7\u914D\u7F6E
security.excludes.14=/actuator
security.excludes.15=/actuator/**
# \u63D0\u4F9B\u8C03\u7528\u7684\u63A5\u53E3
security.excludes.16=/openapi/**

# MyBatisPlus\u914D\u7F6E
# \u4E0D\u652F\u6301\u591A\u5305, \u5982\u6709\u9700\u8981\u53EF\u5728\u6CE8\u89E3\u914D\u7F6E \u6216 \u63D0\u5347\u626B\u5305\u7B49\u7EA7
# \u4F8B\u5982 com.**.**.mapper
mybatis-plus.mapperPackage=com.qmqb.imp.**.mapper
# \u5BF9\u5E94\u7684 XML \u6587\u4EF6\u4F4D\u7F6E
mybatis-plus.mapperLocations=classpath*:mapper/**/*Mapper.xml
# \u5B9E\u4F53\u626B\u63CF\uFF0C\u591A\u4E2Apackage\u7528\u9017\u53F7\u6216\u8005\u5206\u53F7\u5206\u9694
mybatis-plus.typeAliasesPackage=com.qmqb.imp.**.domain
# \u542F\u52A8\u65F6\u662F\u5426\u68C0\u67E5 MyBatis XML \u6587\u4EF6\u7684\u5B58\u5728\uFF0C\u9ED8\u8BA4\u4E0D\u68C0\u67E5
mybatis-plus.checkConfigLocation=false
# \u81EA\u52A8\u9A7C\u5CF0\u547D\u540D\u89C4\u5219\uFF08camel case\uFF09\u6620\u5C04
mybatis-plus.configuration.mapUnderscoreToCamelCase=true
# MyBatis \u81EA\u52A8\u6620\u5C04\u7B56\u7565
# NONE\uFF1A\u4E0D\u542F\u7528 PARTIAL\uFF1A\u53EA\u5BF9\u975E\u5D4C\u5957 resultMap \u81EA\u52A8\u6620\u5C04 FULL\uFF1A\u5BF9\u6240\u6709 resultMap \u81EA\u52A8\u6620\u5C04
mybatis-plus.configuration.autoMappingBehavior=PARTIAL
# MyBatis \u81EA\u52A8\u6620\u5C04\u65F6\u672A\u77E5\u5217\u6216\u672A\u77E5\u5C5E\u6027\u5904\u7406\u7B56
# NONE\uFF1A\u4E0D\u505A\u5904\u7406 WARNING\uFF1A\u6253\u5370\u76F8\u5173\u8B66\u544A FAILING\uFF1A\u629B\u51FA\u5F02\u5E38\u548C\u8BE6\u7EC6\u4FE1\u606F
mybatis-plus.configuration.autoMappingUnknownColumnBehavior=NONE
# \u66F4\u8BE6\u7EC6\u7684\u65E5\u5FD7\u8F93\u51FA \u4F1A\u6709\u6027\u80FD\u635F\u8017 org.apache.ibatis.logging.stdout.StdOutImpl
# \u5173\u95ED\u65E5\u5FD7\u8BB0\u5F55 (\u53EF\u5355\u7EAF\u4F7F\u7528 p6spy \u5206\u6790) org.apache.ibatis.logging.nologging.NoLoggingImpl
# \u9ED8\u8BA4\u65E5\u5FD7\u8F93\u51FA org.apache.ibatis.logging.slf4j.Slf4jImpl
mybatis-plus.configuration.logImpl=org.apache.ibatis.logging.nologging.NoLoggingImpl
# \u662F\u5426\u6253\u5370 Logo banner
mybatis-plus.global-config.banner=true
# \u4E3B\u952E\u7C7B\u578B
# AUTO \u81EA\u589E NONE \u7A7A INPUT \u7528\u6237\u8F93\u5165 ASSIGN_ID \u96EA\u82B1 ASSIGN_UUID \u552F\u4E00 UUID
mybatis-plus.global-config.dbConfig.idType=ASSIGN_ID
# \u903B\u8F91\u5DF2\u5220\u9664\u503C
mybatis-plus.global-config.dbConfig.logicDeleteValue=2
# \u903B\u8F91\u672A\u5220\u9664\u503C
mybatis-plus.global-config.dbConfig.logicNotDeleteValue=0
# \u5B57\u6BB5\u9A8C\u8BC1\u7B56\u7565\u4E4B insert,\u5728 insert \u7684\u65F6\u5019\u7684\u5B57\u6BB5\u9A8C\u8BC1\u7B56\u7565
# IGNORED \u5FFD\u7565 NOT_NULL \u975ENULL NOT_EMPTY \u975E\u7A7A DEFAULT \u9ED8\u8BA4 NEVER \u4E0D\u52A0\u5165 SQL
mybatis-plus.global-config.dbConfig.insertStrategy=NOT_NULL
# \u5B57\u6BB5\u9A8C\u8BC1\u7B56\u7565\u4E4B update,\u5728 update \u7684\u65F6\u5019\u7684\u5B57\u6BB5\u9A8C\u8BC1\u7B56\u7565
mybatis-plus.global-config.dbConfig.updateStrategy=NOT_NULL
# \u5B57\u6BB5\u9A8C\u8BC1\u7B56\u7565\u4E4B select,\u5728 select \u7684\u65F6\u5019\u7684\u5B57\u6BB5\u9A8C\u8BC1\u7B56\u7565\u65E2 wrapper \u6839\u636E\u5185\u90E8 entity \u751F\u6210\u7684 where \u6761\u4EF6
mybatis-plus.global-config.dbConfig.strategy=NOT_NULL

# Swagger\u914D\u7F6E
# \u662F\u5426\u5F00\u542Fswagger
swagger.enabled=true
# \u6807\u9898
swagger.info.title=\u6807\u9898\uFF1A${ruoyi.name}\u540E\u53F0\u7BA1\u7406\u7CFB\u7EDF_\u63A5\u53E3\u6587\u6863
# \u63CF\u8FF0
swagger.info.description=\u63CF\u8FF0\uFF1A\u7528\u4E8E\u7BA1\u7406\u96C6\u56E2\u65D7\u4E0B\u516C\u53F8\u7684\u4EBA\u5458\u4FE1\u606F,\u5177\u4F53\u5305\u62ECXXX,XXX\u6A21\u5757...
# \u7248\u672C
swagger.info.version=\u7248\u672C\u53F7: ${imp.version}
# \u4F5C\u8005\u4FE1\u606F
swagger.info.contact.name=Lion Li
swagger.info.contact.email=<EMAIL>
swagger.info.contact.url=https://gitee.com/JavaLionLi/RuoYi-Vue-Plus
# \u9274\u6743\u65B9\u5F0F\u914D\u7F6E
swagger.components.security-schemes.apiKey.type=APIKEY
swagger.components.security-schemes.apiKey.in=HEADER
swagger.components.security-schemes.apiKey.name=${sa-token.token-name}

# springdoc\u914D\u7F6E
# \u8FD9\u91CC\u5B9A\u4E49\u4E86\u4E24\u4E2A\u5206\u7EC4\uFF0C\u53EF\u5B9A\u4E49\u591A\u4E2A\uFF0C\u4E5F\u53EF\u4EE5\u4E0D\u5B9A\u4E49
springdoc.group-configs[0].group=1.\u6F14\u793A\u6A21\u5757
springdoc.group-configs[0].packages-to-scan=com.qmqb.imp.demo
springdoc.group-configs[1].group=2.\u7CFB\u7EDF\u6A21\u5757
springdoc.group-configs[1].packages-to-scan=com.qmqb.imp.web
springdoc.group-configs[2].group=3.\u4EE3\u7801\u751F\u6210\u6A21\u5757
springdoc.group-configs[2].packages-to-scan=com.qmqb.imp.generator

# knife4j\u914D\u7F6E
knife4j.enable=true
knife4j.setting.language=ZH_CN
knife4j.setting.swagger-model-name=\u5B9E\u4F53\u7C7B\u5217\u8868

# \u9632\u6B62XSS\u653B\u51FB
# \u8FC7\u6EE4\u5F00\u5173
xss.enabled=true
# \u6392\u9664\u94FE\u63A5\uFF08\u591A\u4E2A\u7528\u9017\u53F7\u5206\u9694\uFF09
xss.excludes=/system/notice
# \u5339\u914D\u94FE\u63A5
xss.url-patterns=/system/*,/monitor/*,/tool/*

# \u5168\u5C40\u7EBF\u7A0B\u6C60\u76F8\u5173\u914D\u7F6E
# \u662F\u5426\u5F00\u542F\u7EBF\u7A0B\u6C60
thread-pool.enabled=true
# \u961F\u5217\u6700\u5927\u957F\u5EA6
thread-pool.queueCapacity=128
# \u7EBF\u7A0B\u6C60\u7EF4\u62A4\u7EBF\u7A0B\u6240\u5141\u8BB8\u7684\u7A7A\u95F2\u65F6\u95F4
thread-pool.keepAliveSeconds=300

# \u5206\u5E03\u5F0F\u9501 lock4j \u5168\u5C40\u914D\u7F6E
# \u83B7\u53D6\u5206\u5E03\u5F0F\u9501\u8D85\u65F6\u65F6\u95F4\uFF0C\u9ED8\u8BA4\u4E3A 3000 \u6BEB\u79D2
lock4j.acquire-timeout=3000
#\u5206\u5E03\u5F0F\u9501\u7684\u8D85\u65F6\u65F6\u95F4\uFF0C\u9ED8\u8BA4\u4E3A 30 \u79D2
lock4j.expire=30000
#Actuator \u76D1\u63A7\u7AEF\u70B9\u7684\u914D\u7F6E\u9879
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=ALWAYS
management.endpoint.logfile.external-file=./logs/sys-console.log
#\u9489\u9489\u914D\u7F6E\uFF0C\u5168\u6C11\u94B1\u5305\u516C\u53F8\u5E94\u7528
dingtalk.app-key=dingzdbsspnvbrcddwzs
dingtalk.app-secret=ljf0G7Z7_5LOQaJ-hI_45BwfxA3cYb4gW6apoT8Z41BmvKZuP3F2LUH6h6R8Zgjp
#006.\u5185\u90E8\u7BA1\u7406\u7CFB\u7EDF\u53D1\u5E03 \u6A21\u677F\u7F16\u7801
dingtalk.system-release-code=PROC-BC1EC039-13B7-4518-A893-EC72AA7A09FD
#020.\u7CFB\u7EDF\u6545\u969C\u5904\u7406 \u6A21\u677F\u7F16\u7801
dingtalk.system-failure-code=PROC-2B19F911-6BBA-4636-80C6-0B6E7AEC6936
#\u7EC4\u957F\u7FA4\u673A\u5668\u4EBA\u5730\u5740
dingtalk.robot-url=https://oapi.dingtalk.com/robot/send?access_token=d7766aac143224a9fbadbeb2097d60a9fffcd4456a6ea343f27b6e8f7e5c40b5
#\u9879\u7BA1\u7FA4\u673A\u5668\u4EBA\u5730\u5740
dingtalk.pm-robot-url=https://oapi.dingtalk.com/robot/send?access_token=ba38fc23c4f00a37db08935494fd01bddbcabc2ade8a267a953454a1472df224
#\u6280\u672F\u4E2D\u5FC3\u673A\u5668\u4EBA\u5730\u5740
dingtalk.jszx-robot-url=https://oapi.dingtalk.com/robot/send?access_token=173ae4be408aea35be98898ef0e01750a7610c93cd3a2658a8937f46d53e534d
#\u9489\u9489\u53CD\u9988\u5E94\u7528\u914D\u7F6E
dingtalk.feed-back-app-key=dingjwuyk9emoy6rwhry
dingtalk.feed-back-app-secret=tNz-bKU6c_PFkSP9s6nN8Espal0eW4BlpioSGHv8J6LQSsUX_uhjZCL8UFO6tl8F
