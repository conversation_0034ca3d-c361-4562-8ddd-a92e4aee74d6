package com.qmqb.imp.web.controller.business;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.entity.SysDept;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.mapper.SysDeptMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 公共
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/common-biz")
public class CommonBizController {

    private final SysDeptMapper sysDeptMapper;

    /**
     * 部门下拉框
     *
     * @return
     */
    @GetMapping("/deptSelect")
    public R<List<SysDept>> deptSelect(@RequestParam(required = false) String filter) {
        List<SysDept> sysDepts = sysDeptMapper.selectList(Wrappers.lambdaQuery(SysDept.class).eq(SysDept::getParentId, 101L));
        if (StringUtils.isBlank(filter)) {
            return R.ok(sysDepts);
        }
        List<SysDept> vos = sysDepts.stream().filter(sysDept -> StringUtils.contains(sysDept.getDeptName(), filter)).collect(Collectors.toList());
        return R.ok(vos);
    }
}
