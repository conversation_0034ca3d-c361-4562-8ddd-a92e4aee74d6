<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.performance.PerformanceFeedbackMainIndicatorResultMapper">

    <resultMap id="PerformanceFeedbackMainIndicatorResultMap" type="com.qmqb.imp.system.domain.performance.PerformanceFeedbackMainIndicatorResult">
        <id property="id" column="id" />
        <result property="mainId" column="main_id" />
        <result property="indicatorResultId" column="indicator_result_id" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <select id="selectIndicatorResultIdsByMainId" resultType="java.lang.Long">
        SELECT distinct indicator_result_id
        FROM tb_performance_feedback_main_indicator_result
        WHERE main_id = #{mainId}
    </select>

</mapper>
