package com.qmqb.imp.app.service;

import com.qmqb.imp.app.domain.bo.WarnHandleBO;
import com.qmqb.imp.app.domain.bo.WarnListBO;
import com.qmqb.imp.app.domain.vo.WarnDetailVO;
import com.qmqb.imp.common.core.domain.R;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
public interface WarningPageService {

    /**
     * 个人预警列表
     *
     * @param bo
     * @return
     */
    R<List<WarnDetailVO>> list(WarnListBO bo);

    /**
     * 预警处理
     *
     * @param bo
     * @return
     */
    R<Void> handle(WarnHandleBO bo);

    /**
     * 全部预警列表
     *
     * @param bo
     * @return
     */
    R<List<WarnDetailVO>> all(WarnListBO bo);
}
