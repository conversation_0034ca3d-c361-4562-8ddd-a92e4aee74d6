package com.qmqb.imp.job.indicator.indicators.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.WorkStat;
import com.qmqb.imp.system.domain.bo.TrackWorkResultBO;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.ITrackWorkResultService;
import com.qmqb.imp.system.service.IWorkStatService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.YearMonth;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * @className: ZentaoTasksIndicatorServiceImpl
 * @author: yangjiangqian
 * @description:
 * @date: 2025/6/24 17:42
 * @version: 1.0
 */
@Service
public class ZentaoTasksIndicatorServiceImpl implements IndicatorLevelCalcService {
    private static final int S_LEVEL_TASK_COUNT = 30;
    private static final int A_LEVEL_TASK_COUNT = 20;
    private static final int B_LEVEL_TASK_COUNT = 15;
    private static final int D_LEVEL_THRESHOLD = 10;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IWorkStatService workStatService;

    private static final List<LevelRule> LEVEL_RULES = Arrays.asList(
        new LevelRule(S_LEVEL_TASK_COUNT, ScoreLevelEnum.SCORE_S.getCode()),
        new LevelRule(A_LEVEL_TASK_COUNT, ScoreLevelEnum.SCORE_A.getCode()),
        new LevelRule(B_LEVEL_TASK_COUNT, ScoreLevelEnum.SCORE_B.getCode())
    );

    @Override
    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults) {
        int taskCount = Optional.ofNullable(workResult.getAllWorkTaskCount()).orElse(0);

        String level = LEVEL_RULES.stream()
            .filter(rule -> rule.appliesTo(taskCount))
            .findFirst()
            .map(LevelRule::getLevel)
            .orElse(ScoreLevelEnum.SCORE_B.getCode());

        // 检查D级条件：连续2个月任务数小于10
        if (taskCount < D_LEVEL_THRESHOLD) {
            level = ScoreLevelEnum.SCORE_C.getCode();
            Integer year = workResult.getWorkYear();
            Integer month = workResult.getWorkMonth();
            if (year != null && month != null) {
                int prevTaskCount = getPreviousMonthTaskCount(nickName, year, month);
                if (prevTaskCount < D_LEVEL_THRESHOLD) {
                    level = ScoreLevelEnum.SCORE_D.getCode();
                }
            }
        }
        return level;
    }

    @Override
    public String getIndicatorCode() {
        return PerformanceIndicatorEnum.ZENTAO_TASKS.getCode();
    }

    @Override
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level) {
        int taskCount = Optional.ofNullable(workResult.getAllWorkTaskCount()).orElse(0);
        Integer year = workResult.getWorkYear();
        Integer month = workResult.getWorkMonth();

        StringBuilder logContent = new StringBuilder();
        logContent.append(String.format("[%s]在%s年%s月份完成禅道任务数：%d个",
            nickName, year, month, taskCount));

        // 添加评级信息
        logContent.append(String.format("，评级：%s", level));

        // 添加评级原因
        String reason = getRatingReason(taskCount, level);
        if (reason != null) {
            logContent.append(String.format("，原因：%s", reason));
        }
        return logContent.toString();
    }

    /**
     * 获取评级原因
     */
    private String getRatingReason(int taskCount, String level) {
        switch (level) {
            case "S":
                return String.format("任务数量(%d)达到S级标准(≥%d)", taskCount, S_LEVEL_TASK_COUNT);
            case "A":
                return String.format("任务数量(%d)达到A级标准(≥%d)", taskCount, A_LEVEL_TASK_COUNT);
            case "B":
                return String.format("任务数量(%d)达到B级标准(≥%d)", taskCount, B_LEVEL_TASK_COUNT);
            case "C":
                return String.format("任务数量(%d)达到C级标准(<%d)", taskCount, D_LEVEL_THRESHOLD);
            case "D":
                return String.format("连续2个月任务数量均低于%d个", D_LEVEL_THRESHOLD);
            default:
                return null;
        }
    }

    @Data
    private static class LevelRule {
        private final int taskThreshold;
        private final String level;

        public LevelRule(int taskThreshold, String level) {
            this.taskThreshold = taskThreshold;
            this.level = level;
        }

        public boolean appliesTo(int taskCount) {
            return taskCount >= this.taskThreshold;
        }
    }

    private int getPreviousMonthTaskCount(String nickName, int year, int month) {
        YearMonth previousYearMonth = YearMonth.of(year, month).minusMonths(1);
        SysUser user = sysUserService.selectUserByNickName(nickName);
        if (user == null || user.getDeptId() == null) {
            return 0;
        }

        WorkStat workStat = workStatService.queryByUserNameAndYearAndMonth(
            user.getDeptId(),
            nickName,
            previousYearMonth.getYear(),
            previousYearMonth.getMonthValue()
        );

        if (workStat != null && workStat.getWorkDoneClosedTaskCount() != null) {
            return Math.toIntExact(workStat.getWorkDoneClosedTaskCount());
        }

        return 0;
    }
}
