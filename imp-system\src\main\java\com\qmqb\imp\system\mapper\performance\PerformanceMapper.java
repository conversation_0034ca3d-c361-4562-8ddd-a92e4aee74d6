package com.qmqb.imp.system.mapper.performance;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.bo.performance.PerformanceBo;
import com.qmqb.imp.system.domain.dto.PerformanceDTO;
import com.qmqb.imp.system.domain.performance.Performance;
import com.qmqb.imp.system.domain.vo.BranchVo;
import com.qmqb.imp.system.domain.vo.performance.PerformanceVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 绩效点评主Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface PerformanceMapper extends BaseMapperPlus<PerformanceMapper, Performance, PerformanceVo> {

    /**
     * 绩效点评分页
     * @param pageInfo
     * @param performanceBo
     * @return
     */
    Page<PerformanceDTO> selectPerformanceSummary(@Param("page") Page<PerformanceVo> pageInfo, @Param("performanceBo") PerformanceBo performanceBo);
}
