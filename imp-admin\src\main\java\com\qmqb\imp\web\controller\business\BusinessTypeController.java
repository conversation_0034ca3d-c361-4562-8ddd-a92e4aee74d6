package com.qmqb.imp.web.controller.business;

import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;

import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.bo.BusinessTypeBo;
import com.qmqb.imp.system.domain.vo.BusinessTypeVo;
import com.qmqb.imp.system.service.IBusinessTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 业务类型
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/businessType")
public class BusinessTypeController extends BaseController {

    private final IBusinessTypeService iBusinessTypeService;

    /**
     * 查询业务类型列表
     */
    @GetMapping("/list")
    public TableDataInfo<BusinessTypeVo> list(BusinessTypeBo bo, PageQuery pageQuery) {
        return iBusinessTypeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出业务类型列表
     */
    @Log(title = "业务类型", businessType = com.qmqb.imp.common.enums.BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BusinessTypeBo bo, HttpServletResponse response) {
        List<BusinessTypeVo> list = iBusinessTypeService.queryList(bo);
        ExcelUtil.exportExcel(list, "业务类型", BusinessTypeVo.class, response);
    }

    /**
     * 获取业务类型详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<BusinessTypeVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iBusinessTypeService.queryById(id));
    }

    /**
     * 新增业务类型
     */
    @Log(title = "业务类型", businessType = com.qmqb.imp.common.enums.BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BusinessTypeBo bo) {
        return toAjax(iBusinessTypeService.insertByBo(bo));
    }

    /**
     * 修改业务类型
     */
    @Log(title = "业务类型", businessType = com.qmqb.imp.common.enums.BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BusinessTypeBo bo) {
        return toAjax(iBusinessTypeService.updateByBo(bo));
    }

    /**
     * 删除业务类型
     */
    @Log(title = "业务类型", businessType = com.qmqb.imp.common.enums.BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iBusinessTypeService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
