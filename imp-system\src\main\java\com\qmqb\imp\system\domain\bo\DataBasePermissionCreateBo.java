package com.qmqb.imp.system.domain.bo;

import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 数据库权限业务对象 tb_data_base_permission
 *
 * <AUTHOR>
 * @date 2025-06-26
 */

@Data
public class DataBasePermissionCreateBo {


    /**
     * 数据库名
     */
    private String dbName;
    /**
     * 数据库备注
     */
    private String dbRemark;
    /**
     * 组id列表（逗号隔开）
     */
    private String groupIds;
    /**
     * 成员id列表（逗号隔开）
     */
    private String memberIds;
    /**
     * 组名称列表（逗号隔开）
     */
    private String groupNames;

    /**
     * 成员名称列表（逗号隔开）
     */
    private String memberNames;


}
