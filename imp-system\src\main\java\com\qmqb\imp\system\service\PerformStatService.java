package com.qmqb.imp.system.service;

import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.system.domain.vo.WorkTotalVO;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-12
 */
public interface PerformStatService {
    /**
     * 根据年月获取每月各组绩效合计
     *
     * @param doneYear  完成年份
     * @param doneMonth 完成月份
     * @return
     */
    R<List<WorkTotalVO>> getWorkTotal(Integer doneYear, Integer doneMonth);
}
