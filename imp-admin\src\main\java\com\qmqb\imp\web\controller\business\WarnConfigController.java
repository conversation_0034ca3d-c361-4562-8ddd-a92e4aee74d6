package com.qmqb.imp.web.controller.business;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.bo.WarnConfigBo;
import com.qmqb.imp.system.domain.bo.WarnConfigEnableBo;
import com.qmqb.imp.system.domain.vo.WarnConfigVo;
import com.qmqb.imp.system.service.IWarnConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 预警配置
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/warnConfig")
public class WarnConfigController extends BaseController {

    private final IWarnConfigService iWarnConfigService;

    /**
     * 查询预警配置列表
     */
    @SaCheckPermission("business:warnConfig:list")
    @GetMapping("/list")
    public TableDataInfo<WarnConfigVo> list(WarnConfigBo bo, PageQuery pageQuery) {
        return iWarnConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出预警配置列表
     */
    @SaCheckPermission("business:warnConfig:export")
    @Log(title = "预警配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WarnConfigBo bo, HttpServletResponse response) {
        List<WarnConfigVo> list = iWarnConfigService.queryList(bo);
        ExcelUtil.exportExcel(list, "预警配置", WarnConfigVo.class, response);
    }

    /**
     * 获取预警配置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:warnConfig:query")
    @GetMapping("/{id}")
    public R<WarnConfigVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long id) {
        return R.ok(iWarnConfigService.queryById(id));
    }

    /**
     * 新增预警配置
     */
    @SaCheckPermission("business:warnConfig:add")
    @Log(title = "预警配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WarnConfigBo bo) {
        return toAjax(iWarnConfigService.insertByBo(bo));
    }

    /**
     * 修改预警配置
     */
    @SaCheckPermission("business:warnConfig:edit")
    @Log(title = "预警配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WarnConfigBo bo) {
        return toAjax(iWarnConfigService.updateByBo(bo));
    }

    /**
     * 开启预警配置
     */
    @SaCheckPermission("business:warnConfig:edit")
    @Log(title = "开启预警配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/enable")
    public R<Void> enable(@Validated(EditGroup.class) @RequestBody WarnConfigEnableBo bo) {
        return toAjax(iWarnConfigService.enable(bo));
    }

    /**
     * 删除预警配置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:warnConfig:remove")
    @Log(title = "预警配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iWarnConfigService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
