package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目成果表对象 tb_project_result
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_project_result")
public class ProjectResult extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 业务类型ID，关联业务类型管理表主键
     */
    private Long businessTypeId;

    /**
     * 成果编号
     */
    private String resultCode;

    /**
     * 成果类型：1新系统、2重要功能模块、3一般功能模块、4事项支撑、5其它
     */
    private String resultType;

    /**
     * 项目/任务名称，限制30字符
     */
    private String projectTaskName;

    /**
     * 优先级：P1/P2/P3
     */
    private String priorityLevel;

    /**
     * 状态：1未开始、2进行中、3已完成、4已取消
     */
    private String status;

    /**
     * 完成评审时间
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Date milestoneRequirements;

    /**
     * 完成开发时间
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Date milestoneDevelopment;

    /**
     * 完成测试验收时间
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Date milestoneTest;

    /**
     * 完成上线时间
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Date milestoneOnline;

    /**
     * 需求评审进度百分比
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private BigDecimal requirementsProgress;

    /**
     * 开发进度百分比
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private BigDecimal developmentProgress;

    /**
     * 测试验收进度百分比
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private BigDecimal testProgress;

    /**
     * 开发组，多个用逗号分隔
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private String devTeams;

    /**
     * 测试组，多个用逗号分隔
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private String testTeams;

    /**
     * 产品经理(需求创建人)，多个用逗号分隔
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private String productManagers;

    /**
     * 开发投入人力（人）
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Integer devManpower;

    /**
     * 测试投入人力（人）
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Integer testManpower;

    /**
     * 开发工作量（人日）
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private BigDecimal devWorkload;

    /**
     * 测试工作量（人日）
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private BigDecimal testWorkload;

    /**
     * 需求背景
     */
    private String requirementBackground;

    /**
     * 备注信息，最多1000字符
     */
    private String remark;

    /**
     * 负责项目经理，多个用逗号分隔
     */
    private String projectManagers;

    /**
     * 完成时间，仅状态为已完成时可填写
     */
    private Date completionTime;

    /**
     * 归档标志：0未归档、1已归档
     */
    private Integer archiveFlag;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private Integer delFlag;
}
