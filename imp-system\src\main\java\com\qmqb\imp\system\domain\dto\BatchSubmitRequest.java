package com.qmqb.imp.system.domain.dto;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量提交请求对象
 * 用于批量处理主键ID列表的请求
 * 包含主键ID列表
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
public class BatchSubmitRequest {
    @NotEmpty(message = "主键不能为空")
    private List<Long> ids;

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }
}
