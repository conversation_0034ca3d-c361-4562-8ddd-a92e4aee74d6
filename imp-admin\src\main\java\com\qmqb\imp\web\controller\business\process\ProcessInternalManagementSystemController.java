package com.qmqb.imp.web.controller.business.process;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.bo.process.ProcessInternalManagementSystemBo;
import com.qmqb.imp.system.domain.vo.process.ProcessInternalManagementSystemVo;
import com.qmqb.imp.system.service.process.IProcessInternalManagementSystemService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 内部管理系统功能需求
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/processInternalManagementSystem")
public class ProcessInternalManagementSystemController extends BaseController {

    private final IProcessInternalManagementSystemService iProcessInternalManagementSystemService;

    /**
     * 查询内部管理系统功能需求列表
     */
    @SaCheckPermission("system:processInternalManagementSystem:list")
    @GetMapping("/list")
    public TableDataInfo<ProcessInternalManagementSystemVo> list(ProcessInternalManagementSystemBo bo, PageQuery pageQuery) {
        return iProcessInternalManagementSystemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出内部管理系统功能需求列表
     */
    @SaCheckPermission("system:processInternalManagementSystem:export")
    @Log(title = "内部管理系统功能需求", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProcessInternalManagementSystemBo bo, HttpServletResponse response) {
        List<ProcessInternalManagementSystemVo> list = iProcessInternalManagementSystemService.queryList(bo);
        ExcelUtil.exportExcel(list, "内部管理系统功能需求", ProcessInternalManagementSystemVo.class, response);
    }

    /**
     * 获取内部管理系统功能需求详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:processInternalManagementSystem:query")
    @GetMapping("/{id}")
    public R<ProcessInternalManagementSystemVo> getInfo(@NotNull(message = "主键不能为空")
                                                        @PathVariable Long id) {
        return R.ok(iProcessInternalManagementSystemService.queryById(id));
    }

    /**
     * 新增内部管理系统功能需求
     */
    @SaCheckPermission("system:processInternalManagementSystem:add")
    @Log(title = "内部管理系统功能需求", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProcessInternalManagementSystemBo bo) {
        return toAjax(iProcessInternalManagementSystemService.insertByBo(bo));
    }

    /**
     * 修改内部管理系统功能需求
     */
    @SaCheckPermission("system:processInternalManagementSystem:edit")
    @Log(title = "内部管理系统功能需求", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProcessInternalManagementSystemBo bo) {
        return toAjax(iProcessInternalManagementSystemService.updateByBo(bo));
    }

    /**
     * 删除内部管理系统功能需求
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:processInternalManagementSystem:remove")
    @Log(title = "内部管理系统功能需求", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iProcessInternalManagementSystemService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
