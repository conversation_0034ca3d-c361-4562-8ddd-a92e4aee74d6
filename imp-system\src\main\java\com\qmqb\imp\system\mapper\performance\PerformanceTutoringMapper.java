package com.qmqb.imp.system.mapper.performance;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.bo.performance.PerformanceTutoringQueryBo;
import com.qmqb.imp.system.domain.performance.PerformanceTutoring;
import com.qmqb.imp.system.domain.vo.performance.PerformanceTutoringVo;
import org.apache.ibatis.annotations.Param;

/**
 * 绩效辅导Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface PerformanceTutoringMapper extends BaseMapperPlus<PerformanceTutoringMapper, PerformanceTutoring, PerformanceTutoringVo> {

    /**
     * 查询绩效辅导列表
     * @param page
     * @param bo
     * @return
     */
    Page<PerformanceTutoringVo> selectTutoringPage(@Param("page") Page<Object> page,@Param("bo") PerformanceTutoringQueryBo bo);

    /**
     * 获取当前登录人的最后一条编辑记录
     * @param username
     * @return
     */
    PerformanceTutoringVo getLastEditTotur(@Param("username") String username);
}
