create table tb_release_records
(
    id                      bigint unsigned auto_increment comment '主键ID'
        primary key,
    result_code             varchar(50)         default ''                not null comment '成果编号',
    result_type             varchar(2)          default ''                null comment '成果类型：1新系统、2重要功能模块、3一般功能模块、4事项支撑、5其它',
    data_source             varchar(2)          default '1'               not null comment '数据来源：1禅道同步、2手工添加',
    business_category_major varchar(2)          default ''                null comment '所属业务大类：1国内、2海外',
    business_category_minor varchar(2)          default ''                null comment '所属业务小类：1风控、2营销、3资金、4资产、5贷后、6自营业务、7综合、8其它',
    duration_days           int unsigned        default 0                 null comment '耗时天数',
    manpower_cost           int unsigned        default 0                 null comment '耗费人力',
    dev_depts               varchar(512)        default ''                null comment '主要开发组id,多个用逗号分隔',
    test_depts              varchar(512)        default ''                null comment '测试组id,多个用逗号分隔',
    other_depts             varchar(255)        default ''                null comment '其它组id,多个用逗号分隔',
    result_title            varchar(500)        default ''                null comment '成果标题',
    result_summary          text                                          null comment '成果简介,取发布单关联的所有需求名称，多个需求则进行分行展示',
    project_manager         varchar(50)         default ''                not null comment '负责项目经理',
    start_time              datetime                                      null comment '开始时间',
    end_time                datetime                                      null comment '结束时间',
    source_release_id       mediumint unsigned  default null              null comment '关联zt_release表ID',
    created_by              varchar(30)         default ''                not null comment '创建人',
    created_time            datetime            default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by              varchar(30)         default ''                not null comment '更新人',
    updated_time            datetime            default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag                tinyint(4) unsigned default 0                 null comment '删除标志（0代表存在 2代表删除）',
    constraint idx_result_code_uindex
        unique (result_code),
    constraint idx_source_release_id_uindex
        unique (source_release_id)
)
    comment '发布版本记录表' charset = utf8mb4;

create index idx_business_major
    on tb_release_records (business_category_major);

create index idx_business_minor
    on tb_release_records (business_category_minor);

create index idx_created_time
    on tb_release_records (del_flag, created_time);

create index idx_project_manager
    on tb_release_records (project_manager);

create index idx_result_type
    on tb_release_records (result_type);

create index idx_start_time
    on tb_release_records (del_flag, start_time, end_time);
-- 绩效指标表
CREATE TABLE `tb_performance_indicator` (
                                            `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                            `performance_id` bigint(20) NOT NULL COMMENT '绩效主表ID',
                                            `indicator_code` varchar(32) NOT NULL COMMENT '指标编码',
                                            `indicator_name` varchar(64) NOT NULL COMMENT '指标名称',
                                            `category_code` varchar(50) DEFAULT NULL COMMENT '指标分类编码(work_achievement/work_quality/collaboration_ability)',
                                            `score_level` char(1) NOT NULL COMMENT '评价等级S/A/B/C/D',
                                            `log_content` varchar(2048) DEFAULT NULL COMMENT '日志登记内容',
                                            `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                            `create_time` datetime NOT NULL COMMENT '创建时间',
                                            `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                            `update_time` datetime NOT NULL COMMENT '更新时间',
                                            PRIMARY KEY (`id`),
                                            KEY `idx_performance_id` (`performance_id`),
                                            KEY `idx_category_code` (`category_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1945312774106644487 DEFAULT CHARSET=utf8mb4 COMMENT='绩效指标表';

-- 绩效反馈主表与绩效指标结果中间表
CREATE TABLE `tb_performance_feedback_main_indicator_result` (
                                                                 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                                 `main_id` bigint(20) NOT NULL COMMENT '绩效反馈主表ID',
                                                                 `indicator_result_id` bigint(20) NOT NULL COMMENT '绩效指标结果ID',
                                                                 `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                                                 `create_time` datetime NOT NULL COMMENT '创建时间',
                                                                 `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                                                 `update_time` datetime NOT NULL COMMENT '更新时间',
                                                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1945314420614234114 DEFAULT CHARSET=utf8 COMMENT='绩效反馈主表与绩效指标结果中间表';

-- 绩效反馈主表
CREATE TABLE `tb_performance_feedback_main` (
                                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                `feedback_code` varchar(50) NOT NULL COMMENT '反馈编码',
                                                `year` int(4) NOT NULL COMMENT '年份',
                                                `month` int(2) NOT NULL COMMENT '月份',
                                                `feedback_time` datetime NOT NULL COMMENT '反馈时间',
                                                `primary_indicator` varchar(50) DEFAULT NULL COMMENT '一类指标',
                                                `secondary_indicator` varchar(50) DEFAULT NULL COMMENT '二类指标',
                                                `event_title` varchar(200) DEFAULT NULL COMMENT '事件标题',
                                                `event_detail` text COMMENT '事件明细',
                                                `event_start_time` datetime DEFAULT NULL COMMENT '事件发生时间-开始',
                                                `event_end_time` datetime DEFAULT NULL COMMENT '事件发生时间-结束',
                                                `data_source` varchar(20) DEFAULT NULL COMMENT '数据来源',
                                                `submit_status` varchar(20) DEFAULT NULL COMMENT '提交状态',
                                                `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
                                                `submitter` varchar(100) DEFAULT NULL COMMENT '提交人',
                                                `project_manager_audit_status` varchar(20) DEFAULT NULL COMMENT '项管审核状态',
                                                `project_manager_auditor` varchar(100) DEFAULT NULL COMMENT '项管审核人',
                                                `project_manager_audit_time` datetime DEFAULT NULL COMMENT '项管审核时间',
                                                `project_manager_remark` varchar(500) DEFAULT NULL COMMENT '项管审核备注',
                                                `final_audit` varchar(20) DEFAULT NULL COMMENT '最终审核状态',
                                                `final_audit_time` datetime DEFAULT NULL COMMENT '最终审核时间',
                                                `final_remark` varchar(500) DEFAULT NULL COMMENT '最终审核备注',
                                                `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                                `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                                `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                PRIMARY KEY (`id`),
                                                UNIQUE KEY `uk_feedback_code` (`feedback_code`),
                                                KEY `idx_primary_indicator` (`primary_indicator`),
                                                KEY `idx_secondary_indicator` (`secondary_indicator`),
                                                KEY `idx_data_source` (`data_source`)
) ENGINE=InnoDB AUTO_INCREMENT=1945314487047815171 DEFAULT CHARSET=utf8mb4 COMMENT='绩效反馈主表';

-- 绩效反馈表
CREATE TABLE `tb_performance_feedback` (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                           `main_feedback_id` bigint(20) NOT NULL COMMENT '主反馈表ID（关联主反馈表）',
                                           `group_id` bigint(20) DEFAULT NULL COMMENT '所属组ID',
                                           `group_name` varchar(20) DEFAULT NULL COMMENT '所属组',
                                           `primary_indicator` varchar(100) DEFAULT NULL COMMENT '一类指标',
                                           `secondary_indicator` varchar(100) DEFAULT NULL COMMENT '二类指标',
                                           `event_title` varchar(255) DEFAULT NULL COMMENT '事件标题',
                                           `event_detail` text COMMENT '事件明细',
                                           `recommended_level` varchar(10) DEFAULT NULL COMMENT '推荐绩效级别',
                                           `recommended_reason` text COMMENT '推荐原因',
                                           `nick_name` varchar(50) NOT NULL COMMENT '员工昵称',
                                           `year` int(4) NOT NULL COMMENT '绩效年份',
                                           `month` int(2) NOT NULL COMMENT '绩效月份',
                                           `person_type` varchar(50) DEFAULT NULL COMMENT '角色类型',
                                           `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                           `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                           `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                           `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                           PRIMARY KEY (`id`),
                                           KEY `idx_main_feedback_id` (`main_feedback_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1945314487127506951 DEFAULT CHARSET=utf8mb4 COMMENT='绩效反馈表';

-- 绩效点评主表
CREATE TABLE `tb_performance` (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                  `nick_name` varchar(64) NOT NULL COMMENT '员工昵称',
                                  `year` int(4) NOT NULL COMMENT '绩效年份',
                                  `month` int(2) NOT NULL COMMENT '绩效月份，格式MM',
                                  `group_id` bigint(20) DEFAULT NULL COMMENT '所属组ID',
                                  `group_name` varchar(20) DEFAULT NULL COMMENT '所属组',
                                  `role_id` bigint(20) DEFAULT NULL COMMENT '角色ID',
                                  `role` varchar(20) DEFAULT NULL COMMENT '角色',
                                  `total_level` char(1) DEFAULT NULL COMMENT '总评等级S/A/B/C/D',
                                  `review_level` char(1) DEFAULT NULL COMMENT '评审绩效等级S/A/B/C/D',
                                  `final_level` char(1) DEFAULT NULL COMMENT '最终绩效等级S/A/B/C/D',
                                  `approval_time` datetime DEFAULT NULL COMMENT '核准时间',
                                  `email_sent_flag` char(1) DEFAULT '0' COMMENT '是否邮件报送，0为不发，1为发送',
                                  `email_sent_time` datetime DEFAULT NULL COMMENT '邮件报送时间',
                                  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
                                  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                  `create_time` datetime NOT NULL COMMENT '创建时间',
                                  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                  `update_time` datetime NOT NULL COMMENT '更新时间',
                                  PRIMARY KEY (`id`),
                                  KEY `idx_employee_month` (`nick_name`,`month`)
) ENGINE=InnoDB AUTO_INCREMENT=1945312774052118531 DEFAULT CHARSET=utf8mb4 COMMENT='绩效点评主表';

CREATE TABLE `tb_performance_indicator_category` (
                                                     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                                     `performance_id` bigint(20) NOT NULL COMMENT '绩效主表ID',
                                                     `category_code` varchar(50) NOT NULL COMMENT '分类编码(work_achievement/work_quality/collaboration_ability)',
                                                     `category_name` varchar(100) NOT NULL COMMENT '分类名称(工作成果类/工作质量类/协作能力类)',
                                                     `category_level` varchar(10) NOT NULL COMMENT '分类评分等级S/A/B/C/D',
                                                     `category_weight` int(11) DEFAULT NULL COMMENT '分类权重（已废弃，保留字段）',
                                                     `category_score` decimal(5,2) DEFAULT NULL COMMENT '分类得分（A级数量*25）',
                                                     `category_log` text COMMENT '分类计算日志',
                                                     `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                     `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                     `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                                     `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                                     `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                                     PRIMARY KEY (`id`),
                                                     KEY `idx_performance_id` (`performance_id`),
                                                     KEY `idx_category_code` (`category_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1945312774131810308 DEFAULT CHARSET=utf8mb4 COMMENT='绩效指标分类表';

-- 绩效指标原因表
CREATE TABLE `tb_performance_indicator_result` (
                                                   `id` bigint(20) NOT NULL,
                                                   `first_indecator` varchar(30) NOT NULL COMMENT '一类指标',
                                                   `second_indecator` varchar(30) NOT NULL COMMENT '二类指标',
                                                   `level` varchar(10) NOT NULL COMMENT '绩效级别(S/A/B/C/D)',
                                                   `result` varchar(255) NOT NULL COMMENT '推荐原因',
                                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                                   `create_by` varchar(30) DEFAULT NULL COMMENT '创建人',
                                                   `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                                   `update_by` varchar(30) DEFAULT NULL COMMENT '更新人',
                                                   `del_flag` tinyint(4) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='绩效指标原因';

-- 绩效辅导表
CREATE TABLE `tb_performance_tutoring` (
                                           `id` bigint(20) NOT NULL COMMENT '主键',
                                           `feedback_id` bigint(20) NOT NULL COMMENT '绩效反馈表id(tb_performance_feedback)',
                                           `tutoring_summary` text COMMENT '辅导概要',
                                           `tutoring_result` text COMMENT '辅导结果',
                                           `tutor` varchar(20) DEFAULT NULL COMMENT '辅导人',
                                           `tutoring_time` datetime DEFAULT NULL COMMENT '辅导时间',
                                           `tutoring_attachment` varchar(255) DEFAULT NULL COMMENT '辅导附件',
                                           `director_suggest` text COMMENT '总监建议',
                                           `suggest_time` datetime DEFAULT NULL COMMENT '建议时间',
                                           `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                           `create_time` datetime NOT NULL COMMENT '创建时间',
                                           `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                           `update_time` datetime NOT NULL COMMENT '更新时间',
                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='绩效辅导表';



-- 项目成果管理字典管理

INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940228618905505794, '项目成果管理-成果类型', 'project_outcome_types', '0', 'admin', '2025-07-02 09:58:45', 'admin', '2025-07-02 09:58:45', NULL);

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940228762585583617, 1, '新系统', '1', 'project_outcome_types', NULL, 'default', 'N', '0', 'admin', '2025-07-02 09:59:19', 'admin', '2025-07-02 09:59:19', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940228824875192322, 2, '重要功能模块', '2', 'project_outcome_types', NULL, 'default', 'N', '0', 'admin', '2025-07-02 09:59:34', 'admin', '2025-07-02 09:59:40', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940228899009515522, 3, '一般功能模块', '3', 'project_outcome_types', NULL, 'default', 'N', '0', 'admin', '2025-07-02 09:59:52', 'admin', '2025-07-02 09:59:52', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940228950108721154, 4, '事项支撑', '4', 'project_outcome_types', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:00:04', 'admin', '2025-07-02 10:00:04', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940228998053810178, 5, '其它', '5', 'project_outcome_types', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:00:16', 'admin', '2025-07-02 10:00:16', NULL);

INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940230459378683905, '项目成果管理-所属业务大类', 'project_outcome_business_category_major', '0', 'admin', '2025-07-02 10:06:04', 'admin', '2025-07-02 10:06:04', NULL);

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940230685829156866, 1, '国内', '1', 'project_outcome_business_category_major', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:06:58', 'admin', '2025-07-02 10:06:58', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940230734382419970, 2, '海外', '2', 'project_outcome_business_category_major', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:07:09', 'admin', '2025-07-02 10:07:09', NULL);

INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940231307831857153, '项目成果管理-所属业务小类', 'project_outcome_business_category_minor', '0', 'admin', '2025-07-02 10:09:26', 'admin', '2025-07-02 10:11:59', NULL);

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940231488073682946, 1, '风控', '1', 'project_outcome_business_category_minor', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:10:09', 'admin', '2025-07-02 10:10:09', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940231526296375297, 2, '营销', '2', 'project_outcome_business_category_minor', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:10:18', 'admin', '2025-07-02 10:10:18', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940231570303012866, 3, '资金', '3', 'project_outcome_business_category_minor', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:10:29', 'admin', '2025-07-02 10:10:29', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940231615253368833, 4, '资产', '4', 'project_outcome_business_category_minor', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:10:39', 'admin', '2025-07-02 10:10:39', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940231659553607681, 5, '贷后', '5', 'project_outcome_business_category_minor', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:10:50', 'admin', '2025-07-02 10:10:50', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940231707704217601, 6, '自营业务', '6', 'project_outcome_business_category_minor', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:11:02', 'admin', '2025-07-02 10:11:02', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940231745675251714, 7, '综合', '7', 'project_outcome_business_category_minor', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:11:11', 'admin', '2025-07-02 10:11:11', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940231784334151682, 8, '其它', '8', 'project_outcome_business_category_minor', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:11:20', 'admin', '2025-07-02 10:11:20', NULL);

INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940233499783516162, '项目成果管理-负责项目经理', 'project_outcome_project_manager', '0', 'admin', '2025-07-02 10:18:09', 'admin', '2025-07-02 10:18:09', NULL);

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940234193999548418, 1, '李崇高', 'lichonggao', 'project_outcome_project_manager', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:20:54', 'admin', '2025-07-02 10:20:54', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940235557504864258, 2, '郑静霞', 'zhengjingxia', 'project_outcome_project_manager', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:26:19', 'admin', '2025-07-02 10:26:19', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940235619802861570, 3, '胡嘉鑫', 'hujiaxin', 'project_outcome_project_manager', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:26:34', 'admin', '2025-07-02 10:26:34', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940235658860220417, 4, '黄金媛', 'huangjinyuan', 'project_outcome_project_manager', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:26:44', 'admin', '2025-07-02 10:26:44', NULL);

INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940236082291986434, '项目成果管理-数据来源', 'project_outcome_data_sources', '0', 'admin', '2025-07-02 10:28:25', 'admin', '2025-07-02 10:28:25', NULL);

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940236225657491457, 1, '禅道同步', '1', 'project_outcome_data_sources', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:28:59', 'admin', '2025-07-02 10:28:59', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940236274621796354, 2, '手工添加', '2', 'project_outcome_data_sources', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:29:10', 'admin', '2025-07-02 10:29:10', NULL);

INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940236701572583425, '项目成果管理-创建人', 'project_outcome_creator', '0', 'admin', '2025-07-02 10:30:52', 'admin', '2025-07-02 10:30:52', NULL);

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940236895802413057, 1, '李海洋', 'lihaiyang', 'project_outcome_creator', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:31:38', 'admin', '2025-07-02 10:31:38', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940236944863186945, 2, '李崇高', 'lichonggao', 'project_outcome_creator', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:31:50', 'admin', '2025-07-02 10:31:50', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940237001800863745, 3, '郑静霞', 'zhengjingxia', 'project_outcome_creator', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:32:04', 'admin', '2025-07-02 10:32:04', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940237047741075457, 4, '胡嘉鑫', 'hujiaxin', 'project_outcome_creator', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:32:15', 'admin', '2025-07-02 10:32:15', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940237097540046849, 5, '黄金媛', 'huangjinyuan', 'project_outcome_creator', NULL, 'default', 'N', '0', 'admin', '2025-07-02 10:32:27', 'admin', '2025-07-02 10:32:27', NULL);

INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940305036272300033, '项目成果管理-开发组', 'project_outcome_dev_dept', '0', 'admin', '2025-07-02 15:02:24', 'admin', '2025-07-02 15:02:24', NULL);

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940305858456547329, 1, '风控开发组', '103', 'project_outcome_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:05:40', 'admin', '2025-07-02 15:05:40', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940305928904077314, 2, 'Android开发组', '106', 'project_outcome_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:05:57', 'admin', '2025-07-02 15:05:57', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940305994909839362, 3, '创新开发组', '107', 'project_outcome_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:06:13', 'admin', '2025-07-02 15:06:13', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940306108491591681, 4, '催收组', '108', 'project_outcome_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:06:40', 'admin', '2025-07-02 15:06:40', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940306698437226498, 5, '前端开发组', '110', 'project_outcome_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:09:01', 'admin', '2025-07-02 15:09:01', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940306754959667202, 6, '资金开发组', '111', 'project_outcome_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:09:14', 'admin', '2025-07-02 15:09:14', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940306917178568705, 7, '架构开发组', '114', 'project_outcome_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:09:53', 'admin', '2025-07-02 15:09:53', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940307093662298114, 8, '资产开发一组', '115', 'project_outcome_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:10:35', 'admin', '2025-07-02 15:10:35', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940307212583399425, 9, '财务开发一组', '116', 'project_outcome_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:11:03', 'admin', '2025-07-02 15:11:03', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940307276169048065, 10, '资产运营开发组', '117', 'project_outcome_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:11:18', 'admin', '2025-07-02 15:11:18', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940307333031227394, 11, 'IOS开发组', '119', 'project_outcome_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:11:32', 'admin', '2025-07-02 15:11:32', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940307425251389441, 12, '财务开发二组', '1782659977081524226', 'project_outcome_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:11:54', 'admin', '2025-07-02 15:11:54', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940307537163808770, 13, '资产开发二组', '1830780594219999233', 'project_outcome_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:12:21', 'admin', '2025-07-02 15:12:21', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940307635776090114, 14, '工具开发组', '1843829274434977794', 'project_outcome_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:12:44', 'admin', '2025-07-02 15:12:44', NULL);

INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940305182280216578, '项目成果管理-测试组', 'project_outcome_test_dept', '0', 'admin', '2025-07-02 15:02:59', 'admin', '2025-07-02 15:02:59', NULL);

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940307868815814657, 1, '风控运营测试组', '109', 'project_outcome_test_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:13:40', 'admin', '2025-07-02 15:13:40', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940307929163460610, 2, '助贷测试一组', '118', 'project_outcome_test_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:13:54', 'admin', '2025-07-02 15:13:54', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940307980724039682, 3, '海外测试组', '120', 'project_outcome_test_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:14:06', 'admin', '2025-07-02 15:14:06', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940308048734679042, 4, '助贷测试二组', '1830780365290692610', 'project_outcome_test_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:14:23', 'admin', '2025-07-02 15:14:23', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940308114820132865, 5, '贷后测试组', '1830899936018391041', 'project_outcome_test_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:14:38', 'admin', '2025-07-02 15:14:38', NULL);

INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940305386240831489, '项目成果管理-其它组', 'project_outcome_other_dept', '0', 'admin', '2025-07-02 15:03:48', 'admin', '2025-07-02 15:03:48', NULL);

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940308294101463042, 1, '运维组', '104', 'project_outcome_other_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:15:21', 'admin', '2025-07-02 15:15:21', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940308349508218882, 2, '数据管理组', '105', 'project_outcome_other_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:15:34', 'admin', '2025-07-02 15:15:34', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940308444253351938, 3, 'BI组', '113', 'project_outcome_other_dept', NULL, 'default', 'N', '0', 'admin', '2025-07-02 15:15:57', 'admin', '2025-07-02 15:15:57', NULL);

-- 绩效审核、绩效提交状态字典
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`,`update_time`, `remark`)VALUES (1940582476395651074, '绩效反馈审核状态', 'performance_feedback_audit_status', '0', 'admin','2025-07-03 09:24:51', 'admin', '2025-07-03 09:24:51', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)VALUES (1940582618817437697, '绩效反馈提交状态', 'performance_feedback_submit_status', '0', 'admin','2025-07-03 09:25:25', 'admin', '2025-07-03 09:25:25', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`,`update_time`, `remark`)VALUES (1940587844500791298, '绩效反馈的数据来源', 'performance_feedback_data_source', '0', 'admin','2025-07-03 09:46:11', 'admin', '2025-07-03 09:46:11', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`,`update_time`, `remark`)VALUES (1940588770766692354, '绩效等级', 'performance_level', '0', 'admin', '2025-07-03 09:49:52', 'admin','2025-07-03 09:49:52', NULL);

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`,`list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`,`update_time`, `remark`)VALUES (1940582761021120513, 1, '未审核', 'NOT_AUDITED', 'performance_feedback_audit_status', NULL, 'default', 'N', '0','admin', '2025-07-03 09:25:59', 'admin', '2025-07-03 09:25:59', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`,`list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`,`update_time`, `remark`)VALUES (1940582807699529730, 2, '同意', 'APPROVED', 'performance_feedback_audit_status', NULL, 'default', 'N', '0','admin', '2025-07-03 09:26:10', 'admin', '2025-07-03 09:26:10', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`,`list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`,`update_time`, `remark`)VALUES (1940582851244793857, 3, '拒绝', 'REJECTED', 'performance_feedback_audit_status', NULL, 'default', 'N', '0','admin', '2025-07-03 09:26:21', 'admin', '2025-07-03 09:26:21', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`,`list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`,`update_time`, `remark`)VALUES (1940582939505532929, 1, '未提交', 'NOT_SUBMITTED', 'performance_feedback_submit_status', NULL, 'default', 'N','0', 'admin', '2025-07-03 09:26:42', 'admin', '2025-07-03 09:26:42', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)VALUES (1940582981213691905, 2, '已提交', 'SUBMITTED', 'performance_feedback_submit_status', NULL, 'default', 'N', '0','admin', '2025-07-03 09:26:52', 'admin', '2025-07-03 09:26:52', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`,`list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`,`update_time`, `remark`)VALUES (1940583017154682881, 3, '待重提', 'PENDING_RESUBMIT', 'performance_feedback_submit_status', NULL, 'default','N', '0', 'admin', '2025-07-03 09:27:00', 'admin', '2025-07-03 09:27:00', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`,`list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`,`update_time`, `remark`)VALUES (1940588854296256513, 1, '手工添加', 'MANUAL_ADD', 'performance_feedback_data_source', NULL, 'default', 'N', '0','admin', '2025-07-03 09:50:12', 'admin', '2025-07-03 09:50:12', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`,`list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`,`update_time`, `remark`)VALUES (1940588894800650242, 2, '定时任务', 'SCHEDULED_TASK', 'performance_feedback_data_source', NULL, 'default', 'N','1', 'admin', '2025-07-03 09:50:22', 'admin', '2025-07-03 09:50:22', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`,`list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`,`update_time`, `remark`)VALUES (1940588931819577345, 3, '系统产生', 'SYSTEM_GENERATED', 'performance_feedback_data_source', NULL, 'default','N', '0', 'admin', '2025-07-03 09:50:30', 'admin', '2025-07-03 09:50:30', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)VALUES (1940588970910490626, 1, 'S', 'S', 'performance_level', NULL, 'default', 'N', '0', 'admin','2025-07-03 09:50:40', 'admin', '2025-07-03 09:50:40', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`,`list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`,`update_time`, `remark`)VALUES (1940588989973602305, 2, 'A', 'A', 'performance_level', NULL, 'default', 'N', '0', 'admin','2025-07-03 09:50:44', 'admin', '2025-07-03 09:50:44', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`,`list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`,`update_time`, `remark`)VALUES (1940589014170542081, 3, 'B', 'B', 'performance_level', NULL, 'default', 'N', '0', 'admin','2025-07-03 09:50:50', 'admin', '2025-07-03 09:50:50', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`,`list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`,`update_time`, `remark`)VALUES (1940589043392258050, 3, 'C', 'C', 'performance_level', NULL, 'default', 'N', '0', 'admin','2025-07-03 09:50:57', 'admin', '2025-07-03 09:50:57', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`,`list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`,`update_time`, `remark`)VALUES (1940589081954689026, 4, 'D', 'D', 'performance_level', NULL, 'default', 'N', '0', 'admin','2025-07-03 09:51:06', 'admin', '2025-07-03 09:51:06', NULL);

INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940711821437157377, '绩效点评邮件发送人', 'performance_email_to_list', '0', 'admin', '2025-07-03 17:58:50', 'admin', '2025-07-03 17:58:50', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940712305363369986, '绩效点评邮件抄送人', 'performance_email_cc_list', '0', 'admin', '2025-07-03 18:00:45', 'admin', '2025-07-03 18:00:45', NULL);

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940712048671965186, 0, '李海洋', '<EMAIL>', 'performance_email_to_list', NULL, 'default', 'N', '0', 'admin', '2025-07-03 17:59:44', 'admin', '2025-07-03 17:59:44', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1940712527225274369, 0, '付洁', '<EMAIL>', 'performance_email_cc_list', NULL, 'default', 'N', '0', 'admin', '2025-07-03 18:01:38', 'admin', '2025-07-03 18:01:38', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1944670655713939457, 7, '运维人员', '7', 'person_type', NULL, 'default', 'N', '0', 'admin', '2025-07-14 16:09:49', 'admin', '2025-07-14 16:09:49', NULL);

-- 绩效反馈管理菜单、按钮权限

-- 菜单权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1939932590323122177, '绩效反馈管理', 1600327964042485761, 12, 'FeedbackManagement', 'business/feedbackManagement/index', NULL, 1, 0, 'C', '0', '0', NULL, 'log', 'admin', '2025-07-01 14:22:26', 'admin', '2025-07-01 14:22:26', '');


-- 查询权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)VALUES (1941324689187315713, '查询', 1939932590323122177, '1', '#', '', 1, 0, 'F', '0', '0', 'system:performanceFeedbackMain:query', '#', 'admin', sysdate(), '', null, '');
-- 列表权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)VALUES (1941324689187315714, '列表', 1939932590323122177, '2', '#', '', 1, 0, 'F', '0', '0', 'system:performanceFeedbackMain:list', '#', 'admin', sysdate(), '', null, '');
-- 导出权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)VALUES (1941324689187315715, '导出', 1939932590323122177, '3', '#', '', 1, 0, 'F', '0', '0', 'system:performanceFeedbackMain:export', '#', 'admin', sysdate(), '', null, '');
-- 新增权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)VALUES (1941324689187315716, '新增', 1939932590323122177, '4', '#', '', 1, 0, 'F', '0', '0', 'system:performanceFeedbackMain:add', '#', 'admin', sysdate(), '', null, '');
-- 修改权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)VALUES (1941324689187315717, '修改', 1939932590323122177, '5', '#', '', 1, 0, 'F', '0', '0', 'system:performanceFeedbackMain:edit', '#', 'admin', sysdate(), '', null, '');
-- 删除权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)VALUES (1941324689187315718, '删除', 1939932590323122177, '6', '#', '', 1, 0, 'F', '0', '0', 'system:performanceFeedbackMain:remove', '#', 'admin', sysdate(), '', null, '');
-- 项管审核权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)VALUES (1941324689187315719, '项管审核', 1939932590323122177, '7', '#', '', 1, 0, 'F', '0', '0', 'system:performanceFeedbackMain:projectManagerAudit', '#', 'admin', sysdate(), '', null, '');
-- 批量提交权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)VALUES (1941324689187315720, '批量提交', 1939932590323122177, '8', '#', '', 1, 0, 'F', '0', '0', 'system:performanceFeedbackMain:batchSubmit', '#', 'admin', sysdate(), '', null, '');
-- 批量最终审核权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)VALUES (1941324689187315721, '批量最终审核', 1939932590323122177, '9', '#', '', 1, 0, 'F', '0', '0', 'system:performanceFeedbackMain:batchFinalAudit', '#', 'admin', sysdate(), '', null, '');

-- 菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1939931677164421121, '绩效反馈明细查询', 1600327964042485761, 11, 'FeedbackDetails', 'business/feedbackDetails/index', NULL, 1, 0, 'C', '0', '0', NULL, 'tree-table', 'admin', '2025-07-01 14:18:49', 'admin', '2025-07-01 14:18:49', '');

-- 按钮 SQL
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)values(1941329663715004417, '分页查询绩效反馈明细', 1939931677164421121, '1',  '#', '', 1, 0, 'F', '0', '0', 'system:performanceFeedback:list',        '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)values(1941329663715004418, '查询绩效反馈明细列表', 1939931677164421121, '2',  '#', '', 1, 0, 'F', '0', '0', 'system:performanceFeedback:all',        '#', 'admin', sysdate(), '', null, '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)values(1941329663715004419, '获取绩效反馈明细详细信息', 1939931677164421121, '3',  '#', '', 1, 0, 'F', '0', '0', 'system:performanceFeedback:query',        '#', 'admin', sysdate(), '', null, '');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1941329663715004420, '获取全部用户', 100, 8, '', '', '', 1, 0, 'F', '0', '0', 'system:user:all', '#', 'admin', sysdate(), '', NULL, '');

-- 绩效指标菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1939892726898737154, '绩效指标管理', 1, 12, 'system/performanceIndicatorManagement', 'system/performanceIndicatorManagement/index', '', 1, 0, 'C', '0', '0', 'system:performIndicatorResult:list', 'build', 'admin', '2025-07-01 11:44:02', 'admin', '2025-07-04 15:24:22', '');
-- 绩效辅导菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1939972669749936129, '绩效辅导管理', 1600327964042485761, 10, 'business/performanceGuidanceManagement', 'business/performanceGuidanceManagement', NULL, 1, 0, 'C', '0', '0', 'system:performanceTutoring:list', 'skill', 'admin', '2025-07-01 17:01:42', 'admin', '2025-07-04 17:04:38', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1939973270508486657, '绩效辅导', 1939972669749936129, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:performanceTutoring:tutoring', '#', 'admin', '2025-07-01 17:04:05', 'admin', '2025-07-03 10:12:55', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1939973385088483330, '总监建议', 1939972669749936129, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:performanceTutoring:suggest', '#', 'admin', '2025-07-01 17:04:33', 'admin', '2025-07-03 10:13:11', '');



-- 系统功能异常报障处理流程表新增字段
ALTER TABLE `tb_process_system_alarm`
    ADD COLUMN `manager_confirm` VARCHAR(20) DEFAULT NULL COMMENT '项目经理确认',
ADD COLUMN `test_confirm` VARCHAR(20) DEFAULT NULL COMMENT '测试确认',
ADD COLUMN `dev_repair` VARCHAR(20) DEFAULT NULL COMMENT '开发修复';

-- 增加早退字段
ALTER TABLE `tb_user_kq_stat` ADD COLUMN `kq_leave_early_count` int(11) NULL DEFAULT 0 COMMENT '早退次数' AFTER `kq_yz_late_count`;
-- 增加唯一索引防止数据重复
ALTER TABLE `tb_performance_registration` ADD UNIQUE INDEX `uniq_year_month_name_del`(`user_name`, `eval_year`, `eval_month`, `del_flag`) USING BTREE;
