package com.qmqb.imp.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.qmqb.imp.common.annotation.ExcelDictFormat;
import com.qmqb.imp.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 预警事件和部门关联视图对象 tb_monitor_event_dept
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Data
@ExcelIgnoreUnannotated
public class MonitorEventDeptVo {

    private static final long serialVersionUID = 1L;

    /**
     * 事件id
     */
    @ExcelProperty(value = "事件id")
    private Long eventId;

    /**
     * 部门id
     */
    @ExcelProperty(value = "部门id")
    private Long deptId;


}
