package com.qmqb.imp.app.domain.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <p>
 * 提交点评业务对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SubmitEvaluateBO extends PerformanceEvaluateBizBO {

    /**
     * 点评内容
     */
    @NotEmpty(message = "点评内容不能为空")
    @Length(max = 300, message = "字数请控制在300以内")
    private String content;
}
