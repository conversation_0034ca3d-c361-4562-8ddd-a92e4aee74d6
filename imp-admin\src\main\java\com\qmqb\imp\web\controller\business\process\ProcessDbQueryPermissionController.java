package com.qmqb.imp.web.controller.business.process;


import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.system.domain.vo.process.ProcessDbQueryPermissionVo;
import com.qmqb.imp.system.domain.bo.process.ProcessDbQueryPermissionBo;
import com.qmqb.imp.system.service.process.IProcessDbQueryPermissionService;
import com.qmqb.imp.common.core.page.TableDataInfo;

/**
 * 生产数据库查询权限申请
 *
 * <AUTHOR>
 * @date 2024-12-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/processDbQueryPermission")
public class ProcessDbQueryPermissionController extends BaseController {

    private final IProcessDbQueryPermissionService iProcessDbQueryPermissionService;

    /**
     * 查询生产数据库查询权限申请列表
     */
    @SaCheckPermission("system:processDbQueryPermission:list")
    @GetMapping("/list")
    public TableDataInfo<ProcessDbQueryPermissionVo> list(ProcessDbQueryPermissionBo bo, PageQuery pageQuery) {
        return iProcessDbQueryPermissionService.queryPageList(bo, pageQuery);
    }


}
