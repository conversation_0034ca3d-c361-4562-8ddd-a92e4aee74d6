package com.qmqb.imp.system.service.indicator;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceBo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceLevelBo;
import com.qmqb.imp.system.domain.dto.PerformanceDTO;
import com.qmqb.imp.system.domain.performance.Performance;
import com.qmqb.imp.system.domain.performance.PerformanceIndicator;
import com.qmqb.imp.system.domain.performance.PerformanceIndicatorCategory;
import com.qmqb.imp.system.domain.vo.performance.PerformanceVo;

import java.util.Collection;
import java.util.List;

/**
 * 绩效点评主Service接口
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface IPerformanceService extends IService<Performance> {

    /**
     * 查询绩效点评主
     *
     * @param id 绩效点评主ID
     * @return 绩效点评主信息
     */
    PerformanceVo queryById(Long id);

    /**
     * 查询绩效点评主列表（分页）
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 绩效点评主分页列表
     */
    TableDataInfo<PerformanceDTO> queryPageList(PerformanceBo bo, PageQuery pageQuery);

    /**
     * 查询绩效点评主列表
     *
     * @param bo 查询条件
     * @return 绩效点评主列表
     */
    List<PerformanceVo> queryList(PerformanceBo bo);

    /**
     * 新增绩效点评主
     *
     * @param bo 新增对象
     * @return 是否成功
     */
    Boolean insertByBo(PerformanceBo bo);

    /**
     * 修改绩效点评主
     *
     * @param bo 修改对象
     * @return 是否成功
     */
    Boolean updateByBo(PerformanceBo bo);

    /**
     * 校验并批量删除绩效点评主信息
     *
     * @param ids     待删除ID集合
     * @param isValid 是否校验
     * @return 是否成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 删除同nickName同month的记录
     *
     * @param nickName 员工昵称
     * @param month    月份
     * @param year    年
     */
    void removeByNickNameAndMonth(String nickName, Integer month, Integer year);

    /**
     * 删除同年月和角色类型的记录
     *
     * @param year       年份
     * @param month      月份
     * @param personType 角色类型 0非组长，1组长
     * @param deptId
     */
    void removeByYearAndMonth(Integer year, Integer month, Integer personType, Long deptId);

    /**
     * 根据员工昵称和月份获取绩效记录
     *
     * @param nickName 员工昵称
     * @param year     年份
     * @param month    月份
     * @return 绩效记录
     */
    Performance getByNickNameAndMonth(String nickName, Integer year, Integer month);

    /**
     * 根据年份和月份获取绩效记录列表
     *
     * @param year  年份
     * @param month 月份
     * @return 绩效记录列表
     */
    List<Performance> listByYearAndMonth(Integer year, Integer month);

    /**
     * 计算总评等级
     *
     * @param indicators 绩效指标列表
     * @return 总评等级
     */
    String calculateTotalLevel(List<PerformanceIndicator> indicators);

    /**
     * 基于分类计算总评等级
     *
     * @param categories 绩效指标分类列表
     * @return 总评等级
     */
    String calculateTotalLevelByCategories(List<PerformanceIndicatorCategory> categories);



    /**
     * 评审等级
     *
     * @param performanceLevelBo 绩效点评入参
     * @return 总评等级
     */
    Boolean reviewLevel(PerformanceLevelBo performanceLevelBo);

    /**
     * 评审最终等级
     *
     * @param performanceLevelBo 绩效点评入参
     * @return 总评等级
     */
    Boolean approvalLevel(PerformanceLevelBo performanceLevelBo);


    /**
     * 发送邮件
     *
     * @param ids ids
     * @return 总评等级
     */
    Boolean sendEmail(List<Long> ids);
}
