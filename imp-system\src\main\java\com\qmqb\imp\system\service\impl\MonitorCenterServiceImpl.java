package com.qmqb.imp.system.service.impl;

import com.qmqb.imp.common.config.MonitorCenterProperties;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.constant.HttpStatus;
import com.qmqb.imp.common.exception.ServiceException;
import com.qmqb.imp.system.service.MonitorCenterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 监控中心服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MonitorCenterServiceImpl implements MonitorCenterService {

    private final RestTemplate restTemplate;
    private final MonitorCenterProperties monitorCenterProperties;

    /**
     * 处理预警事件
     *
     * @param eventId 事件ID
     * @param status 状态：1-受理，2-处理
     * @param result 处理结果
     * @return 是否成功
     */
    @Override
    public boolean processAlertEvent(Long eventId, Integer status, String result) {
        try {
            // 根据状态选择不同的接口
            if (status == CommConstants.CommonVal.ONE) {
                // 受理事件 - 调用processing接口
                return callProcessingApi(eventId);
            } else if (status == CommConstants.CommonVal.TWO) {
                // 处理事件 - 调用finish接口
                return callFinishApi(eventId, result);
            } else {
                log.error("不支持的状态值：{}", status);
                return false;
            }
        } catch (Exception e) {
            log.error("调用监控中心API异常，事件ID：{}，状态：{}", eventId, status, e);
            throw new ServiceException("调用监控中心API异常：" + e.getMessage());
        }
    }

    /**
     * 调用受理事件接口
     *
     * @param eventId 事件ID
     * @return 是否成功
     */
    private boolean callProcessingApi(Long eventId) {
        String url = monitorCenterProperties.getApiUrl() + "/alertEvent/processing";

        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        // 构建请求体
        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
        requestBody.add("eventId", String.valueOf(eventId));

        // 创建请求实体
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);

        // 发送请求
        ResponseEntity<Map<String, Object>> responseEntity = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                new ParameterizedTypeReference<Map<String, Object>>() {}
        );

        return handleResponse(responseEntity, eventId, 1);
    }

    /**
     * 调用完成事件接口
     *
     * @param eventId 事件ID
     * @param result 处理结果
     * @return 是否成功
     */
    private boolean callFinishApi(Long eventId, String result) {
        String url = monitorCenterProperties.getApiUrl() + "/alertEvent/finish";

        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        // 构建请求体
        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
        requestBody.add("eventId", String.valueOf(eventId));
        requestBody.add("result", result);

        // 创建请求实体
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);

        // 发送请求
        ResponseEntity<Map<String, Object>> responseEntity = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                new ParameterizedTypeReference<Map<String, Object>>() {}
        );

        return handleResponse(responseEntity, eventId, 2);
    }

    /**
     * 处理API响应
     *
     * @param responseEntity 响应实体
     * @param eventId 事件ID
     * @param status 状态
     * @return 是否成功
     */
    private boolean handleResponse(ResponseEntity<Map<String, Object>> responseEntity, Long eventId, Integer status) {
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            Map<String, Object> responseBody = responseEntity.getBody();
            String code="code";
            if (responseBody != null &&  CommConstants.CommonValStr.ZERO.equals(String.valueOf(responseBody.get(code)))) {
                log.info("调用监控中心API成功，事件ID：{}，状态：{}", eventId, status);
                return true;
            } else {
                log.error("调用监控中心API失败，事件ID：{}，状态：{}，响应：{}", eventId, status, responseBody);
                throw new ServiceException("调用监控中心API失败,事件ID：" + eventId + "，状态：" + status + "，响应：" + responseBody);
            }
        } else {
            log.error("调用监控中心API失败，事件ID：{}，状态：{}，HTTP状态码：{}", eventId, status, responseEntity.getStatusCodeValue());
            throw new ServiceException("调用监控中心API失败,事件ID：" + eventId + "，状态：" + status + "，HTTP状态码：" + responseEntity.getStatusCodeValue());
        }
    }
}
