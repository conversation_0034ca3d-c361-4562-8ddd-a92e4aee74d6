package com.qmqb.imp.common.constant;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public interface Constants {

    /**
     * UTF-8 字符集
     */
    String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    String GBK = "GBK";

    /**
     * www主域
     */
    String WWW = "www.";

    /**
     * http请求
     */
    String HTTP = "http://";

    /**
     * https请求
     */
    String HTTPS = "https://";

    /**
     * 通用成功标识
     */
    String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    String FAIL = "1";

    /**
     * 登录成功
     */
    String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    String LOGOUT = "Logout";

    /**
     * 注册
     */
    String REGISTER = "Register";

    /**
     * 登录失败
     */
    String LOGIN_FAIL = "Error";

    /**
     * 验证码有效期（分钟）
     */
    Integer CAPTCHA_EXPIRATION = 2;

    /**
     * 令牌
     */
    String TOKEN = "token";

    /**
     * 值班提醒
     */
    String REMINDER = "温馨提示：您将于{num}天后（{time}）值班，打卡时间是早上6:30~7:30和晚上11:30~12:30，请按时打卡。";

    /**
     * 组员值班提醒
     */
    String REMINDER_LEADER = "温馨提示：您的组员“{name}”将于{num}天后（{time}）值班，打卡时间是早上6:30~7:30和晚上11:30~12:30，请按时打卡。";

    /**
     * 假期值班提醒
     */
    String REMINDER_ON_VACATION = "温馨提示：您将于{num}天后（{time}）值班，打卡时间是早上6：30~7：30和晚上11：30~12：30，系统检测到您在（{time}）已请假，请提前协调其它人员帮助值班并按时打卡。";

    /**
     * 每天加班超过9点半预警内容 {nameList}（风控开发组）张三、（风控测试组）李四
     */
    String DAY_OVERTIME_TAG = "【日考勤统计】昨天（{time}）加班到21点30之后组员名单：{nameList}，请相关组长同步加班原因。";

    /**
     * 每天加班正常预警内容
     */
    String DAY_NORMAL_TAG = "【日考勤统计】昨天（{time}）完美考勤！";

    /**
     * 每周加班次数超过四次预警内容 {nameList}（风控开发组）张三、（风控测试组）李四
     */
    String WEEK_OVERTIME_TAG = "【周考勤统计】上周（{time}）加班次数超过4次的组员名单：{nameList} ，请相关组长同步加班原因。";

    /**
     * 每周加班正常预警内容
     */
    String WEEK_NORMAL_TAG = "【周考勤统计】上周（{time}）完美考勤！";

    /**
     * 组内工时差距超出阈值预警
     */
    String WORK_HOUR_GAP_TAG = "【工时预警】目前统计工时相差大于40的组名单：{groupList}！";
    /**
     * 组内工时正常预警
     */
    String WORK_HOUR_NORMAL_TAG = "【工时预警】目前统计工时正常！";

    /**
     * 代码质量问题预警
     */
    String CODE_QUALITY_WARN_TAG = "目前统计代码问题待处理情况如下：\n{groupList}\n请以上组尽快安排人员到【绩效系统-代码管理-代码质量管理】进行处理，谢谢！";

    /**
     * 慢sql问题预警
     */
    String SLOW_SQL_WARN_TAG = "目前统计暂未处理的生产慢SQL如下：\n{groupList}\n请负责以上数据库的各组尽快安排人员到【绩效系统-工作管理-生产慢SQL管理】进行处理，谢谢！";
    /**
     * 慢sql问题未指派预警
     */
    String SLOW_SQL_NOT_ASSIGN_WARN_TAG = "一周内慢SQL处理情况统计：\n{content}";
    /**
     * 昨日及以前有未提交状态绩效事件的组长名单
     */
    String NOT_SUBMIT_PERFORMANCE_FEEDBACK = "昨日已创建绩效事件但未提交的组长名单：{yesterdayNotSubmitLeaders}\n以前已创建绩效事件但未提交的组长名单：{beforeNotSubmitLeaders}";

    /**
     * 连续5个工作日以上没有提交任何绩效反馈的组长名单
     */
    String NOT_SUBMIT_PERFORMANCE_FEEDBACK_MORE_THAN_FIVE_DAYS = "连续5个工作日以上没有提交任何绩效事件的组长名单：{users}";

    /**
     * 超过2周只提交S或A指标，未提交C或D反馈的组长名单
     */
    String ONLY_SUBMIT_S_OR_A_MORE_THAN_TWO_WEEKS = "超过2周只提交S或A，未提交C或D等级事件的组长名单：{leaders}";

    /**
     * 有C或D绩效，超过3个工作日未填写绩效辅导的组长名单
     */
    String NOT_SUBMIT_PERFORMANCE_COACHING = "有C或D绩效，超过3个工作日未填写绩效辅导的组长名单：{leaders}";

    /**
     * 连续2个工作日没有新增的项目成果
     */
    String NOT_ADD_PROJECT_OUTCOMES_MORE_THAN_TWO_DAYS = "连续2个工作日没有新增项目成果的项目经理名单：{managers}";

    /**
     * 连续5个工作日以上没有提交任何绩效反馈
     */
    String NOT_SUBMIT_PERFORMANCE_FEEDBACK_MORE_THAN_FIVE_DAYS_MANAGER = "连续5个工作日以上没有提交任何绩效事件的项目经理名单：{managers}";

    /**
     * 超过2周只提交S或A，未提交C或D反馈的项目经理名单
     */
    String ONLY_SUBMIT_S_OR_A_MORE_THAN_TWO_WEEKS_MANAGER = "超过2周只提交S或A，未提交C或D等级事件的项目经理名单：{managers}";

    /**
     * 任务状态超时预警
     */
    String TASK_STATUS_TIMEOUT_TAG = "请大家关注禅道中的如下任务情况并处理：{content}";

    /**
     * 任务状态未超时预警
     */
    String TASK_STATUS_NOT_TIMEOUT_TAG = "目前禅道任务状态一切正常，太棒啦！";

    /**
     * 案例为0的需求数
     */
    String NO_CASE_STORY_COUNT = "请所有测试人员注意：\n" +
        "近2个月内，有{count}条需求的用例数为0，请确保所有需求都有测试用例并在禅道系统中正确关联，如果未处理，将进行绩效扣分处理。";

    /**
     * 需求案例正常
     */
    String STORY_CASE_NORMAL = "目前暂无测试用例为0的需求，太棒啦！";

    /**
     * 代码库信息缺失预警
     */
    String PROJECT_INFO_LACK_TAG = "请以下代码库相关组长到绩效系统代码库管理模块中请完善【负责开发组】、【负责测试组】、【业务大类】、【业务小类】四项要素：{content}";

    /**
     * 项目成果信息缺失预警
     */
    String PROJECT_RESULT_INFO_LACK_TAG = "目前绩效系统有{count}个项目成果没有填写【项目里程碑】【任务进度】【干系人】【投入人力】【工作量】等信息，请抓紧补充信息。";

    String MINUS_ONE = "-1";


}

