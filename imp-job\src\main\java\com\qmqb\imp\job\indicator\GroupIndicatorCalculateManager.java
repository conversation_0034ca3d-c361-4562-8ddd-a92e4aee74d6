package com.qmqb.imp.job.indicator;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.PerformanceFeedbackDataSourceEnum;
import com.qmqb.imp.common.enums.PerformanceFeedbackSubmitStatusEnum;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.system.domain.UserKqStat;
import com.qmqb.imp.system.domain.performance.Performance;
import com.qmqb.imp.system.domain.performance.PerformanceFeedbackMain;
import com.qmqb.imp.system.domain.performance.PerformanceIndicator;
import com.qmqb.imp.system.mapper.UserKqStatMapper;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.indicator.IPerformanceFeedbackMainService;
import com.qmqb.imp.system.service.indicator.IPerformanceIndicatorService;
import com.qmqb.imp.system.service.indicator.IPerformanceService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-07-09 16:19
 * 组长二级指标统一计算逻辑
 */
@Service
public class GroupIndicatorCalculateManager {

    @Autowired
    private IPerformanceFeedbackMainService performanceFeedbackMainService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IPerformanceService performanceService;
    @Autowired
    private IPerformanceIndicatorService performanceIndicatorService;

    @Resource
    UserKqStatMapper userKqStatMapper;

    private static double ratio = 50.0;


    public String caculateIndicator(Integer year, Integer month, String performanceIndicator,  SysUser manager) {
        // 获取需要统计指标的数据
        CaculateDate caculateDate = setCaculateDate(year, month, performanceIndicator, manager);
        // 计算指标逻辑
        return calculateLevel(caculateDate,performanceIndicator,manager);
    }

    /**
     * 计算指标
     * @param caculateDate
     * @param performanceIndicator
     * @param manager
     * @return
     */
    public String calculateLevel(CaculateDate caculateDate, String performanceIndicator,  SysUser manager) {
        Long dCount = caculateDate.getLevelCountMap().getOrDefault(ScoreLevelEnum.SCORE_D.getCode(), 0L);
        Boolean dLevel = dCount > CommConstants.CommonVal.ZERO ||
            (caculateDate.getCurrentRegisterCount() == CommConstants.CommonVal.ZERO &&
                caculateDate.getLastRegisterCount() == CommConstants.CommonVal.ZERO);
        Long cLevelCount = caculateDate.getLevelCountMap().getOrDefault(ScoreLevelEnum.SCORE_C.getCode(), 0L);
        double cRatio = cLevelCount * 100.0 / caculateDate.getTeamMembers().size();
        Boolean cLevelFlag = cRatio >= ratio || caculateDate.getCurrentRegisterCount() == CommConstants.CommonVal.ZERO;

        //特殊处理：如果是工作规范遵守情况，开发经理无需填写
        if (performanceIndicator.equals(PerformanceIndicatorEnum.DEV_SPEC.getCode()) && manager.getDept().getDeptType() == CommConstants.CommonVal.ONE ) {
            if (dCount > CommConstants.CommonVal.ZERO) {
                return ScoreLevelEnum.SCORE_D.getCode();
            } else if (cRatio >= ratio) {
                return ScoreLevelEnum.SCORE_C.getCode();
            }
        } else {
            if (dLevel) {
                return ScoreLevelEnum.SCORE_D.getCode();
            } else if (cLevelFlag) {
                return ScoreLevelEnum.SCORE_C.getCode();
            }
        }

        if (caculateDate.getTeamMembers().size() >= CommConstants.CommonVal.FOUR) {
            return calculateLevelForBigTeam(caculateDate);
        } else {
            return calculateLevelForSmallTeam(caculateDate);
        }
    }

    public CaculateDate setCaculateDate(Integer year, Integer month, String performanceIndicator,  SysUser manager) {
        CaculateDate caculateDate = new CaculateDate();
        //1.获取经理和组员信息
        caculateDate.setManager(manager);
        List<SysUser> teamList = sysUserService.selectUserByDeptId(manager.getDeptId());
        // 排除请假人员
        List<String> leaveMembers = userKqStatMapper.selectList(new LambdaQueryWrapper<UserKqStat>()
            .eq(UserKqStat::getKqYear, year)
            .eq(UserKqStat::getKqMonth, month)
            .eq(UserKqStat::getKqAttendanceDays, 0))
            .stream().map(UserKqStat::getKqUserName).collect(Collectors.toList());
        teamList = teamList.stream().filter(user -> !leaveMembers.contains(user.getNickName())).collect(Collectors.toList());
        caculateDate.setTeamMembers(teamList);
        //2.获取本月和上个月绩效指标登记数据
        YearMonth currentYearMonth = YearMonth.of(year, month);
        YearMonth lastYearMonth = currentYearMonth.minusMonths(1);
        List<PerformanceFeedbackMain> currentRegisterList = registerIndicator(manager.getUserName(), currentYearMonth,performanceIndicator);
        List<PerformanceFeedbackMain> lastRegisterList = registerIndicator(manager.getUserName(), lastYearMonth,performanceIndicator);
        if (!currentRegisterList.isEmpty()) {
            caculateDate.setCurrentRegisterCount(currentRegisterList.size());
        }
        if (!lastRegisterList.isEmpty()) {
            caculateDate.setLastRegisterCount(lastRegisterList.size());
        }
        //3.获取各个成员等级的个数
        getMenberLevelCount(year, month, performanceIndicator, manager,caculateDate);

        return caculateDate;
    }

    /**
     * 根据用户名称和时间获取绩效指标登记
     * @param username
     * @param yearMonth
     * @return
     */
    public List<PerformanceFeedbackMain> registerIndicator(String username, YearMonth yearMonth, String performanceIndicator) {
        LocalDate startDate = yearMonth.atDay(1);
        LocalDate endDate = yearMonth.atEndOfMonth();
        String startTime = startDate.toString() + " 00:00:00";
        String endTime = endDate.toString() + " 23:59:59";
        List<PerformanceFeedbackMain> feedbackMainList =  performanceFeedbackMainService.lambdaQuery().eq(PerformanceFeedbackMain::getSubmitter, username)
            .between(PerformanceFeedbackMain::getSubmitTime, startTime, endTime)
            .eq(PerformanceFeedbackMain::getDataSource, PerformanceFeedbackDataSourceEnum.MANUAL_ADD.getCode())
            .eq(PerformanceFeedbackMain::getSecondaryIndicator, performanceIndicator)
            .and(wapper -> wapper
                .eq(PerformanceFeedbackMain::getSubmitStatus, PerformanceFeedbackSubmitStatusEnum.SUBMITTED)
                .or()
                .eq(PerformanceFeedbackMain::getSubmitStatus, PerformanceFeedbackSubmitStatusEnum.PENDING_RESUBMIT))
            .list();
        return feedbackMainList;
    }

    /**
     * 获取各个成员等级的个数
     * @param year
     * @param month
     * @param performanceIndicator
     * @param manager
     * @return
     */
    public void getMenberLevelCount(Integer year, Integer month, String performanceIndicator,  SysUser manager, CaculateDate caculateDate) {
        List<Performance> performanceList =  performanceService.list(new LambdaQueryWrapper<Performance>()
            .eq(Performance::getYear, year)
            .eq(Performance::getMonth, month)
            .eq(Performance::getGroupId, manager.getDeptId()));

        List<Long> performanceIds = performanceList.stream().map(Performance::getId).collect(Collectors.toList());
        Map<Long, String> performanceMap =  performanceList.stream().collect(Collectors.toMap(Performance::getId, Performance::getNickName));

        if (performanceIds.isEmpty()) {
            return ;
        }
        List<PerformanceIndicator> indicators = performanceIndicatorService.lambdaQuery()
            .in(PerformanceIndicator::getPerformanceId, performanceIds)
            .eq(PerformanceIndicator::getIndicatorCode, performanceIndicator)
            .list();
        Map<String, Long> levelCountMap = indicators.stream().collect(Collectors.groupingBy(
            PerformanceIndicator::getScoreLevel,
            Collectors.counting()
        ));
        caculateDate.setLevelCountMap(levelCountMap);

        Map<String, String> memberLevelMap = new HashMap<>(16);
        for (PerformanceIndicator indicator : indicators) {
            String nickName = performanceMap.getOrDefault(indicator.getPerformanceId(), "");
            memberLevelMap.put(nickName, indicator.getScoreLevel());
        }
        caculateDate.setMemberLevelMap(memberLevelMap);
    }

    /**
     * 计算小于4人团队的组长绩效（统计比例不含组长）
     * @param caculateDate
     * @return
     */
    private String calculateLevelForSmallTeam(CaculateDate caculateDate) {
        Long sLevelCount = caculateDate.getLevelCountMap().getOrDefault(ScoreLevelEnum.SCORE_S.getCode(), 0L);
        Long aLevelCount = caculateDate.getLevelCountMap().getOrDefault(ScoreLevelEnum.SCORE_A.getCode(), 0L);

        //排除组长的组员人数
        int memberSize = caculateDate.getTeamMembers().size() - 1;

        Boolean sLevelFlag = (sLevelCount == memberSize);
        Boolean aLevelFlagFlag = (sLevelCount + aLevelCount) == memberSize;

        if (sLevelFlag) {
            return ScoreLevelEnum.SCORE_S.getCode();
        } else if (aLevelFlagFlag) {
            return ScoreLevelEnum.SCORE_A.getCode();
        }
        return ScoreLevelEnum.SCORE_B.getCode();

    }

    /**
     * 计算大于4人团队的组长绩效（统计比例含组长）
     * @param caculateDate
     * @return
     */
    private String calculateLevelForBigTeam(CaculateDate caculateDate) {
        Long sLevelCount = caculateDate.getLevelCountMap().getOrDefault(ScoreLevelEnum.SCORE_S.getCode(), 0L);
        Long aLevelCount = caculateDate.getLevelCountMap().getOrDefault(ScoreLevelEnum.SCORE_A.getCode(), 0L);

        // 精确计算比例（避免整数除法问题）
        double sRatio = sLevelCount * 100.0 / caculateDate.getTeamMembers().size();
        double saRatio = (sLevelCount + aLevelCount) * 100.0 / caculateDate.getTeamMembers().size();

        // S级：超过半数（>50%）为S级
        Boolean sLevelFlag = (sRatio >= ratio);
        // A级：超过半数（>50%）为S+A级
        Boolean aLevelFlag = (saRatio >= ratio);

        if (sLevelFlag) {
            return ScoreLevelEnum.SCORE_S.getCode();
        } else if (aLevelFlag) {
            return ScoreLevelEnum.SCORE_A.getCode();
        }

        return ScoreLevelEnum.SCORE_B.getCode();

    }


    @Data
    public static class CaculateDate {
        //经理
        private SysUser manager;
        //本组成员
        List<SysUser> teamMembers;
        //本月绩效指标登记数据
        private Integer currentRegisterCount = 0;
        //上个月绩效指标登记数据
        private Integer lastRegisterCount = 0;
        //成员的等级个数
        private Map<String,Long> levelCountMap  = new HashMap<>();;
        //成员对应的等级
        private Map<String,String> memberLevelMap  = new HashMap<>();;

    }

    /**
     * 获取评级原因
     * @param caculateDate 计算数据
     * @param level 当前等级
     * @return 评级原因
     */
    public String getRatingReason(CaculateDate caculateDate, String level) {
        int teamSize = caculateDate.getTeamMembers().size();
        Long dCount = caculateDate.getLevelCountMap().getOrDefault(ScoreLevelEnum.SCORE_D.getCode(), 0L);
        Long cCount = caculateDate.getLevelCountMap().getOrDefault(ScoreLevelEnum.SCORE_C.getCode(), 0L);
        Long bCount = caculateDate.getLevelCountMap().getOrDefault(ScoreLevelEnum.SCORE_B.getCode(), 0L);
        Long aCount = caculateDate.getLevelCountMap().getOrDefault(ScoreLevelEnum.SCORE_A.getCode(), 0L);
        Long sCount = caculateDate.getLevelCountMap().getOrDefault(ScoreLevelEnum.SCORE_S.getCode(), 0L);
        switch (level) {
            case "S":
                if (teamSize >= CommConstants.CommonVal.FOUR) {
                    return String.format("组内%d人(含组长)中,组员S级共%d个(满足半数以上达到S级)",
                        teamSize, sCount);
                } else {
                    return String.format("组内%d人(含组长)中,组内%d个组员全部达到S级", teamSize,sCount);
                }
            case "A":
                if (teamSize >= CommConstants.CommonVal.FOUR) {
                    return String.format("组内%d人(含组长)中，组员S+A级共%d个(满足半数以上达到A级或以上)",
                        teamSize, sCount + aCount);
                } else {
                    return String.format("组内%d人(含组长)中,组内%d个组员全部达到A级或以上", teamSize,sCount + aCount);
                }
            case "B":
                return String.format("组内等级分布：S=%d, A=%d, B=%d", sCount, aCount, bCount);
            case "C":
                if (caculateDate.getCurrentRegisterCount() == 0) {
                    return "本月本指标无任何绩效事件登记";
                } else {
                    return String.format("组内%d人(含组长)中，组员C级共%d个(C级人数占一半以上)",
                        teamSize, cCount);
                }
            case "D":
                if (dCount > 0) {
                    return String.format("组内有%d个组员得D级", dCount);
                } else if (caculateDate.getCurrentRegisterCount() == 0 &&
                    caculateDate.getLastRegisterCount() == 0) {
                    return "连续2个月本指标无任何绩效事件登记";
                }
                return "组内绩效情况不达标";
            default:
                return "未知评级原因";
        }
    }

}
