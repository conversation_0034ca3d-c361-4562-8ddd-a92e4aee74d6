package com.qmqb.imp.job.indicator.indicators.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.WorkStat;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.mapper.WorkStatMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 月执行用例数指标等级计算
 * @date 2025/7/2 14:37
 */
@Service
public class CaseExecuteCountIndicatorServiceImpl implements IndicatorLevelCalcService {

    @Resource
    private WorkStatMapper workStatMapper;

    private static final int S_EXEC_CASE_COUNT = 100;
    private static final int A_EXEC_CASE_COUNT = 70;
    private static final int B_EXEC_CASE_COUNT = 40;
    private static final int C_EXEC_CASE_COUNT = 10;

    @Override
    public String getIndicatorCode() {
        return PerformanceIndicatorEnum.CASE_EXECUTE_COUNT.getCode();
    }

    @Override
    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults) {
        int workCaseCount = Optional.ofNullable(workResult.getWorkCaseCount()).orElse(0);
        if (workCaseCount >= S_EXEC_CASE_COUNT) {
            return ScoreLevelEnum.SCORE_S.getCode();
        } else if (workCaseCount >= A_EXEC_CASE_COUNT) {
            return ScoreLevelEnum.SCORE_A.getCode();
        } else if (workCaseCount >= B_EXEC_CASE_COUNT) {
            return ScoreLevelEnum.SCORE_B.getCode();
        } else if (workCaseCount < C_EXEC_CASE_COUNT) {
            LocalDate localDate = LocalDate.of(workResult.getWorkYear(), workResult.getWorkMonth(), 1)
                .plusMonths(-1);
            WorkStat lastMonthWorkStat = workStatMapper.selectOne(new LambdaQueryWrapper<WorkStat>()
                .eq(WorkStat::getWorkYear, localDate.getYear())
                .eq(WorkStat::getWorkMonth, localDate.getMonth())
                .eq(WorkStat::getWorkUsername, workResult.getWorkUsername())
                .last("limit 1"));
            if (Optional.ofNullable(lastMonthWorkStat).map(WorkStat::getWorkCaseCount).orElse(0L) < C_EXEC_CASE_COUNT) {
                return ScoreLevelEnum.SCORE_D.getCode();
            }
            return ScoreLevelEnum.SCORE_C.getCode();
        }
        return ScoreLevelEnum.SCORE_B.getCode();
    }

    @Override
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level) {
        int workCaseCount = Optional.ofNullable(workResult.getWorkCaseCount()).orElse(0);
        Integer year = workResult.getWorkYear();
        Integer month = workResult.getWorkMonth();
        StringBuilder logContent = new StringBuilder();
        logContent.append(String.format("[%s]在%s年%s月份创建执行用例数：%d个", nickName, year, month, workCaseCount));
        logContent.append(String.format("，评级：%s", level));
        String reason = getRatingReason(workCaseCount, level);
        if (reason != null) {
            logContent.append(String.format("，原因：%s", reason));
        }
        return logContent.toString();
    }

    /**
     * 获取评级原因
     *
     * @param workCaseCount
     * @param level
     * @return
     */
    private String getRatingReason(int workCaseCount, String level) {
        switch (level) {
            case "S":
                return String.format("创建执行用例数(%d)达到S级标准(≥%d个)", workCaseCount, S_EXEC_CASE_COUNT);
            case "A":
                return String.format("创建执行用例数(%d)达到A级标准(≥%d个)", workCaseCount, A_EXEC_CASE_COUNT);
            case "B":
                return String.format("创建执行用例数(%d)达到B级标准(≥%d个)", workCaseCount, B_EXEC_CASE_COUNT);
            case "C":
                return String.format("创建执行用例数(%d)达到C级标准(<%d个)", workCaseCount, C_EXEC_CASE_COUNT);
            case "D":
                return String.format("连续2个月执行用例条数低于%d条", C_EXEC_CASE_COUNT);
            default:
                return null;
        }
    }
}
