<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.WorkStatMapper">

    <resultMap type="com.qmqb.imp.system.domain.WorkStat" id="WorkStatResult">
        <result property="workSId" column="work_s_id"/>
        <result property="workUsername" column="work_username"/>
        <result property="workGroupId" column="work_group_id"/>
        <result property="workGroup" column="work_group"/>
        <result property="workRoleId" column="work_role_id"/>
        <result property="workRole" column="work_role"/>
        <result property="workYear" column="work_year"/>
        <result property="workMonth" column="work_month"/>
        <result property="workDoingTaskCount" column="work_doing_task_count"/>
        <result property="workDoneClosedTaskCount" column="work_done_closed_task_count"/>
        <result property="workCaseCount" column="work_case_count"/>
        <result property="workResolveBugCount" column="work_resolve_bug_count"/>
        <result property="workCloseBugCount" column="work_close_bug_count"/>
        <result property="createBugCount" column="create_bug_count"/>
        <result property="workOverTenDaysTaskCount" column="work_over_ten_days_task_count"/>
    </resultMap>

    <resultMap id="PerfStatMap" type="com.qmqb.imp.system.domain.vo.PerfStatVO">
        <result column="work_username" property="workUsername"/>
        <result column="work_group_id" property="workGroupId"/>
        <result column="work_year" property="workYear"/>
        <result column="work_month" property="workMonth"/>
        <result column="work_doing_task_count" property="workDoingTaskCount"/>
        <result column="work_done_closed_task_count" property="workDoneClosedTaskCount"/>
        <result column="work_case_count" property="workCaseCount"/>
        <result column="work_resolve_bug_count" property="workResolveBugCount"/>
        <result column="work_close_bug_count" property="workCloseBugCount"/>
        <result column="create_bug_count" property="createBugCount"/>
        <result column="kq_absenteeism_days" property="kqAbsenteeismDays"/>
        <result column="kq_attendance_days" property="kqAttendanceDays"/>
        <result column="kq_attendance_work_time" property="kqAttendanceWorkTime"/>
        <result column="kq_late_minute" property="kqLateMinute"/>
        <result column="kq_overtime_approve_count" property="kqOvertimeApproveCount"/>
        <result column="kq_late_count" property="kqLateCount"/>
        <result column="kq_yz_late_times" property="kqYzLateTimes"/>
        <result column="kq_yz_late_count" property="kqYzLateCount"/>
        <result column="kq_leave_early_count" property="kqLeaveEarlyCount"/>
    </resultMap>

    <!-- 获取绩效综合统计列表 -->
    <select id="getPerfStatList" resultMap="PerfStatMap" resultType="com.qmqb.imp.system.domain.vo.PerfStatVO">
        SELECT work_username, work_group_id, work_role, work_year, work_month, work_doing_task_count,
        work_done_closed_task_count, work_case_count, work_resolve_bug_count, work_close_bug_count, create_bug_count, kq_absenteeism_days,
        kq_attendance_days, kq_attendance_work_time, kq_late_minute, kq_overtime_approve_count, kq_late_count,
        kq_yz_late_times, kq_yz_late_count,kq_leave_early_count
        FROM tb_work_stat
        INNER JOIN tb_user_kq_stat
        ON tb_work_stat.work_username = tb_user_kq_stat.kq_user_name
        WHERE work_year = #{year} AND kq_year = #{year} AND work_month = #{month} AND kq_month = #{month}
        <if test="roleIdList != null">
            AND work_role_id IN
            <foreach collection="roleIdList" item="roleId" separator="," open="(" close=")">
                #{roleId}
            </foreach>
        </if>
        <if test="groupIdList != null">
            AND work_group_id IN
            <foreach collection="groupIdList" item="group" separator="," open="(" close=")">
                #{group}
            </foreach>
        </if>
        <if test="bestQuery eq 1">
            AND work_done_closed_task_count > 20
        </if>
    </select>

    <!-- 获取绩效综合统计列表 -->
    <select id="getPerfStatList2" resultType="com.qmqb.imp.system.domain.vo.TrackWorkResultVO">
        SELECT work_username, work_group_id, work_group, work_year,
        <if test="month != -1">
            work_month,
        </if>
        sum(work_case_count) as work_case_count, sum(work_close_bug_count) as work_close_bug_count, sum(create_bug_count) as create_bug_count,
        sum(kq_attendance_work_time) as kq_attendance_work_time,sum(work_done_closed_task_count) as allWorkTaskCount,sum(kq_attendance_days) as kqAttendanceDays,
        sum(work_resolve_bug_count) as workResolveBugCount, sum(a.work_consumed_time) as workConsumedTime,
        sum(a.work_consumed_sys_time) as workConsumedSysTime, sum(a.work_over_ten_days_task_count) as workOverTenDaysTaskCount,
        sum(b.kq_late_minute) as kqLateMinute, sum(b.kq_overtime_approve_count) as kqOvertimeApproveCount
        FROM tb_work_stat a
        left join tb_user_kq_stat b
        ON a.work_username = b.kq_user_name and a.work_year=b.kq_year and a.work_month=b.kq_month
        INNER JOIN sys_user c on a.work_username=c.nick_name and c.del_flag='0'
        <where>
            <if test="groupIdList !=null">
                AND work_group_id IN
                <foreach collection="groupIdList" item="group" separator="," open="(" close=")">
                    #{group}
                </foreach>
            </if>
            <if test="roleIdList !=null">
                AND work_role_id IN
            <foreach collection="roleIdList" item="roleId" separator="," open="(" close=")">
                #{roleId}
            </foreach>
            </if>
            <if test="year !=null">
                AND work_year = #{year}
            </if>
            <if test="month != -1 and month !=null">
                AND work_month = #{month}
            </if>
            <if test="name !=null and name !='' ">
                AND work_username = #{name}
            </if>
            GROUP BY
            <if test="year !=null ">
                work_year,
            </if>
            <if test="month !=-1 and month !=null">
                work_month,
            </if>
            work_username
        </where>
    </select>

    <select id="getPerfStatListWithOutMonth" resultType="com.qmqb.imp.system.domain.vo.TrackWorkResultVO">
        SELECT work_username, work_group_id, work_year,work_month,
        sum(work_case_count) as work_case_count, sum(work_close_bug_count) as work_close_bug_count,
        sum(create_bug_count) as create_bug_count,
        sum(kq_attendance_work_time) as kq_attendance_work_time,sum(work_done_closed_task_count) as allWorkTaskCount,
        sum(work_resolve_bug_count) as workResolveBugCount, a.work_consumed_time as workConsumedTime,
        a.work_consumed_sys_time as workConsumedSysTime, a.work_over_ten_days_task_count as workOverTenDaysTaskCount
        FROM tb_work_stat a
        left join tb_user_kq_stat b
        ON a.work_username = b.kq_user_name and a.work_year=b.kq_year and a.work_month=b.kq_month
        INNER JOIN sys_user c on a.work_username=c.nick_name and c.del_flag='0'
        <where>
            <if test="groupIdList !=null">
                AND work_group_id IN
                <foreach collection="groupIdList" item="group" separator="," open="(" close=")">
                    #{group}
                </foreach>
            </if>
            <if test="roleIdList !=null">
                AND work_role_id IN
                <foreach collection="roleIdList" item="roleId" separator="," open="(" close=")">
                    #{roleId}
                </foreach>
            </if>
            <if test="year !=null">
                AND work_year = #{year}
            </if>
            <if test="name !=null and name !='' ">
                AND work_username = #{name}
            </if>
            <if test="year !=null ">
                GROUP BY work_year,work_month,work_username
            </if>
        </where>
    </select>


    <!-- 获取绩效综合统计列表 -->
    <select id="getWorkStatExcludeDepart" resultType="com.qmqb.imp.system.domain.WorkStat">
        SELECT work_s_id,
        work_username,
        work_group_id,
        work_group,
        work_role_id,
        work_role,
        work_year,
        work_month,
        work_doing_task_count,
        work_over_ten_days_task_count,
        work_done_closed_task_count,
        work_case_count,
        work_resolve_bug_count,
        work_close_bug_count,
        create_bug_count,
        work_consumed_time,
        work_consumed_sys_time
        FROM tb_work_stat a
        INNER JOIN sys_user c on a.work_username=c.nick_name and c.del_flag='0'
        <where>
            <if test="bo.workYear !=null and bo.workYear != -1">
                AND work_year = #{bo.workYear}
            </if>
            <if test="bo.workMonth != -1 and bo.workMonth !=null">
                AND work_month = #{bo.workMonth}
            </if>
            <if test="bo.workUsername !=null and bo.workUsername !='' ">
                AND work_username = #{bo.workUsername}
            </if>
        </where>
    </select>
</mapper>
