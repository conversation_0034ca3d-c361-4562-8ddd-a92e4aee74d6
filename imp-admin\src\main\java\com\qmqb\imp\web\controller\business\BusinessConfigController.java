package com.qmqb.imp.web.controller.business;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.vo.BusinessConfigSelectVo;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.bo.BusinessConfigBo;
import com.qmqb.imp.system.domain.vo.BusinessConfigVo;
import com.qmqb.imp.system.service.IBusinessConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 业务配置
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/businessConfig")
public class BusinessConfigController extends BaseController {

    private final IBusinessConfigService iBusinessConfigService;

    /**
     * 查询业务配置列表
     */
    @SaCheckPermission("system:businessConfig:list")
    @GetMapping("/list")
    public TableDataInfo<BusinessConfigVo> list(BusinessConfigBo bo, PageQuery pageQuery) {
        return iBusinessConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出业务配置列表
     */
    @SaCheckPermission("system:businessConfig:export")
    @Log(title = "业务配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BusinessConfigBo bo, HttpServletResponse response) {
        List<BusinessConfigVo> list = iBusinessConfigService.queryList(bo);
        ExcelUtil.exportExcel(list, "业务配置", BusinessConfigVo.class, response);
    }

    /**
     * 获取业务配置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:businessConfig:query")
    @GetMapping("/{id}")
    public R<BusinessConfigVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iBusinessConfigService.queryById(id));
    }

    /**
     * 新增业务配置
     */
    @SaCheckPermission("system:businessConfig:add")
    @Log(title = "业务配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BusinessConfigBo bo) {
        return toAjax(iBusinessConfigService.insertByBo(bo));
    }

    /**
     * 修改业务配置
     */
    @SaCheckPermission("system:businessConfig:edit")
    @Log(title = "业务配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BusinessConfigBo bo) {
        return toAjax(iBusinessConfigService.updateByBo(bo));
    }

    /**
     * 删除业务配置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:businessConfig:remove")
    @Log(title = "业务配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iBusinessConfigService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 根据类型查询业务配置列表
     * @param bo
     * @return
     */
    @GetMapping("/listByType")
    public R<List<BusinessConfigSelectVo>> listByType(BusinessConfigBo bo) {
        return R.ok(iBusinessConfigService.listByType(bo));
    }
}
