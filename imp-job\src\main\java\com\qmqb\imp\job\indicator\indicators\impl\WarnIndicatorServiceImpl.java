package com.qmqb.imp.job.indicator.indicators.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qmqb.imp.common.core.domain.entity.SysRole;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.*;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.WarnRecord;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.mapper.WarnRecordMapper;
import com.qmqb.imp.system.service.ISysUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2025-07-03 10:54
 * 月绩效预警数
 */
@Service
public class WarnIndicatorServiceImpl implements IndicatorLevelCalcService {

    @Resource
    WarnRecordMapper warnRecordMapper;
    @Resource
    ISysUserService sysUserService;

    /**
     * S: 无预警
     */
    private static final int S_LEVEL_WARN_COUNT = 0;

    /**
     * A:无P0\P1代码预警
     */
    private static final int A_LEVEL_PO_WARN_COUNT = 0;
    private static final int A_LEVEL_P1_WARN_COUNT = 0;

    /**
     * B:无P0代码预警
     */
    private static final int B_LEVEL_PO_WARN_COUNT = 0;

    /**
     * C:代码/用例/全部预警P1及以上>2 或者 P0警数>=1
     */
    private static final int C_LEVEL_P1_UP = 2;
    private static final int C_LEVEL_P0 = 1;


    /**
     * D:连续2月 代码/用例/全部预警P1及以上>2 或者 P0警数>=1
     */
    private static final int D_LEVEL_P1_UP = 2;
    private static final int D_LEVEL_P0 = 1;

    private static final Map<String, String> WARN_TYPE_DESC;
    static {
        Map<String, String> map = new HashMap<>();
        map.put(PersonTypeEnum.DEVELOPER.getDesc(), "代码");
        map.put(PersonTypeEnum.TESTER.getDesc(), "用例");
        map.put("default", "");
        WARN_TYPE_DESC = Collections.unmodifiableMap(map);
    }


    @Override
    public String getIndicatorCode() {
        return PerformanceIndicatorEnum.PERFORMANCE_WARN.getCode();
    }

    @Override
    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults) {
        Map<String, Object> stats = getWarnStatistics(workResult, nickName);
        int currentAllTotal = (int) stats.get("currentAllTotal");
        int currentP0Count = (int) stats.get("currentP0Count");
        int currentP1Count = (int) stats.get("currentP1Count");
        int previousP0Count = (int) stats.get("previousP0Count");
        int previousP1Count = (int) stats.get("previousP1Count");

        Boolean dLevelFlag = ((currentP0Count + currentP1Count) > D_LEVEL_P1_UP || currentP0Count >= D_LEVEL_P0) &&
                ((previousP0Count + previousP1Count) > D_LEVEL_P1_UP || previousP0Count >= D_LEVEL_P0);

        Boolean cLevelFlag = ((currentP0Count + currentP1Count) > C_LEVEL_P1_UP || currentP0Count >= C_LEVEL_P0);

        // 评级逻辑
        if (dLevelFlag) {
            return ScoreLevelEnum.SCORE_D.getCode();
        }
        if (cLevelFlag) {
            return ScoreLevelEnum.SCORE_C.getCode();
        }
        if (currentAllTotal == S_LEVEL_WARN_COUNT) {
            return ScoreLevelEnum.SCORE_S.getCode();
        }
        if (currentP0Count == A_LEVEL_PO_WARN_COUNT && currentP1Count == A_LEVEL_P1_WARN_COUNT) {
            return ScoreLevelEnum.SCORE_A.getCode();
        }
        return ScoreLevelEnum.SCORE_B.getCode();
    }

    /**
     * 获取预警统计数据
     * @param workResult 工作结果
     * @param nickName 用户昵称
     * @return 包含各类预警统计数据的Map
     */
    private Map<String, Object> getWarnStatistics(TrackWorkResultVO workResult, String nickName) {
        Integer year = workResult.getWorkYear();
        Integer month = workResult.getWorkMonth();
        // 1. 时间范围处理
        YearMonth currentMonth = YearMonth.of(year, month);
        YearMonth previousMonth = currentMonth.minusMonths(1);
        // 2. 获取用户信息
        SysUser sysUser = sysUserService.selectUserByNickName(nickName);
        String roleName = sysUser.getRoles().stream().findFirst().map(SysRole::getRoleName).orElse(null);
        // 3. 查询预警数据
        List<WarnRecord> currentWarnRecords = getWarnRecords(sysUser.getUserId(), currentMonth);
        List<WarnRecord> previousWarnRecords = getWarnRecords(sysUser.getUserId(), previousMonth);
        // 4. 统计预警数量
        int currentP0Count = 0, currentP1Count = 0,currentP2Count = 0,currentAllTotal = 0, currentTotal = 0,
            previousTotal = 0,previousP0Count = 0, previousP1Count = 0,previousP2Count = 0;

        if (PersonTypeEnum.DEVELOPER.getDesc().equals(roleName)) {
            currentP0Count = countWarnRecords(currentWarnRecords, WarnLevelEnum.P0, WarnTypeEnum.CODE);
            currentP1Count = countWarnRecords(currentWarnRecords, WarnLevelEnum.P1, WarnTypeEnum.CODE);
            currentP2Count = countWarnRecords(currentWarnRecords, WarnLevelEnum.P2, WarnTypeEnum.CODE);
            currentAllTotal = currentWarnRecords.size();
            currentTotal = countWarnRecords(currentWarnRecords, null, WarnTypeEnum.CODE);
            previousTotal = countWarnRecords(previousWarnRecords, null, WarnTypeEnum.CODE);
            previousP0Count = countWarnRecords(previousWarnRecords, WarnLevelEnum.P0, WarnTypeEnum.CODE);
            previousP1Count = countWarnRecords(previousWarnRecords, WarnLevelEnum.P1, WarnTypeEnum.CODE);
            previousP2Count = countWarnRecords(previousWarnRecords, WarnLevelEnum.P2, WarnTypeEnum.CODE);

        } else if (PersonTypeEnum.TESTER.getDesc().equals(roleName)) {
            currentP0Count = countWarnRecords(currentWarnRecords, WarnLevelEnum.P0, WarnTypeEnum.USE_CASES);
            currentP1Count = countWarnRecords(currentWarnRecords, WarnLevelEnum.P1, WarnTypeEnum.USE_CASES);
            currentP2Count = countWarnRecords(currentWarnRecords, WarnLevelEnum.P2, WarnTypeEnum.USE_CASES);
            currentAllTotal = currentWarnRecords.size();
            currentTotal = countWarnRecords(currentWarnRecords, null, WarnTypeEnum.USE_CASES);
            previousTotal = countWarnRecords(previousWarnRecords, null, WarnTypeEnum.USE_CASES);
            previousP0Count = countWarnRecords(previousWarnRecords, WarnLevelEnum.P0, WarnTypeEnum.USE_CASES);
            previousP1Count = countWarnRecords(previousWarnRecords, WarnLevelEnum.P1, WarnTypeEnum.USE_CASES);
            previousP2Count = countWarnRecords(previousWarnRecords, WarnLevelEnum.P2, WarnTypeEnum.USE_CASES);

        } else {
            currentP0Count = countWarnRecords(currentWarnRecords, WarnLevelEnum.P0, null);
            currentP1Count = countWarnRecords(currentWarnRecords, WarnLevelEnum.P1, null);
            currentP2Count = countWarnRecords(currentWarnRecords, WarnLevelEnum.P2, null);
            currentAllTotal = currentWarnRecords.size();
            currentTotal = currentWarnRecords.size();
            previousTotal = previousWarnRecords.size();
            previousP0Count = countWarnRecords(previousWarnRecords, WarnLevelEnum.P0, null);
            previousP1Count = countWarnRecords(previousWarnRecords, WarnLevelEnum.P1, null);
            previousP2Count = countWarnRecords(previousWarnRecords, WarnLevelEnum.P2, null);
        }

        // 返回统计结果
        Map<String, Object> stats = new HashMap<>(16);
        stats.put("year", year);
        stats.put("month", month);
        stats.put("roleName", roleName);
        stats.put("currentP0Count", currentP0Count);
        stats.put("currentP1Count", currentP1Count);
        stats.put("currentP2Count", currentP2Count);
        stats.put("currentAllTotal", currentAllTotal);
        stats.put("currentTotal", currentTotal);
        stats.put("previousTotal", previousTotal);
        stats.put("previousP0Count", previousP0Count);
        stats.put("previousP1Count", previousP1Count);
        stats.put("previousP2Count", previousP2Count);

        return stats;
    }

    /**
     * 获取某月的预警记录
     * @param userId
     * @param month
     * @return
     */
    private List<WarnRecord> getWarnRecords(Long userId, YearMonth month) {
        LocalDateTime start = month.atDay(1).atStartOfDay();
        LocalDateTime end = month.atEndOfMonth().atTime(23, 59, 59);
        return warnRecordMapper.selectList(new LambdaQueryWrapper<WarnRecord>()
            .eq(WarnRecord::getUserId, userId)
            .between(WarnRecord::getCreateTime, start, end));
    }

    /**
     * 按条件统计预警记录数量
     * @param records 预警记录列表
     * @param level 预警级别
     * @param type 预警类型
     * @return 匹配的记录数量
     */
    private int countWarnRecords(List<WarnRecord> records, WarnLevelEnum level, WarnTypeEnum type) {
        return (int) records.stream()
            .filter(r -> level == null || r.getWarnLevel().equals(level.getValue()))
            .filter(r -> type == null || r.getWarnType().equals(type.getValue()))
            .count();
    }



    @Override
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level) {
        Map<String, Object> stats = getWarnStatistics(workResult, nickName);
        int year = (int) stats.get("year");
        int month = (int) stats.get("month");
        String roleName = (String) stats.get("roleName");
        int currentP0Count = (int) stats.get("currentP0Count");
        int currentP1Count = (int) stats.get("currentP1Count");
        int currentP2Count = (int) stats.get("currentP2Count");
        int currentAllTotal = (int) stats.get("currentAllTotal");
        int currentTotal = (int) stats.get("currentTotal");
        int previousP0Count = (int) stats.get("previousP0Count");
        int previousP1Count = (int) stats.get("previousP1Count");

        // 构建日志内容
        StringBuilder logContent = new StringBuilder();
        logContent.append(String.format("[%s]在%s年%s月份的预警统计：", nickName, year, month));
        // 添加预警数量信息
        if (PersonTypeEnum.DEVELOPER.getDesc().equals(roleName)) {
            logContent.append(String.format("代码类P0预警%d个，P1预警%d个,P2预警%d个", currentP0Count, currentP1Count, currentP2Count));
        } else if (PersonTypeEnum.TESTER.getDesc().equals(roleName)) {
            logContent.append(String.format("用例类P0预警%d个，P1预警%d个,P2预警%d个", currentP0Count, currentP1Count, currentP2Count));
        } else {
            logContent.append(String.format("P0预警%d个，P1预警%d个,P2预警%d个", currentP0Count, currentP1Count,currentP2Count));
        }

        logContent.append(String.format("，总预警数：%d个", currentAllTotal));
        // 添加连续月份信息（针对D级）
        if (ScoreLevelEnum.SCORE_D.getCode().equals(level)) {
            logContent.append(String.format("（上月P0预警%d个，P1预警%d个）", previousP0Count, previousP1Count));
        }
        logContent.append(String.format("，评级：%s", level));

        // 添加评级原因
        String reason = getRatingReason(currentTotal, level, roleName);
        if (reason != null) {
            logContent.append(String.format("，原因：%s", reason));
        }

        return logContent.toString();
    }

    private String getRatingReason(int warnCount, String level, String roleName) {
        String typeDesc = WARN_TYPE_DESC.getOrDefault(roleName, WARN_TYPE_DESC.get("default"));
        switch (level) {
            case "S":
                return "本月无预警达到S级标准";
            case "A":
                return String.format("本月无P0、P1%s预警达到A级标准", typeDesc.isEmpty() ? "" : " " + typeDesc);
            case "B":
                return String.format("本月无P0%s预警达到B级标准", typeDesc.isEmpty() ? "" : " " + typeDesc);
            case "C":
                return String.format("P1及以上预警>2或者P0警数>=1，达到C级标准", typeDesc, warnCount);
            case "D":
                return String.format("连续2个月%sP1及以上预警>2或者P0警数>=1", typeDesc.isEmpty() ? "" : typeDesc + " ");
            default: return null;
        }
    }


}
