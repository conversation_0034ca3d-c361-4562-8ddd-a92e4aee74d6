ALTER TABLE tb_work_stat MODIFY COLUMN `work_over_ten_days_task_count` int DEFAULT 0 COMMENT '转时超过10天的任务数量';

INSERT INTO `tb_warn_trigger_rule` (`id`, `warn_type`, `rule_name`, `rule_type`, `compare_type`, `compare_symbol`, `compare_value`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`)
        VALUES (1919570812833714178, 2, '进行中任务超时预警', 4, 3, 1, 10.00, NULL, 'admin', '2025-05-06 09:52:00', 'admin', '2025-05-07 15:28:42');

INSERT INTO `tb_warn_config` (`id`, `warn_type`, `warn_object`, `warn_level`, `warn_code`, `trigger_rule_id`, `trigger_method`, `warn_content`, `statistics_scope`, `is_used`, `remark`, `del_flag`, `version`, `create_by`, `create_time`, `update_by`, `update_time`)
        VALUES (1919927978870538241, 2, 1, 2, 'MANAGE_WARN_DOING_NOT_FINISH_P2', 1919570812833714178, 1, '您有{peoples}个组员（{members}）存在任务10天内还未完成，请及时安排跟进。', 5, 1, NULL, '0', 0, 'admin', '2025-05-07 09:31:15', 'admin', '2025-05-07 09:59:03');
INSERT INTO `tb_warn_config` (`id`, `warn_type`, `warn_object`, `warn_level`, `warn_code`, `trigger_rule_id`, `trigger_method`, `warn_content`, `statistics_scope`, `is_used`, `remark`, `del_flag`, `version`, `create_by`, `create_time`, `update_by`, `update_time`)
        VALUES (1919926630544408578, 2, 1, 2, 'TASK_WARN_DOING_NOT_FINISH_P2', 1919570812833714178, 1, '您存在任务10天内还未完成，有超时风险，请及时处理', 5, 1, NULL, '0', 0, 'admin', '2025-05-07 09:25:54', 'admin', '2025-05-07 10:00:14');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1921743495365214210, '导出', 1694552679315001345, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:trackWorkResult:export', '#', 'admin', '2025-05-12 09:45:28', 'admin', '2025-05-12 09:45:28', '');
