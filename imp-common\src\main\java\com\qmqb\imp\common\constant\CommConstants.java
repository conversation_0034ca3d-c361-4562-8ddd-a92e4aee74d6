package com.qmqb.imp.common.constant;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version v1.0
 * @Description: TODO(常量管理)
 * @date 2018年5月22日 下午3:11:14
 * @since JDK 1.8
 */
@Component
public class CommConstants {

    public static final String EMPTY = "";

    /**
     * 数字
     */
    public static class CommonVal {
        public static final int MINUS_THREE = -3;
        public static final int MINUS_TWO = -2;
        public static final int MINUS_ONE = -1;
        public static final int ZERO = 0;
        public static final int ONE = 1;
        public static final int TWO = 2;
        public static final int THREE = 3;
        public static final int FOUR = 4;
        public static final int FIVE = 5;
        public static final int SIX = 6;
        public static final int SEVEN = 7;
        public static final int EIGHT = 8;
        public static final int NINE = 9;
		public static final int TEN = 10;
        public static final int ELEVEN= 11;
        public static final int TWELVE = 12;
        public static final int FIFTEEN = 15;
        public static final int SIXTEEN = 16;
        public static final int TWENTY = 20;
        public static final int TWENTY_ONE = 21;
        public static final int THIRTY_TWO = 32;
        public static final int FORTY_THREE = 43;
        public static final int FIFTY = 50;
        public static final int ONE_HUNDRED = 100;
        public static final int ONE_HUNDRED_AND_TWENTY_EIGHT = 128;
        public static final int TWOHUNDDRED = 200;
        public static final int FOUR_HUNDRED_AND_FIFTY = 450;
        public static final int HALF_THOUSAND = 500;
        public static final int THOUSAND = 1000;
        public static final int FIVE_MINUTE = 300000;
        public static final int THIRTY_MINUTE = 1800000;
        public static final float F_ONE_TENTH = (float) 1 /10;
        public static final float F_TWO_TENTH = (float) 2 /10;
        public static final double D_FIVE_TENTH=5/10;
    }

    /**
     * 数字
     */
    public static class CommonValStr {
        public static final String MINUS_ONE = "-1";
        public static final String ZERO = "0";
        public static final String ONE = "1";
        public static final String TWO = "2";
        public static final String THREE = "3";
        public static final String FOUR = "4";
        public static final String FIVE = "5";
        public static final String SIX = "6";
        public static final String NINE = "9";
        public static final String TEN = "10";
        public static final String TWELVE = "12";
        public static final String THIRTY = "30";
    }

    /**
     * 正则
     */
    public static class Regex {
        /**
         * 密码正则
         */
        public static final String SIX_TWENTY_NUM_LETTER_PUNCTUATION = "(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9-*/+.~!@#$%^&*()]{6,20}$";
        /**
         * 手机号正则
         */
        public static final String MOBILE_RULE = "^1[3-9]\\d{9}$";
        /**
         * 验证码 四位
         */
        public static final String VERIFY_CODE = "^\\d{4}$";

        /**
         * 姓名正则表达式
         */
        public static final String NAME_RULE = "^[\\u4E00-\\u9FA5]{2,20}(·[\\u4E00-\\u9FA5]{1,20})*$";

    }

    public static String getEmptyStr() {
        return EMPTY;
    }
}
