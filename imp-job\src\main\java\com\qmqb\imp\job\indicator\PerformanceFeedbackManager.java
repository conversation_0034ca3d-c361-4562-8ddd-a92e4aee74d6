package com.qmqb.imp.job.indicator;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.constant.PerformanceFeedbackConstants;
import com.qmqb.imp.common.core.domain.entity.SysDept;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.*;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.bo.TrackWorkResultBO;
import com.qmqb.imp.system.domain.bo.performance.PerformanceEventBo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackMainBo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackMainQueryBo;
import com.qmqb.imp.system.domain.performance.PerformanceFeedback;
import com.qmqb.imp.system.domain.performance.PerformanceFeedbackMain;
import com.qmqb.imp.system.domain.performance.PerformanceEvent;
import com.qmqb.imp.system.domain.performance.PerformanceTutoring;
import com.qmqb.imp.system.domain.vo.DepartmentWarnAnalysisVo;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.domain.vo.performance.PerformanceEventVo;
import com.qmqb.imp.system.service.ISysDeptService;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.ITrackWorkResultService;
import com.qmqb.imp.system.service.IUserKqStatService;
import com.qmqb.imp.system.service.impl.performance.PerformanceFeedbackCodeGenerator;
import com.qmqb.imp.system.service.indicator.IPerformanceEventService;
import com.qmqb.imp.system.service.indicator.IPerformanceFeedbackMainService;
import com.qmqb.imp.system.service.indicator.IPerformanceFeedbackService;
import com.qmqb.imp.system.service.indicator.IPerformanceTutoringService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 绩效反馈生成任务管理器
 * 独立生成绩效反馈记录，不依赖绩效主表
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PerformanceFeedbackManager {

    @Autowired
    private IndicatorLevelCalcRegistry registry;

    @Autowired
    private IPerformanceFeedbackService performanceFeedbackService;

    @Autowired
    private ITrackWorkResultService trackWorkResultService;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private PerformanceFeedbackGenerator performanceFeedbackGenerator;

    @Autowired
    private PerformanceFeedbackMainGenerator performanceFeedbackMainGenerator;

    @Autowired
    private IPerformanceFeedbackMainService performanceFeedbackMainService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IPerformanceTutoringService perfTutoringService;

    @Autowired
    private IPerformanceEventService performanceEventService;

    @Autowired
    private PerformanceFeedbackCodeGenerator codeGenerator;

    /**
     * 为指定年月和部门的员工生成绩效反馈记录
     *
     * @param year   年份
     * @param month  月份
     * @param pType  表示当前逻辑是跑哪个人员类型 0非组长，1组长
     * @param deptId 部门ID，如果为null则处理所有部门
     */
    public void generateFeedbackByYearMonthAndDept(Integer year, Integer month, Integer pType, Long deptId) {
        // 1. 获取部门ID列表
        List<Long> groupIdList;
        if (deptId != null) {
            // 指定部门ID
            groupIdList = new ArrayList<>();
            groupIdList.add(deptId);
        } else {
            // 获取所有技术中心的部门ID
            groupIdList = sysDeptService.listTecCenterDeptIdList();
        }

        // 2. 获取工作成果数据
        TrackWorkResultBO request = new TrackWorkResultBO();
        request.setPageNum(1);
        request.setPageSize(1000);
        request.setEvalYear(year);
        request.setEvalMonth(month);
        if (deptId != null) {
            request.setGroupId(deptId);
        }

        Page<TrackWorkResultVO> page = new Page<>(request.getPageNum(), request.getPageSize());
        TableDataInfo<TrackWorkResultVO> trackWorkResultVoTableDataInfo =
            trackWorkResultService.getTrackWorkResultVoTableDataInfo(request, page, groupIdList, year, month);
        List<TrackWorkResultVO> rows = trackWorkResultVoTableDataInfo.getRows();
        // 3. 按角色分类处理数据
        Map<Long, PersonTypeEnum> roleIdMap = getRoleIdMap();
        Map<PersonTypeEnum, List<TrackWorkResultVO>> roleDataMap = groupDataByRole(rows, roleIdMap);
        // 收集所有有效的用户昵称，去重
        List<String> validNickNames = rows.stream()
            .map(TrackWorkResultVO::getWorkUsername)
            .filter(nickName -> {
                if (nickName == null) {
                    return false;
                }
                if (sysUserService.selectUserByNickName(nickName) == null) {
                    log.error("用户不存在，跳过生成反馈记录，用户名：{}", nickName);
                    return false;
                }
                return true;
            })
            .distinct()
            .collect(Collectors.toList());
        if (!validNickNames.isEmpty()) {
            ((PerformanceFeedbackManager) AopContext.currentProxy())
                .generateFeedbackForEmployees(validNickNames, year, month,pType, rows);
        }
    }

    /**
     * 按指标批量为多个人生成绩效反馈事件、主表、明细
     * @param nickNames 员工昵称列表
     * @param year 年份
     * @param month 月份
     * @param trackWorkResults 工作成果数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void generateFeedbackForEmployees(List<String> nickNames, Integer year, Integer month,
                                             Integer pType,
                                             List<TrackWorkResultVO> trackWorkResults) {
        List<IndicatorLevelCalcService> serviceList = registry.getAll();
        if (serviceList == null || serviceList.isEmpty() || nickNames == null || nickNames.isEmpty()) {
            return;
        }
        // 3. 按角色分类处理数据
        Map<Long, PersonTypeEnum> roleIdMap = getRoleIdMap();
        Map<PersonTypeEnum, List<TrackWorkResultVO>> roleDataMap = groupDataByRole(trackWorkResults, roleIdMap);

        for (IndicatorLevelCalcService service : serviceList) {
            String indicatorCode = service.getIndicatorCode();
            // 先删除旧事件
            removeOldEvent(year, month, indicatorCode, pType);
            PerformanceEvent event = buildEvent(year, month, indicatorCode, pType);
            List<PerformanceFeedbackMain> feedbackMainList = new ArrayList<>();
            List<PerformanceFeedback> feedbackList = new ArrayList<>();
            List<PerformanceTutoring> tutoringList = new ArrayList<>();
            // 组装主表和明细时，建立nickName+indicatorCode到主表的映射
            Map<String, PerformanceFeedbackMain> nickIndicatorToMain = new HashMap<>(16);
            for (String nickName : nickNames) {
                // 根据nickName获取对应的单个角色工作成果数据
                TrackWorkResultVO userWorkResults = trackWorkResults.stream()
                        .filter(r -> nickName.equals(r.getWorkUsername()))
                        .findFirst().orElse(null);
                PersonTypeEnum personTypeEnum = roleIdMap.get(Long.parseLong(userWorkResults.getRoleIds()));
                // 岗位工作成果
                List<TrackWorkResultVO> vos = roleDataMap.get(personTypeEnum);
                //组长的数据过滤掉，因为组长的反馈数据是需要组员的数据来计算，所以统一放到绩效指标统计定时任务calcPerformanceIndicatorJobHandler处理
                if (pType == 1 && !PersonTypeEnum.TECHNICAL_MANAGER.getType().equals(personTypeEnum.getType())) {
                    continue;
                }
                //如果跑非组长的数据，则跳过组长的数据
                if (pType == 0 && PersonTypeEnum.TECHNICAL_MANAGER.getType().equals(personTypeEnum.getType())) {
                    continue;
                }
                if (!registry.isIndicatorBelongToRole(personTypeEnum, indicatorCode)) {
                    // 如果指标不属于该角色，跳过
                    continue;
                }

                removeOldData(nickName, year, month, indicatorCode);
                SysUser sysUser = sysUserService.selectUserByNickName(nickName);
                Long groupId = sysUser != null && sysUser.getDept() != null ? sysUser.getDept().getDeptId() : null;
                String groupName = sysUser != null && sysUser.getDept() != null ? sysUser.getDept().getDeptName() : null;
                if (isWorkTimeZero(nickName, vos)) {
                    continue;
                }
                IndicatorLevelCalcService.IndicatorCalcResult calcResult = service.calcLevel(nickName, month, vos);
                PerformanceFeedbackMain mainFeedback = performanceFeedbackMainGenerator.createMainFeedbackRecord(
                        nickName, year, month, String.valueOf(personTypeEnum.getType()), indicatorCode, calcResult, sysUser);
                // key为nickName-indicatorCode
                String key = nickName + "-" + indicatorCode;
                nickIndicatorToMain.put(key, mainFeedback);
                PerformanceFeedback feedback = performanceFeedbackGenerator.createFeedbackRecord(
                        nickName, year, month, personTypeEnum, indicatorCode, calcResult, groupId, groupName);
                if (ScoreLevelEnum.SCORE_B.getCode().equals(feedback.getRecommendedLevel())) {
                    continue;
                }
                feedbackMainList.add(mainFeedback);
                // 暂不设置mainFeedbackId，后续保存主表后再设置
                feedbackList.add(feedback);
            }
            performanceEventService.save(event);
            Long eventId = event.getId();
            feedbackMainList.forEach(main -> main.setEventId(eventId));
            if (!feedbackMainList.isEmpty()) {
                // 先批量保存主表，生成主键ID
                performanceFeedbackMainService.saveBatch(feedbackMainList);
                // 设置明细的mainFeedbackId为数据库生成的主表ID
                for (PerformanceFeedback feedback : feedbackList) {
                    String key = feedback.getNickName() + "-" + indicatorCode;
                    PerformanceFeedbackMain main = nickIndicatorToMain.get(key);
                    if (main != null) {
                        feedback.setMainFeedbackId(main.getId());
                    }
                }
            }else {
                // 如果没有有效的反馈记录，删除事件
                performanceEventService.removeById(eventId);
            }
            if (!feedbackList.isEmpty()) {
                performanceFeedbackService.saveBatch(feedbackList);
            }
            buildAndSaveTutoring(feedbackMainList, feedbackList, tutoringList);
        }
    }

    /**
     * 构建事件主表
     */
    private PerformanceEvent buildEvent(Integer year, Integer month, String indicatorCode, Integer pType) {
        PerformanceIndicatorEnum categoryEnum = PerformanceIndicatorEnum.fromCode(indicatorCode);
        String name = categoryEnum.getName();
        String eventTitle;
        String eventDetail;
        if (pType == 1) {
            eventTitle = "组长绩效事件-" + name;
            eventDetail = "自动生成组长绩效事件，指标:" + name;
        } else {
            eventTitle = "绩效事件-" + name;
            eventDetail = "自动生成绩效事件，指标:" + name;
        }
        Date now = new Date();
        // 取上个月的开始和结束时间
        LocalDate firstDayOfMonth = LocalDate.of(year, month, 1);
        LocalDate lastDayOfMonth = firstDayOfMonth.withDayOfMonth(firstDayOfMonth.lengthOfMonth());
        Date start = java.sql.Date.valueOf(firstDayOfMonth);
        Date end = java.sql.Timestamp.valueOf(lastDayOfMonth.atTime(23, 59, 59));
        PerformanceEvent event = new PerformanceEvent();
        event.setYear(year);
        event.setMonth(month);
        event.setFeedbackTime(now);
        event.setFeedbackCode(codeGenerator.generateFeedbackCode());
        event.setEventTitle(eventTitle);
        event.setEventDetail(eventDetail);
        event.setEventStartTime(start);
        event.setEventEndTime(end);
        event.setDataSource(PerformanceFeedbackDataSourceEnum.SYSTEM_GENERATED.getCode());
        event.setSubmitTime(now);
        event.setSubmitter(PerformanceFeedbackConstants.SYSTEM_SUBMITTER);
        event.setFinalAudit(PerformanceFeedbackAuditStatusEnum.NOT_AUDITED.getCode());
        return event;
    }

    /**
     * 删除旧数据（按员工、年月、指标）
     */
    private void removeOldData(String nickName, Integer year, Integer month, String indicatorCode) {
        // 通过明细表查找主表ID
        List<PerformanceFeedback> feedbackList = performanceFeedbackService.getMainBySecondaryIndicator(nickName, year, month, indicatorCode);
        if (feedbackList != null && !feedbackList.isEmpty()) {
            List<String> mainIds = feedbackList.stream()
                    .map(PerformanceFeedback::getMainFeedbackId)
                    .filter(Objects::nonNull)
                    .map(String::valueOf)
                    .distinct()
                    .collect(Collectors.toList());
            perfTutoringService.removeByMainIds(mainIds);
            performanceFeedbackService.removeByMainIds(mainIds);
            performanceFeedbackMainService.removeBatchByIds(mainIds);
        }
    }

    /**
     * 删除旧事件（同year、month、indicatorCode、角色类型的event）
     */
    private void removeOldEvent(Integer year, Integer month, String indicatorCode, Integer pType) {
        PerformanceIndicatorEnum categoryEnum = PerformanceIndicatorEnum.fromCode(indicatorCode);
        String name = categoryEnum.getName();
        String eventTitle;
        String eventDetail;
        if (pType == 1) {
            eventTitle = "组长绩效事件-" + name;
            eventDetail = "自动生成组长绩效事件，指标:" + name;
        } else {
            eventTitle = "绩效事件-" + name;
            eventDetail = "自动生成绩效事件，指标:" + name;
        }
        PerformanceEventBo bo = new PerformanceEventBo();
        bo.setYear(year);
        bo.setMonth(month);
        bo.setEventTitle(eventTitle);
        bo.setEventDetail(eventDetail);
        List<PerformanceEventVo> oldEvents = performanceEventService.queryList(bo);
        if (oldEvents != null && !oldEvents.isEmpty()) {
            List<Long> ids = oldEvents.stream()
                .filter(e -> Objects.equals(e.getYear(), year)
                        && Objects.equals(e.getMonth(), month)
                        && Objects.equals(e.getEventTitle(), eventTitle)
                        && Objects.equals(e.getEventDetail(), eventDetail))
                .map(PerformanceEventVo::getId)
                .collect(Collectors.toList());
            if (!ids.isEmpty()) {
                performanceEventService.removeBatchByIds(ids);
            }
        }
    }

    /**
     * 判断工时是否为0
     */
    private boolean isWorkTimeZero(String nickName, List<TrackWorkResultVO> trackWorkResults) {
        Optional<TrackWorkResultVO> userWorkResultOpt = trackWorkResults.stream()
                .filter(r -> nickName.equals(r.getWorkUsername()))
                .findFirst();
        return userWorkResultOpt.isPresent() && Double.parseDouble(userWorkResultOpt.get().getKqAttendanceWorkTime()) == 0.0;
    }

    /**
     * 生成并保存辅导记录
     */
    private void buildAndSaveTutoring(List<PerformanceFeedbackMain> feedbackMainList, List<PerformanceFeedback> feedbackList, List<PerformanceTutoring> tutoringList) {
        Map<Long, PerformanceFeedbackMain> performanceFeedbackMainMap = feedbackMainList.stream().collect(Collectors.toMap(PerformanceFeedbackMain::getId, a -> a));
        for (PerformanceFeedback feedback : feedbackList) {
            PerformanceFeedbackMain main = performanceFeedbackMainMap.getOrDefault(feedback.getMainFeedbackId(), null);
            if (main == null) {
                throw new ServiceException("主反馈记录不存在");
            }
            if (main.getProjectManagerAuditStatus().equals(PerformanceFeedbackAuditStatusEnum.APPROVED.getCode())) {
                if (ScoreLevelEnum.SCORE_C.getCode().equals(feedback.getRecommendedLevel()) ||
                        ScoreLevelEnum.SCORE_D.getCode().equals(feedback.getRecommendedLevel())) {
                    PerformanceTutoring tutoring = PerformanceTutoring.builder().feedbackId(feedback.getId()).build();
                    tutoringList.add(tutoring);
                }
            }
        }
        if (!tutoringList.isEmpty()) {
            perfTutoringService.saveBatch(tutoringList);
        }
    }

    /**
     * 获取角色ID映射
     */
    private Map<Long, PersonTypeEnum> getRoleIdMap() {
        return java.util.Arrays.stream(PersonTypeEnum.values())
            .filter(e -> e.getRoleId() != null)
            .collect(java.util.stream.Collectors.toMap(PersonTypeEnum::getRoleId, Function.identity()));
    }

    /**
     * 按角色分组数据
     */
    private Map<PersonTypeEnum, List<TrackWorkResultVO>> groupDataByRole(List<TrackWorkResultVO> rows, Map<Long, PersonTypeEnum> roleIdMap) {
        return rows.stream()
            .map(vo -> {
                if (vo.getRoleIds() == null) {
                    return null;
                }
                try {
                    // 支持多个角色ID，按逗号分割
                    List<Long> roleIdList = java.util.Arrays.stream(vo.getRoleIds().split(","))
                        .filter(roleIdStr -> !roleIdStr.trim().isEmpty())
                        .map(Long::parseLong)
                        .collect(java.util.stream.Collectors.toList());
                    // 遍历所有角色ID，找到第一个匹配的PersonTypeEnum
                    for (Long roleId : roleIdList) {
                        PersonTypeEnum personType = roleIdMap.get(roleId);
                        if (personType != null) {
                            // 返回第一个匹配的角色类型
                            return new java.util.AbstractMap.SimpleImmutableEntry<>(personType, vo);
                        }
                    }
                    // 没有匹配的角色类型，返回null
                    return null;
                } catch (NumberFormatException e) {
                    // roleIds 不是有效的数字格式，返回 null
                    return null;
                }
            })
            .filter(entry -> entry != null && entry.getKey() != null)
            .collect(java.util.stream.Collectors.groupingBy(java.util.Map.Entry::getKey,
                java.util.stream.Collectors.mapping(java.util.Map.Entry::getValue, java.util.stream.Collectors.toList())));
    }
}
