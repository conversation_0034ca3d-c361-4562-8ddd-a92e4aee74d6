package com.qmqb.imp.system.domain.dto;

import lombok.Data;

/**
 * 绩效事件状态DTO
 * 用于批量查询事件ID对应的提交、项管审核、最终审核状态（返回code）
 * <AUTHOR>
 */
@Data
public class PerformanceEventStatusDTO {
    /** 事件ID */
    private Long eventId;
    /** 提交状态code */
    private String submitStatus;
    /** 项管审核状态code */
    private String projectManagerAuditStatus;
    /** 项管审核人 */
    private String projectManagerAuditor;
    /** 最终审核状态code */
    private String finalAudit;
}
