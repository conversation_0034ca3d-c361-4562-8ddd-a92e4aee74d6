package com.qmqb.imp.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 绩效指标分类枚举
 * <p>
 * 定义了绩效指标的分类，包括：
 * - 工作成果类：代码行数、文档、任务、交付时效、开发效率等
 * - 工作质量类：故障、预警、规范、评审会等
 * - 协作能力类：服从、协作、制度、风险处理等
 * </p>
 *
 * <AUTHOR>
 */
@Getter
public enum IndicatorCategoryEnum {
    /**
     * 工作成果类
     */
    WORK_ACHIEVEMENT("work_achievement", "工作成果类", Arrays.asList(
        PerformanceIndicatorEnum.CODE_LINES.getCode(),
        PerformanceIndicatorEnum.WORK_DOCS.getCode(),
        PerformanceIndicatorEnum.ZENTAO_TASKS.getCode(),
        PerformanceIndicatorEnum.ATTENDANCE.getCode(),
        PerformanceIndicatorEnum.BUG_COUNT.getCode(),
        PerformanceIndicatorEnum.CASE_EXECUTE_COUNT.getCode(),
        PerformanceIndicatorEnum.PLAN_MAINTAIN_COUNT.getCode(),
        PerformanceIndicatorEnum.PROJECT_RELEASE_COUNT.getCode(),
        PerformanceIndicatorEnum.GROUP_AVG_ATTENDANCE.getCode(),
        PerformanceIndicatorEnum.GROUP_RESULT_OUTPUT.getCode(),
        PerformanceIndicatorEnum.GROUP_AVG_WORK_DOCS.getCode(),
        PerformanceIndicatorEnum.PROD_FAULT.getCode(),
        PerformanceIndicatorEnum.GROUP_AVG_ZENTAO_TASKS.getCode(),
        PerformanceIndicatorEnum.GROUP_PERFORMANCE_WARN.getCode(),
        PerformanceIndicatorEnum.PERFORMANCE_WARN.getCode()
    )),

    /**
     * 工作质量类
     */
    WORK_QUALITY("work_quality", "工作质量类", Arrays.asList(
        PerformanceIndicatorEnum.DEV_SPEC.getCode(),
        PerformanceIndicatorEnum.REVIEW_MEETING.getCode(),
        PerformanceIndicatorEnum.PROD_SECURITY_MANAGE.getCode(),
        PerformanceIndicatorEnum.PROD_OPERATION_MANAGE.getCode(),
        PerformanceIndicatorEnum.ORGANIZATIONAL_MEETINGS.getCode(),
        PerformanceIndicatorEnum.RESOURCE_MANAGE.getCode(),
        PerformanceIndicatorEnum.WORK_EFFICIENCY_MANAGE.getCode(),
        PerformanceIndicatorEnum.PROJECT_DELIVERY_TIMELINESS.getCode(),
        PerformanceIndicatorEnum.DEV_EFFICIENCY.getCode(),
        PerformanceIndicatorEnum.MEETING_ATTENDANCE.getCode(),
        PerformanceIndicatorEnum.DELIVERY_TIMELINESS.getCode()
    )),

    /**
     * 协作能力类
     */
    COLLABORATION_ABILITY("collaboration_ability", "协作能力类", Arrays.asList(
        PerformanceIndicatorEnum.WORK_OBEY.getCode(),
        PerformanceIndicatorEnum.WORK_RULES.getCode(),
        PerformanceIndicatorEnum.TEAMWORK.getCode(),
        PerformanceIndicatorEnum.RISK_HANDLE.getCode()
    ));

    /**
     * 分类编码
     */
    private final String code;

    /**
     * 分类名称
     */
    private final String name;

    /**
     * 该分类包含的指标编码列表
     */
    private final List<String> indicatorCodes;

    /**
     * 构造方法
     *
     * @param code           分类编码
     * @param name           分类名称
     * @param indicatorCodes 指标编码列表
     */
    IndicatorCategoryEnum(String code, String name, List<String> indicatorCodes) {
        this.code = code;
        this.name = name;
        this.indicatorCodes = indicatorCodes;
    }

    /**
     * 根据指标编码获取所属分类
     *
     * @param indicatorCode 指标编码
     * @return 分类枚举
     */
    public static IndicatorCategoryEnum getByIndicatorCode(String indicatorCode) {
        for (IndicatorCategoryEnum category : values()) {
            if (category.getIndicatorCodes().contains(indicatorCode)) {
                return category;
            }
        }
        return null;
    }

    /**
     * 根据分类编码获取分类
     *
     * @param code 分类编码
     * @return 分类枚举
     */
    public static IndicatorCategoryEnum getByCode(String code) {
        for (IndicatorCategoryEnum category : values()) {
            if (category.getCode().equals(code)) {
                return category;
            }
        }
        return null;
    }
}
