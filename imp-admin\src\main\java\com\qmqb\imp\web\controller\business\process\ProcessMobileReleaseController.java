package com.qmqb.imp.web.controller.business.process;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.system.domain.bo.process.ProcessMobileReleaseBo;
import com.qmqb.imp.system.domain.vo.process.ProcessMobileReleaseVo;
import com.qmqb.imp.system.service.process.IProcessMobileReleaseService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 移动端应用发布
 *
 * <AUTHOR>
 * @date 2024-12-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/processMobileRelease")
public class ProcessMobileReleaseController extends BaseController {

    private final IProcessMobileReleaseService iProcessMobileReleaseService;

    /**
     * 查询移动端应用发布列表
     */
    @SaCheckPermission("system:processMobileRelease:list")
    @GetMapping("/list")
    public TableDataInfo<ProcessMobileReleaseVo> list(ProcessMobileReleaseBo bo, PageQuery pageQuery) {
        return iProcessMobileReleaseService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取移动端应用发布详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:processMobileRelease:query")
    @GetMapping("/{id}")
    public R<ProcessMobileReleaseVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iProcessMobileReleaseService.queryById(id));
    }

    /**
     * 新增移动端应用发布
     */
    @SaCheckPermission("system:processMobileRelease:add")
    @Log(title = "移动端应用发布", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProcessMobileReleaseBo bo) {
        return toAjax(iProcessMobileReleaseService.insertByBo(bo));
    }

    /**
     * 修改移动端应用发布
     */
    @SaCheckPermission("system:processMobileRelease:edit")
    @Log(title = "移动端应用发布", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProcessMobileReleaseBo bo) {
        return toAjax(iProcessMobileReleaseService.updateByBo(bo));
    }

    /**
     * 删除移动端应用发布
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:processMobileRelease:remove")
    @Log(title = "移动端应用发布", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iProcessMobileReleaseService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
