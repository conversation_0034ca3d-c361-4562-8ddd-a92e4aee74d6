package com.qmqb.imp.system.service.indicator;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceEventBo;
import com.qmqb.imp.system.domain.performance.PerformanceEvent;
import com.qmqb.imp.system.domain.performance.PerformanceFeedbackMainIndicatorResult;
import com.qmqb.imp.system.domain.vo.performance.PerformanceEventVo;

import java.util.Collection;
import java.util.List;

/**
 * 绩效事件Service接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface IPerformanceEventService extends IService<PerformanceEvent> {

    /**
     * 根据ID查询绩效事件
     *
     * @param id 绩效事件主键ID
     * @return 绩效事件视图对象
     */
    PerformanceEventVo queryById(Long id);

    /**
     * 分页查询绩效事件列表
     *
     * @param bo 查询条件业务对象
     * @param pageQuery 分页参数
     * @return 分页结果集
     */
    TableDataInfo<PerformanceEventVo> queryPageList(PerformanceEventBo bo, PageQuery pageQuery);

    /**
     * 查询绩效事件列表
     *
     * @param bo 查询条件业务对象
     * @return 绩效事件视图对象列表
     */
    List<PerformanceEventVo> queryList(PerformanceEventBo bo);

    /**
     * 新增绩效事件
     *
     * @param bo 新增业务对象
     * @return 是否新增成功
     */
    Boolean insertByBo(PerformanceEventBo bo);

    /**
     * 修改绩效事件
     *
     * @param bo 修改业务对象
     * @return 是否修改成功
     */
    Boolean updateByBo(PerformanceEventBo bo);

        /**
     * 校验并批量删除绩效事件信息
     *
     * @param ids 需要删除的主键集合
     * @param isValid 是否校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量提交绩效事件（实际提交其下所有主表）
     *
     * @param eventIds 事件ID集合
     * @param submitter 提交人
     * @return 是否成功
     */
    Boolean batchSubmit(Collection<Long> eventIds, String submitter);

    /**
     * 保存并提交绩效事件
     *
     * @param bo 绩效事件业务对象
     * @return 是否成功
     */
    Boolean saveAndSubmit(PerformanceEventBo bo);
}
