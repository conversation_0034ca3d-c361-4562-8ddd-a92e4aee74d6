-- 为tb_project_result表添加索引优化查询性能
-- 基于代码分析的查询场景和性能优化需求

-- 1. 业务类型ID索引 - 用于关联查询和业务分类筛选
CREATE INDEX idx_business_type_id ON tb_project_result (business_type_id);

-- 2. 状态索引 - 用于状态筛选（未开始、进行中、已完成、已取消）
CREATE INDEX idx_status ON tb_project_result (status);

-- 3. 成果类型索引 - 用于成果类型筛选和排除事项支撑类型的查询
CREATE INDEX idx_result_type ON tb_project_result (result_type);

-- 4. 优先级索引 - 用于优先级筛选和排序
CREATE INDEX idx_priority_level ON tb_project_result (priority_level);

-- 5. 归档标志索引 - 用于区分已归档和未归档数据
CREATE INDEX idx_archive_flag ON tb_project_result (archive_flag);

-- 6. 删除标志索引 - 用于逻辑删除过滤
CREATE INDEX idx_del_flag ON tb_project_result (del_flag);

-- 7. 创建时间索引 - 用于时间范围查询和排序
CREATE INDEX idx_create_time ON tb_project_result (create_time);

-- 8. 完成时间索引 - 用于完成时间范围查询
CREATE INDEX idx_completion_time ON tb_project_result (completion_time);

-- 9. 成果编号唯一索引 - 确保成果编号唯一性并提高查询性能
CREATE UNIQUE INDEX idx_result_code_unique ON tb_project_result (result_code);

-- 10. 复合索引：状态+归档标志+删除标志 - 用于常见的组合查询条件
CREATE INDEX idx_status_archive_del ON tb_project_result (status, archive_flag, del_flag);

-- 11. 复合索引：业务类型+状态+删除标志 - 用于业务类型相关的状态查询
CREATE INDEX idx_business_status_del ON tb_project_result (business_type_id, status, del_flag);

-- 12. 复合索引：成果类型+删除标志 - 用于排除事项支撑类型的查询优化
CREATE INDEX idx_result_type_del ON tb_project_result (result_type, del_flag);

-- 13. 项目任务名称索引 - 用于模糊查询优化（使用前缀索引）
CREATE INDEX idx_project_task_name ON tb_project_result (project_task_name(50));

-- 14. 项目经理索引 - 用于项目经理模糊查询优化（使用前缀索引）
CREATE INDEX idx_project_managers ON tb_project_result (project_managers(100));

-- 15. 复合索引：删除标志+创建时间 - 用于按创建时间排序的查询优化
CREATE INDEX idx_del_create_time ON tb_project_result (del_flag, create_time);

-- 16. 复合索引：归档标志+删除标志 - 用于默认查询条件优化
CREATE INDEX idx_archive_del ON tb_project_result (archive_flag, del_flag);

-- 索引使用说明：
-- 1. idx_business_type_id: 用于与tb_business_type表的关联查询
-- 2. idx_status: 用于状态筛选，特别是查询进行中或未开始的项目成果
-- 3. idx_result_type: 用于成果类型筛选，特别是排除事项支撑类型的查询
-- 4. idx_priority_level: 用于优先级筛选和P1/P2/P3排序
-- 5. idx_archive_flag: 用于区分已归档和未归档数据的查询
-- 6. idx_del_flag: 用于逻辑删除过滤
-- 7. idx_create_time: 用于创建时间范围查询和排序
-- 8. idx_completion_time: 用于完成时间范围查询
-- 9. idx_result_code_unique: 确保成果编号唯一性
-- 10. idx_status_archive_del: 用于常见的状态+归档+删除标志组合查询
-- 11. idx_business_status_del: 用于业务类型相关的状态查询
-- 12. idx_result_type_del: 用于排除事项支撑类型的查询优化
-- 13. idx_project_task_name: 用于项目名称模糊查询
-- 14. idx_project_managers: 用于项目经理模糊查询
-- 15. idx_del_create_time: 用于按创建时间排序的查询
-- 16. idx_archive_del: 用于默认查询条件（未归档且未删除）
