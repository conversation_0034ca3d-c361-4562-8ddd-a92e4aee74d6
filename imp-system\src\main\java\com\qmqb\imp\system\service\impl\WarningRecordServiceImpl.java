package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.WarningRecord;
import com.qmqb.imp.system.domain.bo.WarningRecordBo;
import com.qmqb.imp.system.domain.vo.WarningRecordVo;
import com.qmqb.imp.system.mapper.WarningRecordMapper;
import com.qmqb.imp.system.service.IWarningRecordService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@RequiredArgsConstructor
@Service
public class WarningRecordServiceImpl implements IWarningRecordService {

    private final WarningRecordMapper baseMapper;

    /**
     * 查询【请填写功能名称】
     */
    @Override
    public WarningRecordVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     */
    @Override
    public TableDataInfo<WarningRecordVo> queryPageList(WarningRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WarningRecord> lqw = buildQueryWrapper(bo);
        Page<WarningRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询【请填写功能名称】列表
     */
    @Override
    public List<WarningRecordVo> queryList(WarningRecordBo bo) {
        LambdaQueryWrapper<WarningRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WarningRecord> buildQueryWrapper(WarningRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WarningRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getType()), WarningRecord::getType, bo.getType());
        lqw.eq(bo.getSendTime() != null, WarningRecord::getSendTime, bo.getSendTime());
        return lqw;
    }

    /**
     * 新增【请填写功能名称】
     */
    @Override
    public Boolean insertByBo(WarningRecordBo bo) {
        WarningRecord add = BeanUtil.toBean(bo, WarningRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改【请填写功能名称】
     */
    @Override
    public Boolean updateByBo(WarningRecordBo bo) {
        WarningRecord update = BeanUtil.toBean(bo, WarningRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WarningRecord entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除【请填写功能名称】
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
