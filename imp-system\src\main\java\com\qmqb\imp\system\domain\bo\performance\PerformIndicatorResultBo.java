package com.qmqb.imp.system.domain.bo.performance;

import com.qmqb.imp.common.core.domain.BaseEntity;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 绩效指标原因业务对象 tb_perform_indicator_result
 *
 * <AUTHOR>
 * @date 2025-07-01
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class PerformIndicatorResultBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 一级指标
     */
    @NotBlank(message = "一级指标不能为空", groups = { AddGroup.class, EditGroup.class })
    private String firstIndecator;

    /**
     * 二级指标
     */
    @NotBlank(message = "二级指标不能为空", groups = { AddGroup.class, EditGroup.class })
    private String secondIndecator;

    /**
     * 绩效级别(S/A/B/C/D)
     */
    @NotBlank(message = "绩效级别(S/A/B/C/D)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String level;

    /**
     * 推荐原因
     */
    @NotBlank(message = "推荐原因不能为空", groups = { AddGroup.class, EditGroup.class })
    private String result;


}
