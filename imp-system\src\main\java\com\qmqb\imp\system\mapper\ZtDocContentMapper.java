package com.qmqb.imp.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.ZtDocContent;
import com.qmqb.imp.system.domain.vo.ZtDocContentVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 禅道文档内容Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@DS(DataSource.ZENTAO)
public interface ZtDocContentMapper extends BaseMapperPlus<ZtDocContentMapper, ZtDocContent, ZtDocContentVo> {

    /**
     * 根据时间范围获取文档记录（包含内容）
     * @param userName
     * @param beginTime
     * @param endTime
     * @return
     */
    List<ZtDocContentVo> getUserDocumentsWithContent(@Param("userName") String userName, @Param("beginTime") String beginTime, @Param("endTime") String endTime);
} 