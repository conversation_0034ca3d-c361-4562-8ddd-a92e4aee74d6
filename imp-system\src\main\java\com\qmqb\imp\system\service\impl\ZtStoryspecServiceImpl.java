package com.qmqb.imp.system.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qmqb.imp.system.domain.ZtStoryspec;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.qmqb.imp.system.mapper.ZtStoryspecMapper;
import com.qmqb.imp.system.service.IZtStoryspecService;

import java.util.List;


/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@RequiredArgsConstructor
@Service
public class ZtStoryspecServiceImpl implements IZtStoryspecService {

    private final ZtStoryspecMapper baseMapper;

    @Override
    public List<ZtStoryspec> selectByStoryId(List<Long> storyIdList) {
        return baseMapper.selectList(new LambdaQueryWrapper<ZtStoryspec>()
            .in(ZtStoryspec::getStory, storyIdList));
    }
}
