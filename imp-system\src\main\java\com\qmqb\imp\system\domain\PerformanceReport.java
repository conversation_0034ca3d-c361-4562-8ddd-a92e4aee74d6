package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;


import java.util.Date;


/**
 * 绩效分析报告对象 tb_performance_report
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@TableName("tb_performance_report")
public class PerformanceReport {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 年份
     */
    private String year;
    /**
     * 月份
     */
    private String month;
    /**
     * 报告类型 （0-个人 1-小组 2-岗位）
     */
    private String reportType;
    /**
     * 个人名称
     */
    private String userName;
    /**
     * 小组名称
     */
    private String groupName;
    /**
     * 岗位名称
     */
    private String postName;
    /**
     * 报告地址
     */
    private String reportUrl;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
