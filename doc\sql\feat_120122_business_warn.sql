create table tb_monitor_event_record
(
    id             bigint auto_increment comment '主键ID'
        primary key,
    event_id       bigint                 not null comment '事件id',
    app_group_id   bigint                 null comment '应用组ID',
    app_group_name varchar(128)           null comment '应用组名称',
    app_code       varchar(64)            null comment '应用编码',
    app_name       varchar(128)           null comment '应用名称',
    server_name    varchar(128)           null comment '所属服务器名',
    app_addr       varchar(128)           null comment '应用地址',
    event_code     varchar(64)            null comment '事件编码',
    event_name     varchar(128)           null comment '事件名称',
    event_content  text                   null comment '事件内容',
    event_type     varchar(8)             null comment '事件类型: 0-运维故障, 1-应用故障, 2-业务系统故障',
    alert_type     varchar(8)             null comment '告警类型: 0-故障发生告警, 1-故障恢复告警',
    event_level    varchar(8)             null comment '事件等级: 0-p0, 1-p1, 2-p2, 3-p3, 4-p4',
    status         varchar(8) default '0' null comment '处理状态: 0-未处理, 1-处理中, 2-已处理',
    result         text                   null comment '处理结果',
    handle_time    datetime               null comment '处理时间',
    handler        varchar(64)            null comment '处理人',
    create_time    datetime               null comment '创建时间',
    create_by      varchar(30)            null comment '创建人',
    update_time    datetime               null comment '更新时间',
    update_by      varchar(30)            null comment '修改人',
    del_flag       tinyint    default 0   null comment '删除标志（0代表存在 2代表删除）',
    constraint index_event_id_uindex
        unique (event_id)
)
    comment '监控预警记录表' charset = utf8mb4;

create index index_app_group_name
    on tb_monitor_event_record (app_group_name);

create index index_app_name
    on tb_monitor_event_record (app_name);

create index index_event_name
    on tb_monitor_event_record (event_name);


create table tb_monitor_event_dept
(
    event_id bigint not null comment '事件id',
    dept_id  bigint not null comment '部门id',
    primary key (event_id, dept_id)
)
    comment '预警事件和部门关联表';

create index index_dept_id
    on tb_monitor_event_dept (dept_id);

begin;


INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1913120349020090369, '业务预警', 1621061518097416194, 7, 'business', 'warn/business/index', NULL, 1, 0, 'C', '0', '0', 'system:monitorEventRecord:list', 'time-range', 'admin', '2025-04-18 14:40:10', 'admin', '2025-04-18 18:02:08', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1914200904584204289, '处理按钮', 1913120349020090369, 0, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:monitorEventRecord:handle', '#', 'admin', '2025-04-21 14:13:54', 'admin', '2025-04-21 14:13:54', '');
UPDATE `sys_menu` SET `menu_name` = '预警管理', `parent_id` = 0, `order_num` = 9, `path` = 'warn', `component` = NULL, `query_param` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = NULL, `icon` = 'edit', `create_by` = 'admin', `create_time` = '2023-02-02 16:22:38', `update_by` = 'admin', `update_time` = '2025-04-18 14:27:29', `remark` = '' WHERE `menu_id` = 1621061518097416194;

INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1914575688601038849, '事件类型', 'event_type', '0', 'admin', '2025-04-22 15:03:10', 'admin', '2025-04-22 15:03:10', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1914577751384911873, '事件等级', 'event_level', '0', 'admin', '2025-04-22 15:11:22', 'admin', '2025-04-22 15:11:22', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1914578840020709378, '处理状态', 'handle_status', '0', 'admin', '2025-04-22 15:15:41', 'admin', '2025-04-22 15:15:41', NULL);

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1914575835108077570, 0, '运维故障', '0', 'event_type', NULL, 'default', 'N', '0', 'admin', '2025-04-22 15:03:45', 'admin', '2025-04-22 15:03:45', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1914575897787756546, 1, '应用故障', '1', 'event_type', NULL, 'default', 'N', '0', 'admin', '2025-04-22 15:04:00', 'admin', '2025-04-22 15:04:00', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1914575948639498242, 2, '业务系统故障', '2', 'event_type', NULL, 'default', 'N', '0', 'admin', '2025-04-22 15:04:12', 'admin', '2025-04-22 15:04:42', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1914576034194911234, 3, '业务指标故障', '3', 'event_type', NULL, 'default', 'N', '0', 'admin', '2025-04-22 15:04:32', 'admin', '2025-04-22 15:04:32', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1914577903927554050, 0, 'P0', '0', 'event_level', NULL, 'default', 'N', '0', 'admin', '2025-04-22 15:11:58', 'admin', '2025-04-22 15:11:58', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1914577939734327298, 1, 'P1', '1', 'event_level', NULL, 'default', 'N', '0', 'admin', '2025-04-22 15:12:07', 'admin', '2025-04-22 15:12:07', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1914577982696583169, 2, 'P2', '2', 'event_level', NULL, 'default', 'N', '0', 'admin', '2025-04-22 15:12:17', 'admin', '2025-04-22 15:12:17', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1914578962871873537, 0, '待处理', '0', 'handle_status', NULL, 'default', 'N', '0', 'admin', '2025-04-22 15:16:11', 'admin', '2025-04-22 15:16:11', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1914579004521312258, 1, '处理中', '1', 'handle_status', NULL, 'default', 'N', '0', 'admin', '2025-04-22 15:16:20', 'admin', '2025-04-22 15:16:20', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1914579153813368833, 2, '已处理', '2', 'handle_status', NULL, 'default', 'N', '0', 'admin', '2025-04-22 15:16:56', 'admin', '2025-04-22 15:16:56', NULL);



commit;
