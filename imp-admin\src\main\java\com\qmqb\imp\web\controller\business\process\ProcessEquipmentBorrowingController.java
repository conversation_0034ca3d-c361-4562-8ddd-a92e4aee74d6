package com.qmqb.imp.web.controller.business.process;

import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.system.domain.vo.process.ProcessEquipmentBorrowingVo;
import com.qmqb.imp.system.domain.bo.process.ProcessEquipmentBorrowingBo;
import com.qmqb.imp.system.service.process.IProcessEquipmentBorrowingService;
import com.qmqb.imp.common.core.page.TableDataInfo;

/**
 * 设备借用流程
 *
 * <AUTHOR>
 * @date 2024-12-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/processEquipmentBorrowing")
public class ProcessEquipmentBorrowingController extends BaseController {

    private final IProcessEquipmentBorrowingService iProcessEquipmentBorrowingService;

    /**
     * 查询设备借用流程列表
     */
    @SaCheckPermission("system:processEquipmentBorrowing:list")
    @GetMapping("/list")
    public TableDataInfo<ProcessEquipmentBorrowingVo> list(ProcessEquipmentBorrowingBo bo, PageQuery pageQuery) {
        return iProcessEquipmentBorrowingService.queryPageList(bo, pageQuery);
    }


}
