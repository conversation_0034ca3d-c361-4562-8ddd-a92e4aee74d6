package com.qmqb.imp.common.utils;

import com.qmqb.imp.common.constant.PerformanceFeedbackConstants;
import com.qmqb.imp.common.enums.PerformanceFeedbackAuditStatusEnum;
import com.qmqb.imp.common.enums.PerformanceFeedbackDataSourceEnum;
import com.qmqb.imp.common.enums.PerformanceFeedbackSubmitStatusEnum;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;

/**
 * 绩效反馈工具类
 * <p>
 * 提供绩效反馈相关的工具方法，包括：
 * - 状态验证
 * - 指标名称获取
 * - 业务逻辑判断
 * </p>
 *
 * <AUTHOR>
 */
public class PerformanceFeedbackUtils {

    /**
     * 根据指标编码获取指标名称
     *
     * @param indicatorCode 指标编码
     * @return 指标名称，如果不存在则返回默认名称
     */
    public static String getIndicatorName(String indicatorCode) {
        if (StringUtils.isBlank(indicatorCode)) {
            return PerformanceFeedbackConstants.UNKNOWN_INDICATOR_NAME;
        }

        try {
            return PerformanceIndicatorEnum.valueOf(indicatorCode.toUpperCase()).getName();
        } catch (IllegalArgumentException e) {
            return PerformanceFeedbackConstants.UNKNOWN_INDICATOR_NAME;
        }
    }

    /**
     * 生成事件标题
     *
     * @param indicatorName 指标名称
     * @param level         等级
     * @return 事件标题
     */
    public static String generateEventTitle(String indicatorName, String level) {
        return String.format(PerformanceFeedbackConstants.EVENT_TITLE_FORMAT, indicatorName, level);
    }

    /**
     * 生成推荐原因
     *
     * @param indicatorName 指标名称
     * @param level         等级
     * @param logContent    日志内容
     * @return 推荐原因
     */
    public static String generateRecommendedReason(String indicatorName, String level, String logContent) {
        StringBuilder reason = new StringBuilder();
        reason.append(String.format(PerformanceFeedbackConstants.RECOMMENDED_REASON_FORMAT, indicatorName, level));

        if (StringUtils.isNotBlank(logContent)) {
            reason.append(String.format(PerformanceFeedbackConstants.RECOMMENDED_REASON_DETAIL_FORMAT, logContent));
        }

        return reason.toString();
    }

    /**
     * 验证提交状态是否有效
     *
     * @param submitStatus 提交状态
     * @return 是否有效
     */
    public static boolean isValidSubmitStatus(String submitStatus) {
        return PerformanceFeedbackSubmitStatusEnum.isValidStatus(submitStatus);
    }

    /**
     * 验证审核状态是否有效
     *
     * @param auditStatus 审核状态
     * @return 是否有效
     */
    public static boolean isValidAuditStatus(String auditStatus) {
        return PerformanceFeedbackAuditStatusEnum.isValidStatus(auditStatus);
    }

    /**
     * 验证数据来源是否有效
     *
     * @param dataSource 数据来源
     * @return 是否有效
     */
    public static boolean isValidDataSource(String dataSource) {
        return PerformanceFeedbackDataSourceEnum.isValidSource(dataSource);
    }

    /**
     * 判断是否为最终审核状态
     *
     * @param auditStatus 审核状态
     * @return 是否为最终状态
     */
    public static boolean isFinalAuditStatus(String auditStatus) {
        return PerformanceFeedbackAuditStatusEnum.isFinalStatus(auditStatus);
    }

    /**
     * 判断是否可以提交审核
     *
     * @param submitStatus 提交状态
     * @return 是否可以提交
     */
    public static boolean canSubmit(String submitStatus) {
        return PerformanceFeedbackSubmitStatusEnum.NOT_SUBMITTED.getName().equals(submitStatus) ||
            PerformanceFeedbackSubmitStatusEnum.PENDING_RESUBMIT.getName().equals(submitStatus);
    }

    /**
     * 判断是否可以撤回
     *
     * @param submitStatus 提交状态
     * @param auditStatus  审核状态
     * @return 是否可以撤回
     */
    public static boolean canWithdraw(String submitStatus, String auditStatus) {
        return PerformanceFeedbackSubmitStatusEnum.SUBMITTED.getName().equals(submitStatus) &&
            PerformanceFeedbackAuditStatusEnum.NOT_AUDITED.getName().equals(auditStatus);
    }

    /**
     * 判断是否可以审核
     *
     * @param submitStatus 提交状态
     * @param auditStatus  审核状态
     * @return 是否可以审核
     */
    public static boolean canAudit(String submitStatus, String auditStatus) {
        return PerformanceFeedbackSubmitStatusEnum.SUBMITTED.getName().equals(submitStatus) &&
            PerformanceFeedbackAuditStatusEnum.NOT_AUDITED.getName().equals(auditStatus);
    }

    /**
     * 获取状态显示名称
     *
     * @param submitStatus 提交状态
     * @param auditStatus  审核状态
     * @return 状态显示名称
     */
    public static String getStatusDisplayName(String submitStatus, String auditStatus) {
        if (PerformanceFeedbackSubmitStatusEnum.NOT_SUBMITTED.getName().equals(submitStatus)) {
            return "未提交";
        } else if (PerformanceFeedbackSubmitStatusEnum.PENDING_RESUBMIT.getName().equals(submitStatus)) {
            return "待重提";
        } else if (PerformanceFeedbackSubmitStatusEnum.SUBMITTED.getName().equals(submitStatus)) {
            if (PerformanceFeedbackAuditStatusEnum.NOT_AUDITED.getName().equals(auditStatus)) {
                return "未审核";
            } else if (PerformanceFeedbackAuditStatusEnum.APPROVED.getName().equals(auditStatus)) {
                return "同意";
            } else if (PerformanceFeedbackAuditStatusEnum.REJECTED.getName().equals(auditStatus)) {
                return "拒绝";
            }
        }
        return "未知状态";
    }
}
