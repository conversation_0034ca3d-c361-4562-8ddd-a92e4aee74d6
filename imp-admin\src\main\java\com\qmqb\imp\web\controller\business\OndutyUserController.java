package com.qmqb.imp.web.controller.business;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;

import com.qmqb.imp.common.excel.ExcelResult;
import com.qmqb.imp.system.domain.bo.OndutyUserImportVo;
import com.qmqb.imp.system.service.OnDutyService;
import lombok.RequiredArgsConstructor;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;

import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.vo.OndutyUserVo;
import com.qmqb.imp.system.domain.bo.OndutyUserBo;
import com.qmqb.imp.system.service.IOndutyUserService;
import com.qmqb.imp.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 值班
 *
 * <AUTHOR>
 * @date 2023-04-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/ondutyUser")
public class OndutyUserController extends BaseController {

    private final IOndutyUserService iOndutyUserService;

    private final OnDutyService onDutyService;

    /**
     * 查询值班列表
     */
    @SaCheckPermission("system:ondutyUser:list")
    @GetMapping("/list")
    public TableDataInfo<OndutyUserVo> list(OndutyUserBo bo, PageQuery pageQuery) {
        return iOndutyUserService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出值班列表
     */
    @SaCheckPermission("system:ondutyUser:export")
    @Log(title = "值班", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OndutyUserBo bo, HttpServletResponse response) {
        List<OndutyUserVo> list = iOndutyUserService.queryList(bo);
        ExcelUtil.exportExcel(list, "值班", OndutyUserVo.class, response);
    }

    /**
     * 获取值班详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:ondutyUser:query")
    @GetMapping("/{id}")
    public R<OndutyUserVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long id) {
        return R.ok(iOndutyUserService.queryById(id));
    }

    /**
     * 新增值班
     */
    @SaCheckPermission("system:ondutyUser:add")
    @Log(title = "值班", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OndutyUserBo bo) {
        return toAjax(iOndutyUserService.insertByBo(bo));
    }

    /**
     * 修改值班
     */
    @SaCheckPermission("system:ondutyUser:edit")
    @Log(title = "值班", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OndutyUserBo bo) {
        return toAjax(iOndutyUserService.updateByBo(bo));
    }

    /**
     * 删除值班
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:ondutyUser:remove")
    @Log(title = "值班", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(onDutyService.delete(Arrays.asList(ids), true));
    }

    /**
     * 导入数据
     *
     * @param file 导入文件
     */
    @Log(title = "值班管理", businessType = BusinessType.IMPORT)
    @SaCheckPermission("system:ondutyUser:import")
    @PostMapping(value = "/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> importData(@RequestPart("file") MultipartFile file) throws Exception {
        ExcelResult<OndutyUserImportVo> excelResult = ExcelUtil.importExcel(file.getInputStream(), OndutyUserImportVo.class, true);
        List<OndutyUserImportVo> volist = excelResult.getList();
        onDutyService.importUser(volist);
        return R.ok(excelResult.getAnalysis());
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "值班数据", OndutyUserImportVo.class, response);
    }


}
