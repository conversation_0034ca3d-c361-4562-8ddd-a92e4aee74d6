package com.qmqb.imp.system.mapper.performance;

import com.qmqb.imp.system.domain.performance.PerformIndicatorResult;
import com.qmqb.imp.system.domain.vo.PerformIndicatorResultVo;
import com.qmqb.imp.common.core.mapper.BaseMapperPlus;

/**
 * 绩效指标原因Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface PerformIndicatorResultMapper extends BaseMapperPlus<PerformIndicatorResultMapper, PerformIndicatorResult, PerformIndicatorResultVo> {

}
