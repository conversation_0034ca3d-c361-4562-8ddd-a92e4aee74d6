package com.qmqb.imp.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.SlowQueryInfo;
import com.qmqb.imp.system.domain.bo.SlowQueryInfoBo;
import com.qmqb.imp.system.domain.vo.SlowQueryInfoVo;
import com.qmqb.imp.system.domain.vo.SlowQueryVO;
import org.apache.ibatis.annotations.Param;

/**
 * 慢sql信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@DS(DataSource.SLOWSQL)
public interface SlowQueryInfoMapper extends BaseMapperPlus<SlowQueryInfoMapper, SlowQueryInfo, SlowQueryInfoVo> {

    /**
     * 分页获取慢sql
     *
     * @param build
     * @param bo
     * @return
     */
    Page<SlowQueryInfoVo> slowSqlPage(@Param("page") Page<SlowQueryVO> build, @Param("bo") SlowQueryInfoBo bo);
}
