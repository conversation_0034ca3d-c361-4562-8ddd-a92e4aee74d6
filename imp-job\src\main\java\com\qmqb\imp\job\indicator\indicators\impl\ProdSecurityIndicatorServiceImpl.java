package com.qmqb.imp.job.indicator.indicators.impl;

import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackBo;
import com.qmqb.imp.system.domain.performance.PerformanceFeedback;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.domain.vo.performance.PerformanceFeedbackVo;
import com.qmqb.imp.system.service.indicator.IPerformanceFeedbackMainService;
import com.qmqb.imp.system.service.indicator.IPerformanceFeedbackService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 生产操作管理
 *
 * <AUTHOR>
 */
@Service
public class ProdSecurityIndicatorServiceImpl implements IndicatorLevelCalcService {

    @Resource
    private IPerformanceFeedbackService performanceFeedbackService;

    @Override
    public String getIndicatorCode() {
        return "";
    }


    @Override
    public IndicatorCalcResult calcLevel(String nickName, Integer month, List<TrackWorkResultVO> trackWorkResults) {
        Optional<TrackWorkResultVO> userWorkResultOpt = trackWorkResults.stream()
            .filter(r -> nickName.equals(r.getWorkUsername()))
            .findFirst();

        if (!userWorkResultOpt.isPresent()) {
            String indicatorName = "未知指标";
            try {
                indicatorName = PerformanceIndicatorEnum.valueOf(getIndicatorCode().toUpperCase()).getName();
            } catch (IllegalArgumentException e) {
                // Ignore if enum not found
            }
            return new IndicatorCalcResult(ScoreLevelEnum.SCORE_C.getCode(),
                String.format("员工[%s]在%s月份的%s数据未找到", nickName, month, indicatorName));
        }

        TrackWorkResultVO userWorkResult = userWorkResultOpt.get();
        List<PerformanceFeedback> data = new ArrayList<>();
        String level = calculateLevel(userWorkResult, nickName, trackWorkResults, data);
        String logContent = createLogContent(userWorkResult, nickName, level, data);
        return new IndicatorCalcResult(level, logContent);

    }

    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults, List<PerformanceFeedback> data) {
        PerformanceFeedbackBo performanceFeedbackBo = new PerformanceFeedbackBo();
        performanceFeedbackBo.setSecondaryIndicator(PerformanceIndicatorEnum.PROD_SECURITY_MANAGE.getCode());
        performanceFeedbackBo.setYear(workResult.getWorkYear());
        performanceFeedbackBo.setMonth(workResult.getWorkMonth());
        performanceFeedbackBo.setNickName(nickName);
        performanceFeedbackBo.setIsCanceled(0);
        List<PerformanceFeedback> feedbackVos = performanceFeedbackService.queryApprovalList(performanceFeedbackBo);
        data.addAll(feedbackVos);
        return calculateLevelFromFeedbacks(feedbackVos);
    }
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level, List<PerformanceFeedback> data) {
        return data.stream().map(PerformanceFeedback::getRecommendedReason).collect(Collectors.joining(","));
    }

    @Override
    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults) {
        return "";
    }

    @Override
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level) {
        return "";
    }
}
