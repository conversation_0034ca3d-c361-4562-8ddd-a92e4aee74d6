package com.qmqb.imp.job.config;

import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.PersonTypeEnum;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 角色-指标配置类
 *
 * <AUTHOR>
 */
@Component
public class RoleIndicatorConfig {
    private static final Map<Integer, List<String>> ROLE_INDICATOR_MAP = new HashMap<>();

    static {
        // 开发岗
        ROLE_INDICATOR_MAP.put(PersonTypeEnum.DEVELOPER.getType(), Arrays.asList(
            PerformanceIndicatorEnum.ATTENDANCE.getCode(),
            PerformanceIndicatorEnum.CODE_LINES.getCode(),
            PerformanceIndicatorEnum.WORK_DOCS.getCode(),
            PerformanceIndicatorEnum.ZENTAO_TASKS.getCode(),
            PerformanceIndicatorEnum.PERFORMANCE_WARN.getCode(),
            PerformanceIndicatorEnum.PROD_FAULT.getCode(),
            PerformanceIndicatorEnum.DELIVERY_TIMELINESS.getCode(),
            PerformanceIndicatorEnum.DEV_SPEC.getCode(),
            PerformanceIndicatorEnum.REVIEW_MEETING.getCode(),
            PerformanceIndicatorEnum.DEV_EFFICIENCY.getCode(),
            PerformanceIndicatorEnum.WORK_OBEY.getCode(),
            PerformanceIndicatorEnum.RISK_HANDLE.getCode(),
            PerformanceIndicatorEnum.WORK_RULES.getCode(),
            PerformanceIndicatorEnum.TEAMWORK.getCode()
        ));
        // 测试岗
        ROLE_INDICATOR_MAP.put(PersonTypeEnum.TESTER.getType(), Arrays.asList(
            PerformanceIndicatorEnum.ATTENDANCE.getCode(),
            PerformanceIndicatorEnum.BUG_COUNT.getCode(),
            PerformanceIndicatorEnum.CASE_EXECUTE_COUNT.getCode(),
            PerformanceIndicatorEnum.WORK_DOCS.getCode(),
            PerformanceIndicatorEnum.ZENTAO_TASKS.getCode(),
            PerformanceIndicatorEnum.PERFORMANCE_WARN.getCode(),
            PerformanceIndicatorEnum.PROD_FAULT.getCode(),
            PerformanceIndicatorEnum.DELIVERY_TIMELINESS.getCode(),
            PerformanceIndicatorEnum.DEV_SPEC.getCode(),
            PerformanceIndicatorEnum.REVIEW_MEETING.getCode(),
            PerformanceIndicatorEnum.DEV_EFFICIENCY.getCode(),
            PerformanceIndicatorEnum.WORK_OBEY.getCode(),
            PerformanceIndicatorEnum.RISK_HANDLE.getCode(),
            PerformanceIndicatorEnum.WORK_RULES.getCode(),
            PerformanceIndicatorEnum.TEAMWORK.getCode()
        ));
        // 运维岗
        ROLE_INDICATOR_MAP.put(PersonTypeEnum.OPERATOR.getType(), Arrays.asList(
            PerformanceIndicatorEnum.ATTENDANCE.getCode(),
            PerformanceIndicatorEnum.PLAN_MAINTAIN_COUNT.getCode(),
            PerformanceIndicatorEnum.WORK_DOCS.getCode(),
            PerformanceIndicatorEnum.ZENTAO_TASKS.getCode(),
            PerformanceIndicatorEnum.PERFORMANCE_WARN.getCode(),
            PerformanceIndicatorEnum.PROD_FAULT.getCode(),
            PerformanceIndicatorEnum.DELIVERY_TIMELINESS.getCode(),
            PerformanceIndicatorEnum.DEV_SPEC.getCode(),
            PerformanceIndicatorEnum.REVIEW_MEETING.getCode(),
            PerformanceIndicatorEnum.PROD_SECURITY_MANAGE.getCode(),
            PerformanceIndicatorEnum.PROD_OPERATION_MANAGE.getCode(),
            PerformanceIndicatorEnum.WORK_OBEY.getCode(),
            PerformanceIndicatorEnum.RISK_HANDLE.getCode(),
            PerformanceIndicatorEnum.WORK_RULES.getCode(),
            PerformanceIndicatorEnum.TEAMWORK.getCode()
        ));
        // 项目经理岗
        ROLE_INDICATOR_MAP.put(PersonTypeEnum.PROJECT_MANAGER.getType(), Arrays.asList(
            PerformanceIndicatorEnum.ATTENDANCE.getCode(),
            PerformanceIndicatorEnum.PROJECT_RELEASE_COUNT.getCode(),
            PerformanceIndicatorEnum.WORK_DOCS.getCode(),
            PerformanceIndicatorEnum.ZENTAO_TASKS.getCode(),
            PerformanceIndicatorEnum.PERFORMANCE_WARN.getCode(),
            PerformanceIndicatorEnum.PROD_FAULT.getCode(),
            PerformanceIndicatorEnum.DELIVERY_TIMELINESS.getCode(),
            PerformanceIndicatorEnum.DEV_SPEC.getCode(),
            PerformanceIndicatorEnum.REVIEW_MEETING.getCode(),
            PerformanceIndicatorEnum.ORGANIZATIONAL_MEETINGS.getCode(),
            PerformanceIndicatorEnum.RESOURCE_MANAGE.getCode(),
            PerformanceIndicatorEnum.WORK_OBEY.getCode(),
            PerformanceIndicatorEnum.RISK_HANDLE.getCode(),
            PerformanceIndicatorEnum.WORK_RULES.getCode(),
            PerformanceIndicatorEnum.TEAMWORK.getCode()
        ));
        // 技术经理岗
        ROLE_INDICATOR_MAP.put(PersonTypeEnum.TECHNICAL_MANAGER.getType(), Arrays.asList(
            PerformanceIndicatorEnum.GROUP_AVG_ATTENDANCE.getCode(),
            PerformanceIndicatorEnum.GROUP_RESULT_OUTPUT.getCode(),
            PerformanceIndicatorEnum.GROUP_AVG_WORK_DOCS.getCode(),
            PerformanceIndicatorEnum.GROUP_AVG_ZENTAO_TASKS.getCode(),
            PerformanceIndicatorEnum.GROUP_PERFORMANCE_WARN.getCode(),
            PerformanceIndicatorEnum.DELIVERY_TIMELINESS.getCode(),
            PerformanceIndicatorEnum.DEV_SPEC.getCode(),
            PerformanceIndicatorEnum.MEETING_ATTENDANCE.getCode(),
            PerformanceIndicatorEnum.WORK_EFFICIENCY_MANAGE.getCode(),
            PerformanceIndicatorEnum.WORK_OBEY.getCode(),
            PerformanceIndicatorEnum.RISK_HANDLE.getCode(),
            PerformanceIndicatorEnum.WORK_RULES.getCode(),
            PerformanceIndicatorEnum.TEAMWORK.getCode()
        ));
    }

    public List<String> getIndicatorsByRole(PersonTypeEnum personTypeEnum) {
        return ROLE_INDICATOR_MAP.getOrDefault(personTypeEnum.getType(), Collections.emptyList());
    }
}
