package com.qmqb.imp.app.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qmqb.imp.common.jackson.BigDecimalZeroSerializer;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 绩效报告视图对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Data
@Builder
public class PerformanceReportVO {
    /**
     * 姓名|部门
     */
    private String name;
    /**
     * 工时
     */
    private ManHour manHour;
    /**
     * 出勤天数
     */
    @JsonSerialize(using = BigDecimalZeroSerializer.class)
    private BigDecimal attendanceDay;
    /**
     * 文档数
     */
    @JsonSerialize(using = BigDecimalZeroSerializer.class)
    private BigDecimal docs;
    /**
     * 完成任务数
     */
    @JsonSerialize(using = BigDecimalZeroSerializer.class)
    private BigDecimal completedTasks;
    /**
     * 代码数
     */
    @JsonSerialize(using = BigDecimalZeroSerializer.class)
    private BigDecimal codes;
    /**
     * 用例数
     */
    @JsonSerialize(using = BigDecimalZeroSerializer.class)
    private BigDecimal cases;
    /**
     * 创建bug数
     */
    @JsonSerialize(using = BigDecimalZeroSerializer.class)
    private BigDecimal createdBugs;
    /**
     * 处理bug数
     */
    @JsonSerialize(using = BigDecimalZeroSerializer.class)
    private BigDecimal resolvedBugs;
    /**
     * 预警处理
     */
    private Warning warning;

    @Data
    @Builder
    public static class ManHour {
        /**
         * 总工时
         */
        @JsonSerialize(using = BigDecimalZeroSerializer.class)
        private BigDecimal total;
        /**
         * 日工时
         */
        @JsonSerialize(using = BigDecimalZeroSerializer.class)
        private BigDecimal day;
    }

    @Data
    @Builder
    public static class Warning {
        /**
         * 总数
         */
        @JsonSerialize(using = BigDecimalZeroSerializer.class)
        private BigDecimal total;
        /**
         * 未处理数
         */
        @JsonSerialize(using = BigDecimalZeroSerializer.class)
        private BigDecimal unprocessed;
    }
}
