package com.qmqb.imp.system.domain.bo.performance;

import com.qmqb.imp.common.core.domain.BaseEntity;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 绩效指标分类业务对象 tb_performance_indicator_category
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PerformanceIndicatorCategoryBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 绩效主表ID
     */
    @NotNull(message = "绩效主表ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long performanceId;

    /**
     * 分类编码
     */
    @NotBlank(message = "分类编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String categoryCode;

    /**
     * 分类名称
     */
    @NotBlank(message = "分类名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String categoryName;

    /**
     * 分类评分等级S/A/B/C/D
     */
    @NotBlank(message = "分类评分等级不能为空", groups = {AddGroup.class, EditGroup.class})
    private String categoryLevel;

    /**
     * 分类权重（百分比，如30表示30%）
     */
    @NotNull(message = "分类权重不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer categoryWeight;

    /**
     * 分类得分（数值，如85.5）
     */
    private Double categoryScore;

    /**
     * 分类计算日志
     */
    private String categoryLog;
} 