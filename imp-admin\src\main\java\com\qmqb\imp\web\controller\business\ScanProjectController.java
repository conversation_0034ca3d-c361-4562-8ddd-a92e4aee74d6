package com.qmqb.imp.web.controller.business;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.dto.CodeBlockerStatisticsDTO;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.bo.ScanProjectBo;
import com.qmqb.imp.system.domain.vo.ScanProjectVo;
import com.qmqb.imp.system.service.IScanProjectService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 扫描项目记录
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/scanProject")
public class ScanProjectController extends BaseController {

    private final IScanProjectService iScanProjectService;

    /**
     * 查询扫描项目记录列表
     */
    @SaCheckPermission("system:scanProject:list")
    @GetMapping("/list")
    public TableDataInfo<ScanProjectVo> list(ScanProjectBo bo, PageQuery pageQuery) {
        return iScanProjectService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出扫描项目记录列表
     */
    @SaCheckPermission("system:scanProject:export")
    @Log(title = "扫描项目记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ScanProjectBo bo, HttpServletResponse response) {
        List<ScanProjectVo> list = iScanProjectService.queryList(bo);
        ExcelUtil.exportExcel(list, "扫描项目记录", ScanProjectVo.class, response);
    }

    /**
     * 获取扫描项目记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:scanProject:query")
    @GetMapping("/{id}")
    public R<ScanProjectVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long id) {
        return R.ok(iScanProjectService.queryById(id));
    }

    /**
     * 严重问题统计列表
     *
     * @param year 年份
     * @param month 月份
     * @return
     */
    @SaIgnore
    @GetMapping("/blocker/statistics/list")
    public R<List<CodeBlockerStatisticsDTO>> blockerStatisticsList(Integer year, Integer month) {
        return R.ok(iScanProjectService.blockerStatisticsList(year,month));
    }
}
