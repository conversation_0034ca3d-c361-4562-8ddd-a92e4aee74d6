package com.qmqb.imp.web.controller.performance;


import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.system.domain.bo.performance.PerformanceTutoringBo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceTutoringQueryBo;
import com.qmqb.imp.system.domain.vo.performance.PerformanceTutoringVo;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.indicator.IPerformanceTutoringService;
import lombok.RequiredArgsConstructor;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.core.page.TableDataInfo;

/**
 * 绩效辅导
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/performanceTutoring")
public class PerformanceTutoringController extends BaseController {

    private final IPerformanceTutoringService iPerformanceTutoringService;

    private final ISysUserService sysUserService;

    /**
     * 查询绩效辅导列表
     */
    @SaCheckPermission("system:performanceTutoring:list")
    @GetMapping("/list")
    public TableDataInfo<PerformanceTutoringVo> list(@Validated PerformanceTutoringQueryBo bo, PageQuery pageQuery) {
        return iPerformanceTutoringService.queryPageList(bo, pageQuery);
    }


    /**
     * 获取绩效辅导详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:performanceTutoring:query")
    @GetMapping("/{id}")
    public R<PerformanceTutoringVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iPerformanceTutoringService.queryById(id));
    }

    /**
     * 绩效辅导登记
     * @param bo
     * @return
     */
    @SaCheckPermission("system:performanceTutoring:tutoring")
    @PostMapping("/tutoring")
    public R<Void> tutoring(@RequestBody @Validated(EditGroup.class) PerformanceTutoringBo bo) {
        return toAjax(iPerformanceTutoringService.tutoring(bo));
    }

    /**
     * 绩效建议
     * @param bo
     * @return
     */
    @SaCheckPermission("system:performanceTutoring:suggest")
    @PostMapping("/suggest")
    public R<Void> suggest(@RequestBody @Validated(AddGroup.class) PerformanceTutoringBo bo) {
        return toAjax(iPerformanceTutoringService.suggest(bo));
    }

    /**
     * 获取最后编辑辅导人，如果没有返回当前登录人
     * @return
     */
    @GetMapping("/getLastEditTotur")
    public R<String> getLastEditTotur() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            return R.fail("用户未登录");
        }
        PerformanceTutoringVo performanceTutoringVo = iPerformanceTutoringService.getLastEditTotur(loginUser.getUsername());
        if (performanceTutoringVo == null) {
            SysUser sysUser = sysUserService.selectUserById(loginUser.getUserId());
            return R.ok("当前登录人",sysUser.getNickName());
        }
        return R.ok("上一次填写的辅导人",performanceTutoringVo.getTutor());
    }



}
