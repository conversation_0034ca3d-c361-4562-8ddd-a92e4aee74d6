package com.qmqb.imp.system.service;

import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.bo.ReleaseRecordBo;
import com.qmqb.imp.system.domain.vo.ReleaseRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 发布版本记录Service接口
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
public interface IReleaseRecordService {

    /**
     * 查询发布版本记录
     * @param id 发布版本记录ID
     * @return 发布版本记录
     */
    ReleaseRecordVo queryById(Long id);

    /**
     * 查询发布版本记录列表
     * @param bo 查询条件
     * @param pageQuery 分页查询条件
     * @return 发布版本记录列表
     */
    TableDataInfo<ReleaseRecordVo> queryPageList(ReleaseRecordBo bo, PageQuery pageQuery);

    /**
     * 查询发布版本记录列表
     * @param bo 查询条件
     * @return 发布版本记录列表
     */
    List<ReleaseRecordVo> queryList(ReleaseRecordBo bo);

    /**
     * 新增发布版本记录
     * @param bo 新增条件
     * @return 是否成功
     */
    Boolean insertByBo(ReleaseRecordBo bo);

    /**
     * 修改发布版本记录
     * @param bo 修改条件
     * @return 是否成功
     */
    Boolean updateByBo(ReleaseRecordBo bo);
}
