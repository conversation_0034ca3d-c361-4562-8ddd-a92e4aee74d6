package com.qmqb.imp.job.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.google.common.collect.Lists;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.enums.WarnCodeEnum;
import com.qmqb.imp.system.domain.bo.WarnContentParamsBo;
import com.qmqb.imp.system.domain.vo.SysUserExportVo;
import com.qmqb.imp.system.domain.vo.TaskVO;
import com.qmqb.imp.system.domain.vo.WarnConfigVo;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.IWarnConfigService;
import com.qmqb.imp.system.service.IZtTaskService;
import com.qmqb.imp.system.service.warn.ISendWarnService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2025-05-06 16:15
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class DoingTaskTimeoutWarnService {

    private final IWarnConfigService warnConfigService;

    private final IZtTaskService taskService;

    private final ISendWarnService sendWarnService;

    private final ISysUserService sysUserService;


    @TraceId("进行中的任务10天没更新预警")
    @XxlJob("doingTaskTimeoutWarnJobHandler")
    public ReturnT<String> doingTaskTimeoutWarnJobHandler(String param) {
        try {
            XxlJobLogger.log("开始执行进行中的任务10天没更新预警定时任务...");
            log.info("开始执行进行中的任务10天没更新预警定时任务...");
            StopWatch sw = new StopWatch();
            sw.start();
            List<WarnConfigVo> warnConfigVos = warnConfigService.listByWarnCodes(Arrays.asList(
                WarnCodeEnum.TASK_WARN_DOING_NOT_FINISH_P2.getCode(), WarnCodeEnum.MANAGE_WARN_DOING_NOT_FINISH_P2.getCode()));
            if (CollUtil.isEmpty(warnConfigVos)) {
                XxlJobLogger.log("未配置预警");
                log.info("未配置预警");
                return ReturnT.SUCCESS;
            }
            //需要预警的成员
            List<String> timeoutUsers = taskService.doingTaskTimeout().stream().map(TaskVO::getTaskStarter).collect(Collectors.toList());
            if (CollUtil.isEmpty(timeoutUsers)) {
                XxlJobLogger.log("没有需要预警的成员");
                log.info("没有需要预警的成员");
                return ReturnT.SUCCESS;
            }
            List<SysUserExportVo> sysUserExportVos = sysUserService.listExportVoByztUserNames(timeoutUsers);
            //需要预警的组长
            Set<String> leadNames =  sysUserExportVos.stream().map(SysUserExportVo::getLeader).collect(Collectors.toSet());
            List<SysUserExportVo> leadUserExportVos = sysUserService.listExportVoByNickNames(leadNames);
            Map<String, List<SysUserExportVo>> leadMemberMap = sysUserExportVos.stream().collect(Collectors.groupingBy(SysUserExportVo::getLeader));
            //发送组员预警
            sendWarnService.sendWarn(IdWorker.getId(),WarnCodeEnum.TASK_WARN_DOING_NOT_FINISH_P2.getCode(),null,sysUserExportVos);
            //发送组长预警
            leadUserExportVos.forEach(leadUserExportVo -> {
                List<SysUserExportVo> memberList =  leadMemberMap.get(leadUserExportVo.getNickName()).stream()
                    .filter(sysUserExportVo -> !sysUserExportVo.getNickName().equals(leadUserExportVo.getNickName()))
                    .collect(Collectors.toList());
                if (memberList.size() <= 0) {
                    return;
                }
                List<WarnContentParamsBo> params = new ArrayList<>();
                WarnContentParamsBo peoples = new WarnContentParamsBo("peoples", String.valueOf(memberList.size()));
                params.add(peoples);
                String names = memberList.stream().map(SysUserExportVo::getNickName).collect(Collectors.joining("，"));
                WarnContentParamsBo members = new WarnContentParamsBo("members", names);
                params.add(members);
                List<SysUserExportVo> leadUserExportVoList = Lists.newArrayList(leadUserExportVo);
                sendWarnService.sendWarn(IdWorker.getId(), WarnCodeEnum.MANAGE_WARN_DOING_NOT_FINISH_P2.getCode(), params, leadUserExportVoList);
            });
            sw.stop();
            XxlJobLogger.log("进行中的任务10天没更新预警定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            log.info("进行中的任务10天没更新预警定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            return ReturnT.SUCCESS;
        }catch(Exception e){
            XxlJobLogger.log(e);
            log.error("进行中的任务10天没更新预警定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

}
