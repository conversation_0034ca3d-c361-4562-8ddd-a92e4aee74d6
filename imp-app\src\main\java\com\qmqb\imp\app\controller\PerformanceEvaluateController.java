package com.qmqb.imp.app.controller;

import com.qmqb.imp.app.domain.bo.PerformanceEvaluateBizBO;
import com.qmqb.imp.app.domain.bo.SubmitEvaluateBO;
import com.qmqb.imp.app.domain.vo.KeyValueVO;
import com.qmqb.imp.app.domain.vo.PerformanceEvaluateBizVO;
import com.qmqb.imp.app.domain.vo.PerformanceReportVO;
import com.qmqb.imp.app.service.PerformanceEvaluateBizService;
import com.qmqb.imp.common.core.domain.R;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 绩效点评
 *
 * <AUTHOR> Li
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/performance-evaluate")
public class PerformanceEvaluateController {

    private final PerformanceEvaluateBizService performanceEvaluateBizService;

    /**
     * 人员列表
     * key为姓名
     * value为用户ID
     * 查询个人绩效报告传用户ID
     *
     * @return 结果
     */
    @GetMapping("/people-select")
    public R<List<KeyValueVO>> peopleSelect() {
        return performanceEvaluateBizService.peopleSelect();
    }

    /**
     * 个人绩效报告
     *
     * @return 结果
     */
    @GetMapping("/personal-performance-report")
    public R<PerformanceReportVO> personalPerformanceReport(@Valid PerformanceEvaluateBizBO bo) {
        return performanceEvaluateBizService.personalPerformanceReport(bo);
    }

    /**
     * 点评详情
     *
     * @return 结果
     */
    @GetMapping("/evaluate-detail")
    public R<PerformanceEvaluateBizVO> detail(@Valid PerformanceEvaluateBizBO bo) {
        return performanceEvaluateBizService.detail(bo);
    }

    /**
     * 提交点评
     *
     * @return 结果
     */
    @PostMapping("/submit")
    public R<Void> submit(@Validated @RequestBody SubmitEvaluateBO bo) {
        return performanceEvaluateBizService.submit(bo);
    }

}
