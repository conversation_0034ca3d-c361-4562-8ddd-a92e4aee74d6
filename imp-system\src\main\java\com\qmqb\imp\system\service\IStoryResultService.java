package com.qmqb.imp.system.service;

import com.qmqb.imp.system.domain.vo.StoryResultVo;
import com.qmqb.imp.system.domain.bo.StoryResultBo;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.domain.PageQuery;

import java.util.Collection;

/**
 * 需求成果Service接口
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
public interface IStoryResultService {


    /**
     * 查询需求成果列表
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<StoryResultVo> queryPageList(StoryResultBo bo, PageQuery pageQuery);

    /**
     * 根据成果ID批量清空需求的成果关联信息
     * @param resultIds 成果ID集合
     * @return 是否成功
     */
    Boolean clearResultInfoByResultIds(Collection<Long> resultIds);

}
