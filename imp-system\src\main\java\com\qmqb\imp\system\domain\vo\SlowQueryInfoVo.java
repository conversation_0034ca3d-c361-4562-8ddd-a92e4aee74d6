package com.qmqb.imp.system.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 慢sql信息视图对象 tb_slow_query_info
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Data
public class SlowQueryInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 客户端IP
     */
    private String ip;
    /**
     * 最近执行开始时间
     */
    private LocalDateTime lastExecutionStartTime;

    /**
     * SQL哈希值（同类SQL标识）
     */
    private String sqlHash;


    /**
     * 慢查询规则标识<br>
     * returnNull：返回空值<br>
     * returnRowCounts：返回记录数过多<br>
     * parseRowCounts：解析记录数过多<br>
     * query_time：查询时间过长<br>
     * query_hot：慢查询且频繁
     */
    private String slowRule;

    /**
     * 告警级别（P0、P1、P2）
     */
    private String warnLevel;

    /**
     * 处理状态：NO=未处理，YES=已处理
     */
    private String processStatus;
    /**
     * 处理时间
     */
    private LocalDateTime processTime;
    /**
     * 处理人
     */
    private String processBy;
    /**
     * 处理结果
     */
    private String processResult;
    /**
     * AI分析结果（存储分析结论/优化建议）
     */
    private String analysisResult;
    /**
     * SQL文本
     */
    private String sqlText;

}
