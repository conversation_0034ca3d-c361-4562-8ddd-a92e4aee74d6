package com.qmqb.imp.system.domain.bo;

import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import lombok.Data;
import javax.validation.constraints.*;


/**
 * 绩效分析报告业务对象 tb_performance_report
 *
 * <AUTHOR>
 * @date 2025-06-25
 */

@Data
public class PerformanceReportBo{


    /**
     * 年份
     */
    @NotBlank(message = "年份不能为空", groups = { AddGroup.class, EditGroup.class })
    private String year;

    /**
     * 月份
     */
    @NotBlank(message = "月份不能为空", groups = { AddGroup.class, EditGroup.class })
    private String month;

    /**
     * 报告类型 （0-个人 1-小组 2-岗位）
     */
    @NotBlank(message = "报告类型 （0-个人 1-小组 2-岗位）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String reportType;

    /**
     * 个人名称
     */
    private String userName;

    /**
     * 小组名称
     */
    private String groupName;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 报告地址
     */
    @NotBlank(message = "报告地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String reportUrl;


}
