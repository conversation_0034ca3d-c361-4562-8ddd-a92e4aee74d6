package com.qmqb.imp.system.domain.performance;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 绩效反馈主表与绩效指标结果中间表实体
 * 对应表：tb_performance_feedback_main_indicator_result
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_performance_feedback_main_indicator_result")
public class PerformanceFeedbackMainIndicatorResult extends BaseEntity {

    /** 主键ID */
    @TableId(value = "id")
    private Long id;

    /** 绩效反馈主表ID */
    private Long mainId;

    /** 绩效指标结果ID */
    private Long indicatorResultId;
}
