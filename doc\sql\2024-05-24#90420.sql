SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ding_talk_process_instance
-- ----------------------------
DROP TABLE IF EXISTS `ding_talk_process_instance`;
CREATE TABLE `ding_talk_process_instance`  (
  `id` bigint(20) UNSIGNED NOT NULL COMMENT '主键',
  `title` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审批实例标题',
  `process_instance_id` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '审批实例ID',
  `business_id` varchar(24) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审批实例业务编号',
  `instance_type` tinyint(1) UNSIGNED NULL DEFAULT NULL COMMENT '审批单类型（0-内部管理系统发布，1-系统故障处理）',
  `user_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '发起人的用户id',
  `ding_talk_user_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发起人的钉钉userId',
  `dept_id` bigint(20) NULL DEFAULT NULL COMMENT '发起人的部门id',
  `ding_talk_dept_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发起人的钉钉部门id，-1表示根部门',
  `ding_talk_dept_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发起人的部门名称',
  `status` tinyint(1) UNSIGNED NULL DEFAULT NULL COMMENT '审批状态（1-审批中，2-审批完成，3-已撤销）',
  `result` tinyint(1) UNSIGNED NULL DEFAULT NULL COMMENT '审批结果（0-同意，1-拒绝）',
  `biz_action` tinyint(1) UNSIGNED NULL DEFAULT NULL COMMENT '审批实例业务动作（1-表示该审批实例是基于原来的实例修改而来，2-表示该审批实例是由原来的实例撤销后重新发起的，3-表示正常发起）',
  `instance_create_time` datetime NULL DEFAULT NULL COMMENT '审批单创建时间',
  `instance_finish_time` datetime NULL DEFAULT NULL COMMENT '审批单结束时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_dept_id`(`dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '钉钉OA审批流程实例表' ROW_FORMAT = Dynamic;


-- ----------------------------
-- Table structure for ding_talk_process_instance_task
-- ----------------------------
DROP TABLE IF EXISTS `ding_talk_process_instance_task`;
CREATE TABLE `ding_talk_process_instance_task`  (
  `id` bigint(20) UNSIGNED NOT NULL COMMENT '主键',
  `task_id` bigint(20) UNSIGNED NOT NULL COMMENT '任务ID',
  `process_instance_id` varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '流程实例ID',
  `task_node_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任务节点id',
  `task_node_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务节点名称',
  `user_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '审批人用户id',
  `ding_talk_user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审批人的钉钉用户id',
  `status` tinyint(1) UNSIGNED NULL DEFAULT NULL COMMENT '任务状态（0-未启动，1-处理中，2-暂停，3-取消，4-完成，5-终止）',
  `result` tinyint(1) UNSIGNED NULL DEFAULT NULL COMMENT '结果（1-同意，2-拒绝，3-转交）',
  `instance_create_time` datetime NULL DEFAULT NULL COMMENT '单据开始时间',
  `instance_finish_time` datetime NULL DEFAULT NULL COMMENT '单据结束时间',
  `mobile_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '移动端任务URL',
  `pc_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'PC端任务URL',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '删除标识（0-未删除，1-已删除）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_instance_id`(`process_instance_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '钉钉流程实例任务表' ROW_FORMAT = Dynamic;
