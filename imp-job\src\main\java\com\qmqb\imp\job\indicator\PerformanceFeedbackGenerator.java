package com.qmqb.imp.job.indicator;

import com.qmqb.imp.common.constant.PerformanceFeedbackConstants;
import com.qmqb.imp.common.enums.IndicatorCategoryEnum;
import com.qmqb.imp.common.enums.PersonTypeEnum;
import com.qmqb.imp.common.enums.PerformanceFeedbackAuditStatusEnum;
import com.qmqb.imp.common.enums.PerformanceFeedbackDataSourceEnum;
import com.qmqb.imp.common.enums.PerformanceFeedbackSubmitStatusEnum;
import com.qmqb.imp.common.utils.PerformanceFeedbackUtils;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.performance.PerformanceFeedback;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 绩效反馈生成器
 * 根据指标计算结果生成绩效反馈记录
 *
 * <AUTHOR>
 */
@Component
public class PerformanceFeedbackGenerator {

    /**
     * 创建单个绩效反馈记录（基于指标计算结果）
     *
     * @param nickName         员工昵称
     * @param year             年份
     * @param month            月份
     * @param personTypeEnum   角色类型
     * @param indicatorCode    指标编码
     * @param calcResult       指标计算结果
     * @param mainFeedbackCode 主表编码
     * @param groupId          所属组ID
     * @param groupName        所属组名称
     * @return 绩效反馈记录
     */
    public PerformanceFeedback createFeedbackRecord(String nickName, Integer year, Integer month,
                                                    PersonTypeEnum personTypeEnum,
                                                    String indicatorCode,
                                                    IndicatorLevelCalcService.IndicatorCalcResult calcResult,
                                                    Long groupId,
                                                    String groupName) {
        PerformanceFeedback feedback = new PerformanceFeedback();
        feedback.setNickName(nickName);
        feedback.setYear(year);
        feedback.setMonth(month);
        feedback.setPersonType(String.valueOf(personTypeEnum.getType()));

        // 设置组信息
        feedback.setGroupId(groupId);
        feedback.setGroupName(groupName);

        // 设置指标信息
        IndicatorCategoryEnum category = IndicatorCategoryEnum.getByIndicatorCode(indicatorCode);
        if (category != null) {
            feedback.setPrimaryIndicator(category.getCode());
        }

        String indicatorName = PerformanceFeedbackUtils.getIndicatorName(indicatorCode);
        feedback.setSecondaryIndicator(indicatorCode);

        // 设置事件信息
        feedback.setEventTitle(PerformanceFeedbackUtils.generateEventTitle(indicatorName, calcResult.getLevel()));
        feedback.setEventDetail(calcResult.getLogContent());

        // 设置推荐信息
        feedback.setRecommendedLevel(calcResult.getLevel());
        feedback.setRecommendedReason(PerformanceFeedbackUtils.generateRecommendedReason(
            indicatorName, calcResult.getLevel(), calcResult.getLogContent()));

        return feedback;
    }
}
