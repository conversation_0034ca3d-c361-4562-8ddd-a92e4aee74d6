package com.qmqb.imp.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.qmqb.imp.common.exception.ServiceException;
import com.qmqb.imp.system.domain.SlowMonthStats;
import com.qmqb.imp.system.domain.SlowQueryLog;
import com.qmqb.imp.system.domain.SlowQueryProcessInfo;
import com.qmqb.imp.system.mapper.SlowMonthStatsMapper;
import com.qmqb.imp.system.mapper.SlowQueryLogMapper;
import com.qmqb.imp.system.mapper.SlowQueryProcessInfoMapper;
import com.qmqb.imp.system.service.ISlowQueryDbListService;
import com.qmqb.imp.system.service.SiliconFlowAiService;
import com.tool.sillicon.flow.service.ChatService;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tool.sillicon.flow.model.ChatResponse;

/**
 * 硅流AI服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SiliconFlowAiServiceImpl implements SiliconFlowAiService {

    @Autowired
    private ChatService chatService;

    @Autowired
    private ISlowQueryDbListService iSlowQueryDbListService;

    @Autowired
    private SlowQueryLogMapper slowQueryLogMapper;

    @Autowired
    private SlowQueryProcessInfoMapper slowQueryProcessInfoMapper;

    @Autowired
    private SlowMonthStatsMapper slowMonthStatsMapper;

    @Override
    public String analyzeSql(String sqlHash) {
        try {
            SlowQueryLog slowQueryLog=slowQueryLogMapper.selectOne(new LambdaQueryWrapper<SlowQueryLog>().eq(SlowQueryLog::getSqlHash, sqlHash).last("limit 1"));
            if(slowQueryLog==null || StrUtil.isBlank(slowQueryLog.getSqlText())){
                throw new ServiceException("数据或sql文本不存在");
            }
            String sql=slowQueryLog.getSqlText();
            ChatResponse chatResponse = chatService.analyzeSql(sql);
            String content = chatResponse.getChoices().get(0).getMessage().getContent();
            if(StrUtil.isBlank(content)){
                throw new ServiceException("AI分析数据为空");
            }
            slowMonthStatsMapper.update(null, new LambdaUpdateWrapper<SlowMonthStats>()
                .set(SlowMonthStats::getAnalysisResult, content)
                .eq(SlowMonthStats::getSqlHash, sqlHash));
            return content;
        } catch (Exception e) {
            log.error("SQL分析失败", e);
            throw e;
        }
    }

    @Override
    public String getAnalyzeResult(String sqlHash) {
        try {
            SlowMonthStats slowMonthStats = slowMonthStatsMapper.selectOne(new LambdaQueryWrapper<SlowMonthStats>().eq(SlowMonthStats::getSqlHash, sqlHash).last("limit 1"));
            if (slowMonthStats == null) {
                return null;
            }
            return slowMonthStats.getAnalysisResult();
        } catch (Exception e) {
            log.error("获取慢sql分析结果失败", e);
            throw e;
        }
    }
}
