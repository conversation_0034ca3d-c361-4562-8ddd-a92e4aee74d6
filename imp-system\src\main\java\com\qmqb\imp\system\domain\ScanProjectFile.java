package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 扫描项目文件记录对象 tb_scan_project_file
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
@EqualsAndHashCode()
@TableName("tb_scan_project_file")
public class ScanProjectFile {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id")
    private Long id;
    
    /**
     * 项目id
     */
    private Long pId;
    
    /**
     * 扫描版本号
     */
    private Long scanVersion;
    
    /**
     * 是否最新扫描（1新纪录，0旧记录）
     */
    private Integer lastScanFlag;
    
    /**
     * 问题文件路径
     */
    private String scanFileUrl;
    
    /**
     * 严重问题数（master分支）
     */
    private Long blockerAmount;
    
    /**
     * 一般问题数（master分支）
     */
    private Long criticalAmount;
    
    /**
     * 状态（0未指派，1已指派未处理，2已指派已处理）
     */
    private String status;
    
    /**
     * 处理人
     */
    private String handleUser;
    
    /**
     * 处理人id
     */
    private Long handleUserId;
    
    /**
     * 处理时间
     */
    private Date handleTime;
    
    /**
     * 处理说明
     */
    private String handleRemark;
    
    /**
     * 指派人
     */
    private String assignUser;
    
    /**
     * 指派人id
     */
    private Long assignUserId;
    
    /**
     * 指派时间
     */
    private Date assignTime;
    
    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private Integer delFlag;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 