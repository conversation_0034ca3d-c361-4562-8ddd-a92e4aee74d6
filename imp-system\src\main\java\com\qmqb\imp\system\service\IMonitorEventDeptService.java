package com.qmqb.imp.system.service;

import com.qmqb.imp.system.domain.MonitorEventDept;
import com.qmqb.imp.system.domain.vo.MonitorEventDeptVo;
import com.qmqb.imp.system.domain.bo.MonitorEventDeptBo;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 预警事件和部门关联Service接口
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
public interface IMonitorEventDeptService {

    /**
     * 查询预警事件和部门关联
     *
     * @param eventId 预警事件ID
     * @return 预警事件和部门关联信息
     */
    MonitorEventDeptVo queryById(Long eventId);

    /**
     * 查询预警事件和部门关联列表
     *
     * @param bo 预警事件和部门关联业务对象
     * @param pageQuery 分页查询对象
     * @return 预警事件和部门关联分页数据
     */
    TableDataInfo<MonitorEventDeptVo> queryPageList(MonitorEventDeptBo bo, PageQuery pageQuery);

    /**
     * 查询预警事件和部门关联列表
     *
     * @param bo 预警事件和部门关联业务对象
     * @return 预警事件和部门关联列表
     */
    List<MonitorEventDeptVo> queryList(MonitorEventDeptBo bo);

    /**
     * 新增预警事件和部门关联
     *
     * @param bo 预警事件和部门关联业务对象
     * @return 结果
     */
    Boolean insertByBo(MonitorEventDeptBo bo);

    /**
     * 修改预警事件和部门关联
     *
     * @param bo 预警事件和部门关联业务对象
     * @return 结果
     */
    Boolean updateByBo(MonitorEventDeptBo bo);

    /**
     * 校验并批量删除预警事件和部门关联信息
     *
     * @param ids 需要删除的预警事件和部门关联ID集合
     * @param isValid 是否进行有效性校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
