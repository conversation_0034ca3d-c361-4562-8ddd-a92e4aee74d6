package com.qmqb.imp.system.domain.bo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 处理扫描项目文件操作业务对象
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
public class ScanProjectFileProcessBo {

    /**
     * 文件ID列表（支持批量操作）
     */
    @NotEmpty(message = "文件ID列表不能为空")
    private List<Long> fileIds;

    /**
     * 处理说明（处理时可填）
     */
    private String handleRemark;

    /**
     * 目标状态（处理时可选：1-设为未处理，2-设为已处理，不填默认为已处理）
     */
    private String targetStatus;

}
