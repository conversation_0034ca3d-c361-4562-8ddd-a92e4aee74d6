package com.qmqb.imp.system.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 分支详细信息对象 tb_branch
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
@Data
public class SlowQueryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 同类SQL的HASH值
     */
    @Schema(description = "同类SQL的HASH值")
    private String sqlHash;
    /**
     * SQL文本
     */
    @Schema(description = "SQL文本")
    private String sqlText;
    /**
     * 执行次数
     */
    @Schema(description = "执行次数")
    private Long queryTimes;
    /**
     * 总毫秒数
     */
    @Schema(description = "总毫秒数")
    private Long sumTime;
    /**
     * 平均毫秒数
     */
    @Schema(description = "平均毫秒数")
    private Long avgTime;


}
