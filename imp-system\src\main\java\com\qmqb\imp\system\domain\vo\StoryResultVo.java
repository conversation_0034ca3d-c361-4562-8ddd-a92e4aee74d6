package com.qmqb.imp.system.domain.vo;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 需求成果视图对象 tb_story_result
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Data
@ExcelIgnoreUnannotated
public class StoryResultVo {

    private static final long serialVersionUID = 1L;

    /**
     * 需求id
     */
    @ExcelProperty(value = "需求id")
    private Long id;

    /**
     * 成果id
     */
    @ExcelProperty(value = "成果id")
    private Long resultId;

    /**
     * 成果编码
     */
    @ExcelProperty(value = "成果编码")
    private String resultCode;

    /**
     * 产品id
     */
    @ExcelProperty(value = "产品id")
    private Long productId;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 需求标题
     */
    @ExcelProperty(value = "需求标题")
    private String title;

    /**
     * 当前状态
     */
    @ExcelProperty(value = "当前状态")
    private String status;

    /**
     * 所处阶段
     */
    @ExcelProperty(value = "所处阶段")
    private String stage;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private String openedBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date openedDate;

    /**
     * 跟进人
     */
    @ExcelProperty(value = "跟进人")
    private String assignedTo;

    /**
     * 跟进时间
     */
    @ExcelProperty(value = "跟进时间")
    private Date assignedDate;

    /**
     * 评审人
     */
    @ExcelProperty(value = "评审人")
    private String reviewedBy;

    private String reviewer;

    /**
     * 评审时间
     */
    @ExcelProperty(value = "评审时间")
    private Date reviewedDate;

    /**
     * 关闭人
     */
    @ExcelProperty(value = "关闭人")
    private String closedBy;

    /**
     * 关闭时间
     */
    @ExcelProperty(value = "关闭时间")
    private Date closedDate;

    /**
     * 关闭原因
     */
    @ExcelProperty(value = "关闭原因")
    private String closedReason;

    /**
     * bug数量
     */
    @ExcelProperty(value = "bug数量")
    private Integer bugCount;

    /**
     * 用例数量
     */
    @ExcelProperty(value = "用例数量")
    private Integer caseCount;

    /**
     * 任务数量
     */
    @ExcelProperty(value = "任务数量")
    private Integer taskCount;

    /**
     * 发布数
     */
    @ExcelProperty(value = "发布数")
    private Integer releaseCount;



}
