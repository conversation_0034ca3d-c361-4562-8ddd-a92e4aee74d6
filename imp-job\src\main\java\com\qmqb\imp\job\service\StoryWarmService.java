package com.qmqb.imp.job.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.Constants;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.common.enums.WarningTypeEnum;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.common.utils.JsonUtils;
import com.qmqb.imp.job.api.config.DingTalkConfig;
import com.qmqb.imp.system.domain.WarningRecord;
import com.qmqb.imp.system.domain.bo.StoryCaseWarningParamsBO;
import com.qmqb.imp.system.domain.bo.message.DdRobotMsgBo;
import com.qmqb.imp.system.mapper.StoryMapper;
import com.qmqb.imp.system.mapper.WarningRecordMapper;
import com.qmqb.imp.system.runner.HolidayUtil;
import com.qmqb.imp.system.service.message.IMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Collections;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 需求预警
 * @date 2025/7/9 14:25
 */
@Component
@Slf4j
public class StoryWarmService {

    @Resource
    private StoryMapper storyMapper;

    @Resource
    private IMessageService messageService;

    @Resource
    private DingTalkConfig dingTalkConfig;

    @Resource
    private WarningRecordMapper warningRecordMapper;

    /**
     * 新增
     */
    public static final String ADD = "新增";

    /**
     * 减少
     */
    public static final String SUB = "减少";


    @TraceId("需求用例预警")
    @XxlJob("storyCaseWarnJobHandler")
    public ReturnT<String> storyCaseWarnJobHandler(String param) {
        try {
            XxlJobLogger.log("开始执行需求用例预警定时任务...");
            log.info("开始执行进行需求用例预警定时任务...");
            //节假日不执行定时任务
            if (HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getNowDate()))) {
                log.info("节假日不执行定时任务");
                return ReturnT.SUCCESS;
            }
            StopWatch sw = new StopWatch();
            sw.start();
            LocalDate yesterday = LocalDate.now().plusDays(-1);
            Integer count = storyMapper.selectNotCaseStoryCount(yesterday.plusDays(-59), yesterday);
            LocalDate now = LocalDate.now();
            String text;
            if (count > 0) {
                text = StrUtil.format(Constants.NO_CASE_STORY_COUNT, Collections.singletonMap("count", String.valueOf(count)));
                WarningRecord yesterdayWarningRecord = warningRecordMapper.selectOne(new LambdaQueryWrapper<WarningRecord>()
                    .eq(WarningRecord::getType, WarningTypeEnum.STORY_CASE.getCode())
                    .eq(WarningRecord::getSendTime, now.plusDays(-1))
                    .orderByDesc(WarningRecord::getCreateTime)
                    .last("limit 1"));
                if (yesterdayWarningRecord != null && StringUtils.isNotBlank(yesterdayWarningRecord.getParams())) {
                    StoryCaseWarningParamsBO params = JsonUtils.parseObject(yesterdayWarningRecord.getParams(), StoryCaseWarningParamsBO.class);
                    if (params != null) {
                        Integer yesterdayCount = Optional.ofNullable(params.getCaseCount()).orElse(0);
                        if (!count.equals(yesterdayCount)) {
                            text = text + String.format("\n较昨日未添加测试用例的需求数%s%d条。", count > yesterdayCount ? ADD : SUB, Math.abs(count - yesterdayCount));
                        }
                    }
                }
            } else {
                text = Constants.STORY_CASE_NORMAL;
            }
            // 记录预警
            WarningRecord record = new WarningRecord();
            record.setType(WarningTypeEnum.STORY_CASE.getCode());
            record.setSendTime(now);
            record.setContext(text);
            record.setParams(JsonUtils.toJsonString(new StoryCaseWarningParamsBO(count)));
            record.setCreateTime(new Date());
            warningRecordMapper.insert(record);
            // 发送预警
            send(dingTalkConfig.getJszxRobotUrl(), text);
            sw.stop();
            XxlJobLogger.log("需求用例预警定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            log.info("需求用例预警定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("需求用例预警定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 发送预警
     *
     * @param robotUrl 机器人url
     * @param content  内容
     */
    private void send(String robotUrl, String content) {
        DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder()
            .url(robotUrl)
            .msgtype("text")
            .content(content)
            .build();
        robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
        try {
            messageService.sendBase(robotMsgBo);
        } catch (Exception e) {
            log.error("调用消息中心发送消息异常。异常信息：{}", e.getMessage());
        }
    }
}
