package com.qmqb.imp.app.controller;

import com.qmqb.imp.app.domain.bo.DeptKeyIndicatorBO;
import com.qmqb.imp.app.domain.vo.KeyIndicatorVO;
import com.qmqb.imp.app.domain.vo.KeyValueVO;
import com.qmqb.imp.app.service.DeptKeyIndicatorService;
import com.qmqb.imp.common.core.domain.R;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 部门关键指标
 *
 * <AUTHOR> Li
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/dept-indicator")
public class DeptKeyIndicatorController {

    private final DeptKeyIndicatorService deptKeyIndicatorService;

    /**
     * 组列表
     * key为姓名
     * value为部门ID
     * 查询指标详情传部门ID
     *
     * @return 结果
     */
    @GetMapping("/group-select")
    public R<List<KeyValueVO>> groupSelect() {
        return deptKeyIndicatorService.groupSelect();
    }

    /**
     * 指标详情
     *
     * @return 结果
     */
    @GetMapping("/detail")
    public R<KeyIndicatorVO> detail(@Valid DeptKeyIndicatorBO bo) {
        return deptKeyIndicatorService.detail(bo);
    }


}
