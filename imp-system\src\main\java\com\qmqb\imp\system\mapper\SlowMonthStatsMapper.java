package com.qmqb.imp.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.SlowMonthStats;
import com.qmqb.imp.system.domain.bo.SlowMonthStatsBo;
import com.qmqb.imp.system.domain.vo.SlowMonthStatsVo;
import org.apache.ibatis.annotations.Param;

/**
 * 当月慢SQL统计Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@DS(DataSource.SLOWSQL)
public interface SlowMonthStatsMapper extends BaseMapperPlus<SlowMonthStatsMapper, SlowMonthStats, SlowMonthStatsVo> {

    /**
     * 分页获取慢sql
     *
     * @param build
     * @param bo
     * @return
     */
    Page<SlowMonthStatsVo> slowSqlPage(Page<Object> build, @Param("bo") SlowMonthStatsBo bo);
}
