package com.qmqb.imp.system.service;

import com.qmqb.imp.system.domain.bo.DataBasePermissionCreateBo;
import com.qmqb.imp.system.domain.bo.DataBasePermissionUpdateBo;
import com.qmqb.imp.system.domain.vo.DataBasePermissionVo;
import com.qmqb.imp.system.domain.bo.DataBasePermissionQueryBo;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 数据库权限Service接口
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface IDataBasePermissionService {

    /**
     * 查询数据库权限
     *
     * @param id
     * @return
     */
    DataBasePermissionVo queryById(Long id);

    /**
     * 查询数据库权限列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<DataBasePermissionVo> queryPageList(DataBasePermissionQueryBo bo, PageQuery pageQuery);

    /**
     * 查询数据库权限列表
     *
     * @param bo
     * @return
     */
    List<DataBasePermissionVo> queryList(DataBasePermissionQueryBo bo);

    /**
     * 新增数据库权限
     *
     * @param bo
     * @return
     */
    Boolean insertByBo(DataBasePermissionCreateBo bo);

    /**
     * 修改数据库权限
     *
     * @param bo
     * @return
     */
    Boolean updateByBo(DataBasePermissionUpdateBo bo);

    /**
     *
     * 校验并批量删除数据库权限信息
     *
     * @param ids
     * @param isValid
     * @return
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
