package com.qmqb.imp.system.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qmqb.imp.system.domain.SlowLogStats;
import com.qmqb.imp.system.mapper.SlowLogStatsMapper;
import com.qmqb.imp.system.service.ISlowLogStatsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06 10:15:00
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SlowLogStatsServiceImpl extends ServiceImpl<SlowLogStatsMapper, SlowLogStats> implements ISlowLogStatsService {

    private final SlowLogStatsMapper slowLogStatsMapper;


}
