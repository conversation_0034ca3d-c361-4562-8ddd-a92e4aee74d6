package com.qmqb.imp.system.domain.bo;

import com.qmqb.imp.common.core.domain.BaseEntity;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 业务类型业务对象 tb_business_type
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BusinessTypeBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 业务类型名称
     */
    @NotBlank(message = "业务类型名称不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 128, message = "业务类型名称长度不能超过128个字符")
    private String businessTypeName;

    /**
     * 所属业务大类：1国内、2海外
     */
    @NotBlank(message = "所属业务大类不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 2, message = "所属业务大类长度不能超过2个字符")
    private String businessCategoryMajor;

    /**
     * 所属业务小类：1风控、2营销、3资金、4资产、5贷后、6自营业务、7综合、8其它
     */
    @NotBlank(message = "所属业务小类不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 2, message = "所属业务小类长度不能超过2个字符")
    private String businessCategoryMinor;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer sort;

    /**
     * 业务负责人
     */
    @NotBlank(message = "业务负责人不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(max = 30, message = "业务负责人长度不能超过30个字符")
    private String businessManager;

}
