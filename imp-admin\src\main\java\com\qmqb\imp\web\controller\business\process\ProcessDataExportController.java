package com.qmqb.imp.web.controller.business.process;


import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.system.domain.vo.process.ProcessDataExportVo;
import com.qmqb.imp.system.domain.bo.process.ProcessDataExportBo;
import com.qmqb.imp.system.service.process.IProcessDataExportService;
import com.qmqb.imp.common.core.page.TableDataInfo;

/**
 * 业务敏感数据导出申请
 *
 * <AUTHOR>
 * @date 2024-12-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/processDataExport")
public class ProcessDataExportController extends BaseController {

    private final IProcessDataExportService iProcessDataExportService;

    /**
     * 查询业务敏感数据导出申请列表
     */
    @SaCheckPermission("system:processDataExport:list")
    @GetMapping("/list")
    public TableDataInfo<ProcessDataExportVo> list(ProcessDataExportBo bo, PageQuery pageQuery) {
        return iProcessDataExportService.queryPageList(bo, pageQuery);
    }


}
