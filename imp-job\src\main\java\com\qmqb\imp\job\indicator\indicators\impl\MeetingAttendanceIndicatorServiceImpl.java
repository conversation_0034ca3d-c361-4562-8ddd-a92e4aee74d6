package com.qmqb.imp.job.indicator.indicators.impl;

import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.core.domain.entity.SysRole;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.GroupEnum;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.PersonTypeEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.job.indicator.GroupIndicatorCalculateManager;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 参加会议情况指标等级计算
 * @date 2025/7/2 14:42
 */
@Service
public class MeetingAttendanceIndicatorServiceImpl implements IndicatorLevelCalcService {

    @Autowired
    ISysUserService userService;
    @Autowired
    GroupIndicatorCalculateManager groupIndicatorCalculateManager;


    @Override
    public String getIndicatorCode() {
        return PerformanceIndicatorEnum.MEETING_ATTENDANCE.getCode();
    }


    @Override
    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults) {
        SysUser sysUser = userService.selectUserByNickName(nickName);
        if (sysUser == null) {
            throw new ServiceException(String.format("未找到技术经理信息: %s", nickName));
        }
        String roleName = sysUser.getRoles().stream().findFirst().map(SysRole::getRoleName).orElse(null);
        if (PersonTypeEnum.TECHNICAL_MANAGER.getDesc().equals(roleName)) {
            String deptName = sysUser.getDept().getDeptName();
            //运维岗：参加会议情况；
            if (deptName.equals(GroupEnum.SHUJU.getGroupName()) ||
                deptName.equals(GroupEnum.YUNWEI.getGroupName()) ||
                deptName.equals(GroupEnum.BI.getGroupName())) {
                return groupIndicatorCalculateManager.caculateIndicator(workResult.getWorkYear(), workResult.getWorkMonth(),
                    PerformanceIndicatorEnum.MEETING_ATTENDANCE.getCode(),sysUser);
            }
            // 测试、开发岗：参加评审会情况
            if (sysUser.getDept().getDeptType().equals(CommConstants.CommonVal.ONE) ||
                sysUser.getDept().getDeptType().equals(CommConstants.CommonVal.TWO)) {
                return groupIndicatorCalculateManager.caculateIndicator(workResult.getWorkYear(), workResult.getWorkMonth(),
                    PerformanceIndicatorEnum.REVIEW_MEETING.getCode(),sysUser);
            }

        }
        return ScoreLevelEnum.SCORE_B.getCode();
    }

    @Override
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level) {
        SysUser sysUser = userService.selectUserByNickName(nickName);
        String roleName = sysUser.getRoles().stream().findFirst().map(SysRole::getRoleName).orElse(null);
        if (PersonTypeEnum.TECHNICAL_MANAGER.getDesc().equals(roleName)) {
            GroupIndicatorCalculateManager.CaculateDate caculateDate;
            String deptName = sysUser.getDept().getDeptName();
            //运维岗：参加会议情况；
            if (deptName.equals(GroupEnum.SHUJU.getGroupName()) ||
                deptName.equals(GroupEnum.YUNWEI.getGroupName()) ||
                deptName.equals(GroupEnum.BI.getGroupName())) {
                return groupIndicatorCalculateManager.caculateIndicator(workResult.getWorkYear(), workResult.getWorkMonth(),
                    PerformanceIndicatorEnum.MEETING_ATTENDANCE.getCode(),sysUser);
            }else {
                // 其他岗位(开发、测试)：参加评审会情况
                caculateDate = groupIndicatorCalculateManager.setCaculateDate(
                    workResult.getWorkYear(),
                    workResult.getWorkMonth(),
                    PerformanceIndicatorEnum.REVIEW_MEETING.getCode(),
                    sysUser);

            }

            // 生成日志内容
            StringBuilder logContent = new StringBuilder();
            logContent.append(String.format("[%s]在%s年%s月份的会议出席情况：",
                nickName, workResult.getWorkYear(), workResult.getWorkMonth()));

            // 添加团队规模信息
            logContent.append(String.format("团队人数=%d，", caculateDate.getTeamMembers().size()));

            // 添加组员等级分布信息
            Map<String, Long> levelCounts = caculateDate.getLevelCountMap();
            logContent.append("组员等级分布[");
            levelCounts.forEach((k, v) -> logContent.append(String.format("%s=%d ", k, v)));
            logContent.append("]");

            // 添加评级和原因
            logContent.append(String.format("，评级：%s", level));
            String reason = groupIndicatorCalculateManager.getRatingReason(caculateDate, level);
            if (StringUtils.isNotEmpty(reason)) {
                logContent.append(String.format("，原因：%s", reason));
            }
            return logContent.toString();
        }
        return null;
    }


}
