package com.qmqb.imp.web.controller.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.system.domain.bo.BranchBo;
import com.qmqb.imp.system.domain.vo.BranchVo;
import com.qmqb.imp.system.service.IBranchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/branch/query")
public class BranchController  extends BaseController {


    @Autowired
    private IBranchService iBranchService;

    @GetMapping("/page")
    public R<Page<BranchVo>> page(BranchBo request){
        return R.ok(iBranchService.page(request));
    }
}
