package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.constant.CacheNames;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.domain.OndutyUser;
import com.qmqb.imp.system.domain.SysConfig;
import com.qmqb.imp.system.domain.bo.OndutyUserImportVo;
import com.qmqb.imp.system.domain.vo.OndutyUserVo;
import com.qmqb.imp.system.service.IOndutyUserService;
import com.qmqb.imp.system.service.ISysConfigService;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.OnDutyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 值班业务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OnDutyServiceImpl implements OnDutyService {

    private final IOndutyUserService iOndutyUserService;

    private final ISysUserService iSysUserService;

    private final ISysConfigService iSysConfigService;

    @Override
    public void importUser(List<OndutyUserImportVo> volist) {

        if (CollectionUtils.isEmpty(volist)) {
            return;
        }
        String maxBatchNoStr = iSysConfigService.selectConfigByKey("onduty.batch.no.max");
        if (StringUtils.isEmpty(maxBatchNoStr)) {
            maxBatchNoStr = "1";
        }
        Integer maxBatchNo = Integer.parseInt(maxBatchNoStr);
        Optional<OndutyUserImportVo> max = volist.stream()
            .max(Comparator.comparing(OndutyUserImportVo::getBatchNo));
        if (max.isPresent()) {
            if (!maxBatchNo.equals(max.get().getBatchNo())) {
                throw new ServiceException("最新的批次号不对,最新批次：" + maxBatchNoStr + ",请核实");
            }
        }
        //对值班时间排序
        List<OndutyUserImportVo> sortOnDutyUser = volist.stream().sorted(Comparator.comparing(OndutyUserImportVo::getOndutyTime)).collect(Collectors.toList());
        for (int i = 0; i < sortOnDutyUser.size() - 1; i++) {
            if (DateUtils.differentDaysByMillisecond(sortOnDutyUser.get(i).getOndutyTime(), sortOnDutyUser.get(i + 1).getOndutyTime()) != 1) {
                throw new ServiceException("值班时间不连续：" + DateUtils.dateTime(sortOnDutyUser.get(i).getOndutyTime()) + "到" + DateUtils.dateTime(sortOnDutyUser.get(i + 1).getOndutyTime()) + ",请核实");
            }
        }
        List<SysUser> sysUsers = iSysUserService.selectUserByNickNames(sortOnDutyUser.stream().map(OndutyUserImportVo::getNickName).collect(Collectors.toList()));
        Map<String, Long> nickNameUserIdMap = sysUsers.stream().collect(Collectors.toMap(SysUser::getNickName, SysUser::getUserId));
        List<OndutyUser> onDutyUserList = sortOnDutyUser.stream().map(e -> {
            OndutyUser ondutyUser = new OndutyUser();
            Long userId = nickNameUserIdMap.get(e.getNickName());
            if (Objects.isNull(userId)) {

                log.info("导入值班数据，用户:{}存在异常", e.getNickName());
                throw new ServiceException("导入值班数据，用户:" + e.getNickName() + "存在异常");
            }
            ondutyUser.setUserId(userId);
            ondutyUser.setNickName(e.getNickName());
            ondutyUser.setBatchNo(maxBatchNo);
            ondutyUser.setOndutyTime(e.getOndutyTime());
            ondutyUser.setCreateTime(DateUtils.getNowDate());
            return ondutyUser;

        }).collect(Collectors.toList());
        //更新批次
        updateMaxBatchNo(maxBatchNo);
        iOndutyUserService.saveBatch(onDutyUserList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Collection<Long> ids, Boolean sync) {
        List<OndutyUserVo> deleteOnDutyUserVos = iOndutyUserService.queryByIds(ids);
        String max = iSysConfigService.selectConfigByKey("onduty.batch.no.max");
        deleteOnDutyUserVos.forEach(e -> {
            if (Integer.parseInt(max) - 1 != e.getBatchNo()) {
                log.info("只能删除最新批次数据，当前批次是:{}，最新批次是:{}", e.getBatchNo(), (Integer.parseInt(max) - 1));
                throw new ServiceException("只能删除最新批次数据，当前批次是:" + e.getBatchNo() + "，最新批次是:" + (Integer.parseInt(max) - 1));
            }
        });
        List<OndutyUserVo> onDutyUserVos = iOndutyUserService.queryByBatchNo(Integer.parseInt(max) - 1);
        List<OndutyUserVo> sortOnDutyUser = onDutyUserVos.stream().sorted(Comparator.comparing(OndutyUserVo::getOndutyTime)).collect(Collectors.toList());
        //获取最先的时间
        Date minTime = sortOnDutyUser.get(0).getOndutyTime();
        sortOnDutyUser = sortOnDutyUser.stream().filter(e -> !ids.contains(e.getId())).collect(Collectors.toList());
        Boolean success = iOndutyUserService.deleteWithValidByIds(ids, false);
        if (success && sync) {
            for (int i = 0; i < sortOnDutyUser.size(); i++) {
                sortOnDutyUser.get(i).setOndutyTime(DateUtils.addDays(minTime, i));
            }
        }
        List<OndutyUser> existUsers = BeanUtil.copyToList(sortOnDutyUser, OndutyUser.class);
        iOndutyUserService.updateBatch(existUsers);
        return true;
    }

    /**
     * 最大批次加1
     */
    @CacheEvict(cacheNames = CacheNames.SYS_CONFIG, key = "onduty.batch.no.max")
    public synchronized void updateMaxBatchNo(Integer maxBatchNo) {
        int next = maxBatchNo + 1;
        SysConfig config = new SysConfig();
        config.setConfigKey("onduty.batch.no.max");
        config.setConfigValue(String.valueOf(next));
        iSysConfigService.updateConfig(config);
    }
}
