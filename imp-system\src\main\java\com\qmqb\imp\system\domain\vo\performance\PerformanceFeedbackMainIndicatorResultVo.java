package com.qmqb.imp.system.domain.vo.performance;

import lombok.Data;
import java.io.Serializable;

/**
 * 绩效反馈主表与绩效指标结果中间表Vo
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Data
public class PerformanceFeedbackMainIndicatorResultVo implements Serializable {

    /** 主键ID */
    private Long id;

    /** 绩效反馈主表ID */
    private Long mainId;

    /** 绩效指标结果ID */
    private Long indicatorResultId;

    /** 删除标志（0正常 1删除） */
    private Integer delFlag;
} 