package com.qmqb.imp.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 禅道文档内容视图对象 zt_doccontent
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@Data
@ExcelIgnoreUnannotated
public class ZtDocContentVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Integer id;

    /**
     * 文档ID
     */
    @ExcelProperty(value = "文档ID")
    private Integer doc;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    private String title;

    /**
     * 摘要
     */
    @ExcelProperty(value = "摘要")
    private String digest;

    /**
     * 内容
     */
    @ExcelProperty(value = "内容")
    private String content;

    /**
     * HTML内容
     */
    @ExcelProperty(value = "HTML内容")
    private String html;

    /**
     * 原始内容
     */
    @ExcelProperty(value = "原始内容")
    private String rawContent;

    /**
     * 文件
     */
    @ExcelProperty(value = "文件")
    private String files;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型")
    private String type;

    /**
     * 添加人
     */
    @ExcelProperty(value = "添加人")
    private String addedBy;

    /**
     * 添加时间
     */
    @ExcelProperty(value = "添加时间")
    private Date addedDate;

    /**
     * 编辑人
     */
    @ExcelProperty(value = "编辑人")
    private String editedBy;

    /**
     * 编辑时间
     */
    @ExcelProperty(value = "编辑时间")
    private Date editedDate;

    /**
     * 版本
     */
    @ExcelProperty(value = "版本")
    private Integer version;

    /**
     * 来源版本
     */
    @ExcelProperty(value = "来源版本")
    private Integer fromVersion;

    // 为了兼容性，添加zt_doc的字段
    /**
     * 项目ID
     */
    @ExcelProperty(value = "项目ID")
    private Integer project;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Integer product;

    /**
     * 执行ID
     */
    @ExcelProperty(value = "执行ID")
    private Integer execution;

    /**
     * 文档库
     */
    @ExcelProperty(value = "文档库")
    private String lib;

    /**
     * 草稿（来自zt_doc表）
     */
    @ExcelProperty(value = "草稿")
    private String draft;
} 