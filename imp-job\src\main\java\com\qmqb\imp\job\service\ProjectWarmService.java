package com.qmqb.imp.job.service;

import cn.hutool.core.util.StrUtil;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.Constants;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.job.api.config.DingTalkConfig;
import com.qmqb.imp.system.domain.Project;
import com.qmqb.imp.system.domain.bo.message.DdRobotMsgBo;
import com.qmqb.imp.system.mapper.ProjectMapper;
import com.qmqb.imp.system.runner.HolidayUtil;
import com.qmqb.imp.system.service.message.IMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 代码库预警
 * @date 2025/7/9 14:25
 */
@Component
@Slf4j
public class ProjectWarmService {

    @Resource
    private ProjectMapper projectMapper;

    @Resource
    private IMessageService messageService;

    @Resource
    private DingTalkConfig dingTalkConfig;

    /**
     * 代码库信息丢失预警模板
     */
    public static final String PROJECT_INFO_LACK_WARNING_FORMAT = "\n%d.【%s】%s";

    @TraceId("代码信息缺失预警")
    @XxlJob("projectInfoLackWarmJobHandler")
    public ReturnT<String> projectInfoLackWarmJobHandler(String param) {
        try {
            XxlJobLogger.log("开始执行代码信息缺失预警定时任务...");
            log.info("开始执行进行代码信息缺失预警定时任务...");
            StopWatch sw = new StopWatch();
            sw.start();

            //节假日不执行定时任务
            if (HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getNowDate()))) {
                log.info("节假日不执行定时任务");
                return ReturnT.SUCCESS;
            }
            List<Project> projectList = projectMapper.selectList().stream()
                .filter(project -> project.getPDevDept() == null || project.getPTestDept() == null || project.getPNarrowBusiness() == null || project.getPBroadBusiness() == null)
                // 提醒入库时间为2025年以后的代码库。
                .filter(project -> project.getPCreatetime() != null && project.getPCreatetime().after(DateUtils.parseDate("2025-01-01")))
                .collect(Collectors.toList());
            if (!projectList.isEmpty()) {
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < projectList.size(); ) {
                    Project project = projectList.get(i);
                    builder.append(String.format(PROJECT_INFO_LACK_WARNING_FORMAT, ++i, project.getPName(), project.getPDesc()));
                    if (i >= 30) {
                        builder.append("\n.......");
                        break;
                    }
                }
                Map<String, String> map = new HashMap<>(10);
                map.put("content", builder.toString());
                send(dingTalkConfig.getRobotUrl(), Constants.PROJECT_INFO_LACK_TAG, map);
            }
            sw.stop();
            XxlJobLogger.log("代码信息缺失预警定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            log.info("代码信息缺失预警定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("代码信息缺失预警定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 发送预警
     *
     * @param robotUrl 机器人url
     * @param template 模板
     * @param map      数据
     */
    private void send(String robotUrl, String template, Map<String, String> map) {
        String content = StrUtil.format(template, map);
        DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder()
            .url(robotUrl)
            .msgtype("text")
            .content(content)
            .build();
        robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
        try {
            messageService.sendBase(robotMsgBo);
        } catch (Exception e) {
            log.error("调用消息中心发送消息异常。异常信息：{}", e.getMessage());
        }
    }
}
