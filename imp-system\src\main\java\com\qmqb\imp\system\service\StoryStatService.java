package com.qmqb.imp.system.service;

import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.dto.StoryStatQueryDTO;
import com.qmqb.imp.system.domain.vo.CodeStatisticsVO;
import com.qmqb.imp.system.domain.vo.StoryStatVO;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * <p>
 * 禅道需求统计
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18
 */
public interface StoryStatService {

    /**
     * 获取需求统计数据
     *
     * @param dto
     * @return
     */
    List<StoryStatVO> getStoryStat(StoryStatQueryDTO dto);

}
