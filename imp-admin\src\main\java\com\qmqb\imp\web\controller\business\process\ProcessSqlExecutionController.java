package com.qmqb.imp.web.controller.business.process;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.system.domain.bo.process.ProcessSqlExecutionBo;
import com.qmqb.imp.system.domain.vo.process.ProcessSqlExecutionVo;
import com.qmqb.imp.system.service.process.IProcessSqlExecutionService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 生产维护SQL执行
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/processSqlExecution")
public class ProcessSqlExecutionController extends BaseController {

    private final IProcessSqlExecutionService iProcessSqlExecutionService;

    /**
     * 查询生产维护SQL执行列表
     */
    @SaCheckPermission("system:processSqlExecution:list")
    @GetMapping("/list")
    public TableDataInfo<ProcessSqlExecutionVo> list(ProcessSqlExecutionBo bo, PageQuery pageQuery) {
        return iProcessSqlExecutionService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取生产维护SQL执行详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:processSqlExecution:query")
    @GetMapping("/{id}")
    public R<ProcessSqlExecutionVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iProcessSqlExecutionService.queryById(id));
    }

    /**
     * 新增生产维护SQL执行
     */
    @SaCheckPermission("system:processSqlExecution:add")
    @Log(title = "生产维护SQL执行", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProcessSqlExecutionBo bo) {
        return toAjax(iProcessSqlExecutionService.insertByBo(bo));
    }

    /**
     * 修改生产维护SQL执行
     */
    @SaCheckPermission("system:processSqlExecution:edit")
    @Log(title = "生产维护SQL执行", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProcessSqlExecutionBo bo) {
        return toAjax(iProcessSqlExecutionService.updateByBo(bo));
    }

    /**
     * 删除生产维护SQL执行
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:processSqlExecution:remove")
    @Log(title = "生产维护SQL执行", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iProcessSqlExecutionService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
