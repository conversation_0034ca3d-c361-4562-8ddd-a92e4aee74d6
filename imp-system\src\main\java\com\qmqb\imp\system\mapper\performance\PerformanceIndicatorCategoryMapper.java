package com.qmqb.imp.system.mapper.performance;

import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.performance.PerformanceIndicatorCategory;
import com.qmqb.imp.system.domain.vo.performance.PerformanceIndicatorCategoryVo;

/**
 * 绩效指标分类Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface PerformanceIndicatorCategoryMapper extends BaseMapperPlus<PerformanceIndicatorCategoryMapper, PerformanceIndicatorCategory, PerformanceIndicatorCategoryVo> {

} 