package com.qmqb.imp.app.domain.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <p>
 * 修改密码业务对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Data
public class ChangePasswordBO {

    /**
     * 新密码
     */
    @NotBlank(message = "新密码不能为空")
    @Pattern(regexp = "^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d]{8,}$", message = "密码需8个字符以上，包含字母、数字")
    private String newPwd;
    /**
     * 确认新密码
     */
    @NotBlank(message = "确认新密码不能为空")
    private String confirmPwd;
}
