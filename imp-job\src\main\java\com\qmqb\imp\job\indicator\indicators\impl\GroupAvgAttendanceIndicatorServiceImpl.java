package com.qmqb.imp.job.indicator.indicators.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.constant.HttpStatus;
import com.qmqb.imp.common.constant.UserConstants;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.UserKqStat;
import com.qmqb.imp.system.domain.performance.Performance;
import com.qmqb.imp.system.domain.performance.PerformanceIndicator;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.domain.vo.WorkTotalVO;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.IUserKqStatService;
import com.qmqb.imp.system.service.PerformStatService;
import com.qmqb.imp.system.service.indicator.IPerformanceIndicatorService;
import com.qmqb.imp.system.service.indicator.IPerformanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.Optional;
import java.util.HashSet;
import java.util.Set;
import java.util.Arrays;

/**
 * 本组组员平均月考勤指标等级计算
 * <p>
 * 针对技术经理角色，根据其管理团队的考勤情况来评估该指标：
 * - 计算组内所有成员的考勤表现
 * - 特殊规则：如果组内有任何一个人的考勤指标是D级，则技术经理该指标直接评为D级
 * - 否则根据组员平均考勤表现确定等级（迟到数使用平均值）
 *
 * 评级规则：
 * - D级：组内有成员考勤指标为D级（一票否决）
 * - C级：有>=3次早退或无故旷工情况、平均迟到次数>=11次
 * - S级：总工时>=8小时,部门排名前10%,无早退且月平均迟到次数<=3次
 * - A级：总工时>=8小时,部门排名前20%,无早退且月平均迟到次数<=5次
 * - B级：平均总工时>=7小时（请假情况除外）并且无早退、月平均迟到次数<=10次
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class GroupAvgAttendanceIndicatorServiceImpl implements IndicatorLevelCalcService {

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IUserKqStatService userKqStatService;

    @Autowired
    private IPerformanceIndicatorService performanceIndicatorService;

    @Autowired
    private IPerformanceService performanceService;

    @Autowired
    private PerformStatService performStatService;

    @Override
    public String getIndicatorCode() {
        return PerformanceIndicatorEnum.GROUP_AVG_ATTENDANCE.getCode();
    }

    @Override
    public IndicatorCalcResult calcLevel(String nickName, Integer month, List<TrackWorkResultVO> trackWorkResults) {
        Optional<TrackWorkResultVO> userWorkResultOpt = trackWorkResults.stream()
            .filter(r -> nickName.equals(r.getWorkUsername()))
            .findFirst();

        if (!userWorkResultOpt.isPresent()) {
            String indicatorName = PerformanceIndicatorEnum.GROUP_AVG_ATTENDANCE.getName();
            return new IndicatorCalcResult(ScoreLevelEnum.SCORE_C.getCode(),
                String.format("技术经理[%s]在%s月份的%s数据未找到", nickName, month, indicatorName));
        }

        TrackWorkResultVO userWorkResult = userWorkResultOpt.get();

        try {
            // 构建团队计算数据
            TeamCalculationData teamData = buildTeamData(userWorkResult, nickName);

            // 检查一票否决
            if (checkDlevelVeto(teamData)) {
                teamData.hasDlevelMember = true;
                String logContent = generateLogContent(teamData, ScoreLevelEnum.SCORE_D.getCode());
                return new IndicatorCalcResult(ScoreLevelEnum.SCORE_D.getCode(), logContent);
            }

            // 计算考勤统计数据
            calculateAttendanceStatistics(teamData);

            // 获取工作时长数据
            getWorkTimeData(teamData);

            // 计算最终等级
            String finalLevel = calculateFinalLevel(teamData);

            // 生成日志
            String logContent = generateLogContent(teamData, finalLevel);

            return new IndicatorCalcResult(finalLevel, logContent);

        } catch (Exception e) {
            log.error("计算技术经理 {} 本组组员平均月考勤指标失败", nickName, e);
            throw new ServiceException(String.format("技术经理[%s]本组组员平均月考勤指标计算异常：%s", nickName, e.getMessage()), e);
        }
    }

    @Override
    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults) {
        return "";
    }

    /**
     * 构建团队计算数据
     */
    private TeamCalculationData buildTeamData(TrackWorkResultVO workResult, String nickName) {
        Integer year = workResult.getWorkYear();
        Integer month = workResult.getWorkMonth();

        TeamCalculationData data = new TeamCalculationData();
        data.managerName = nickName;
        data.year = year;
        data.month = month;

        // 获取技术经理信息
        data.manager = sysUserService.selectUserByNickName(nickName);
        if (data.manager == null) {
            throw new ServiceException(String.format("未找到技术经理信息: %s", nickName));
        }

        // 获取所有团队成员
        data.allMembers = sysUserService.selectUserByDeptId(data.manager.getDeptId());
        if (data.allMembers.isEmpty()) {
            throw new ServiceException(String.format("技术经理 %s 的团队无成员", nickName));
        }

        // 过滤请假成员
        filterAbsentMembers(data);

        // 获取团队绩效记录
        data.performanceIds = getTeamPerformanceIds(data);

        return data;
    }

    /**
     * 过滤请假成员（出勤天数为0）
     */
    private void filterAbsentMembers(TeamCalculationData data) {
        List<String> allMemberNames = data.allMembers.stream()
            .map(SysUser::getNickName)
            .collect(Collectors.toList());

        // 获取考勤数据
        List<UserKqStat> attendanceStats = userKqStatService.listByUserNamesAndKqYearAndKqMonth(
            allMemberNames, String.valueOf(data.year), String.valueOf(data.month));

        // 找出请假成员
        Set<String> absentMemberNames = attendanceStats.stream()
            .filter(stat -> stat.getKqAttendanceDays() != null && stat.getKqAttendanceDays().intValue() == CommConstants.CommonVal.ZERO)
            .map(UserKqStat::getKqUserName)
            .collect(Collectors.toSet());

        // 分离有效成员和请假成员
        data.activeMembers = data.allMembers.stream()
            .filter(member -> !absentMemberNames.contains(member.getNickName()))
            .collect(Collectors.toList());

        data.absentMembers = data.allMembers.stream()
            .filter(member -> absentMemberNames.contains(member.getNickName()))
            .collect(Collectors.toList());

        log.info("技术经理 {} 团队成员过滤：总人数{}，请假人数{}，有效人数{}",
            data.managerName, data.allMembers.size(), data.absentMembers.size(), data.activeMembers.size());
    }

    /**
     * 获取团队绩效记录ID
     */
    private List<Long> getTeamPerformanceIds(TeamCalculationData data) {
        List<Long> performanceIds = performanceService.list(new LambdaQueryWrapper<Performance>()
                .eq(Performance::getYear, data.year)
                .eq(Performance::getMonth, data.month)
                .eq(Performance::getGroupId, data.manager.getDeptId()))
                .stream()
                .map(Performance::getId)
                .collect(Collectors.toList());

        if (performanceIds.isEmpty()) {
            throw new ServiceException(String.format("技术经理 %s 的团队无绩效记录", data.managerName));
        }
        return performanceIds;
    }

    /**
     * 检查一票否决（是否有D级考勤指标）
     */
    private boolean checkDlevelVeto(TeamCalculationData data) {
        List<PerformanceIndicator> performanceIndicators = performanceIndicatorService.list(new LambdaQueryWrapper<PerformanceIndicator>()
            .in(PerformanceIndicator::getPerformanceId, data.performanceIds)
            .eq(PerformanceIndicator::getIndicatorCode, PerformanceIndicatorEnum.ATTENDANCE.getCode()));

        for (PerformanceIndicator performanceIndicator : performanceIndicators) {
            String memberLevel = performanceIndicator.getScoreLevel();
            if (ScoreLevelEnum.SCORE_D.getCode().equals(memberLevel)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 计算考勤统计数据
     */
    private void calculateAttendanceStatistics(TeamCalculationData data) {
        if (data.activeMembers.isEmpty()) {
            log.warn("技术经理 {} 的团队无有效成员（全部请假）", data.managerName);
            data.attendanceStatistics = new AttendanceStatistics(0L, 0L, 0L, 0.0, new HashMap<>(16));
            return;
        }

        List<String> memberNames = data.activeMembers.stream()
            .map(SysUser::getNickName)
            .collect(Collectors.toList());

        List<UserKqStat> teamAttendanceStats = userKqStatService.listByUserNamesAndKqYearAndKqMonth(
            memberNames, String.valueOf(data.year), String.valueOf(data.month));

        // 计算整组的迟到、早退、旷工之和
        Long totalLateCount = teamAttendanceStats.stream()
            .map(UserKqStat::getKqLateCount)
            .reduce(0L, Long::sum);

        Long totalEarlyLeaveCount = teamAttendanceStats.stream()
            .map(UserKqStat::getKqLeaveEarlyCount)
            .reduce(0L, Long::sum);

        Long totalAbsentCount = teamAttendanceStats.stream()
            .map(UserKqStat::getKqAbsenteeismDays)
            .reduce(0L, Long::sum);

        // 计算平均迟到次数
        double avgLateCount = teamAttendanceStats.isEmpty() ? 0.0 :
            (double) totalLateCount / teamAttendanceStats.size();

        // 创建个人考勤统计
        Map<String, PersonalAttendance> personalStats = new HashMap<>(CommConstants.CommonVal.SIXTEEN);
        for (UserKqStat stat : teamAttendanceStats) {
            PersonalAttendance personal = new PersonalAttendance(
                stat.getKqLateCount() != null ? stat.getKqLateCount().intValue() : CommConstants.CommonVal.ZERO,
                stat.getKqLeaveEarlyCount() != null ? stat.getKqLeaveEarlyCount().intValue() : CommConstants.CommonVal.ZERO,
                stat.getKqAbsenteeismDays() != null ? stat.getKqAbsenteeismDays().intValue() : CommConstants.CommonVal.ZERO
            );
            personalStats.put(stat.getKqUserName(), personal);
        }

        data.attendanceStatistics = new AttendanceStatistics(
            totalLateCount, totalEarlyLeaveCount, totalAbsentCount, avgLateCount, personalStats);
    }

    /**
     * 获取工作时长相关数据
     */
    private void getWorkTimeData(TeamCalculationData data) {
        // 获取所有组的绩效合计数据
        R<List<WorkTotalVO>> rWorkTotal = performStatService.getWorkTotal(data.year, data.month);
        if (rWorkTotal.getCode() != HttpStatus.SUCCESS) {
            throw new ServiceException(String.format("获取部门获取每月各组绩效合计失败，year:%s,month:%s,错误信息：%s",
                data.year, data.month, rWorkTotal.getMsg()));
        }

        // 获取当前组的平均每日工时(工作日)
        WorkTotalVO workTotal = rWorkTotal.getData().stream()
            .filter(item -> !item.getDeptId().equals(UserConstants.NEW_MEM_DEPT_ID) && !item.getDeptId().equals(UserConstants.PROJ_MG_TEAM))
            .filter(item -> item.getDeptId().equals(data.manager.getDeptId()))
            .findFirst()
            .orElse(null);

        if (workTotal == null) {
            throw new ServiceException(String.format("获取部门获取每月各组绩效合计失败，year:%s,month:%s,错误信息：%s",
                data.year, data.month, rWorkTotal.getMsg()));
        }

        data.workTimeData = new WorkTimeData(workTotal.getDailyWorkTime(), rWorkTotal.getData(), workTotal);
    }

    /**
     * 计算最终等级
     */
    private String calculateFinalLevel(TeamCalculationData data) {
        AttendanceStatistics attendanceStats = data.attendanceStatistics;
        WorkTimeData workTimeData = data.workTimeData;
        BigDecimal avgDailyWorkTime = workTimeData.avgDailyWorkTime;

        // 1、D绩效：如果有一个为D绩效则组长直接为D绩效（已在checkDlevelVeto中处理）

        // 2、C绩效：有以下情况之一：有>=3次早退或无故旷工情况、平均迟到次数>=11次
        if (attendanceStats.totalEarlyLeaveCount >= CommConstants.CommonVal.THREE ||
            attendanceStats.totalAbsentCount >= CommConstants.CommonVal.ONE) {
            data.levelReason = String.format("早退总数(%d)或无故旷工情况(%d)",
                attendanceStats.totalEarlyLeaveCount, attendanceStats.totalAbsentCount);
            return ScoreLevelEnum.SCORE_C.getCode();
        }
        if (attendanceStats.avgLateCount >= CommConstants.CommonVal.ELEVEN) {
            data.levelReason = String.format("平均迟到次数(%.1f)≥11次", attendanceStats.avgLateCount);
            return ScoreLevelEnum.SCORE_C.getCode();
        }

        // 3、S绩效：总工时>=8小时,部门排名前10%,无早退且月平均迟到次数<=3次
        if (avgDailyWorkTime.compareTo(new BigDecimal(CommConstants.CommonVal.EIGHT)) >= CommConstants.CommonVal.ZERO &&
            isTopPercent(workTimeData.workTotalList, workTimeData.workTotal, CommConstants.CommonVal.F_ONE_TENTH) &&
            attendanceStats.totalEarlyLeaveCount == CommConstants.CommonVal.ZERO &&
            attendanceStats.avgLateCount <= CommConstants.CommonVal.THREE) {

            data.levelReason = String.format("总工时(%.1f)≥8小时，部门排名前10%%，无早退，平均迟到(%.1f)≤3次",
                avgDailyWorkTime.doubleValue(), attendanceStats.avgLateCount);
            return ScoreLevelEnum.SCORE_S.getCode();
        }

        // 4、A绩效：总工时>=8小时,所用部门的平均工作时长排名前20%,无早退且月平均迟到次数<=5次
        if (avgDailyWorkTime.compareTo(new BigDecimal(CommConstants.CommonVal.EIGHT)) >= CommConstants.CommonVal.ZERO &&
            attendanceStats.totalEarlyLeaveCount == CommConstants.CommonVal.ZERO &&
            attendanceStats.avgLateCount <= CommConstants.CommonVal.FIVE &&
            isTopPercent(workTimeData.workTotalList, workTimeData.workTotal, CommConstants.CommonVal.F_TWO_TENTH)) {

            data.levelReason = String.format("总工时(%.1f)≥8小时，部门排名前20%%，无早退，平均迟到(%.1f)≤5次",
                avgDailyWorkTime.doubleValue(), attendanceStats.avgLateCount);
            return ScoreLevelEnum.SCORE_A.getCode();
        }

        // 5、B绩效：平均总工时>=7小时（请假情况除外）并且无早退、月平均迟到次数<=10次
        if (avgDailyWorkTime.compareTo(new BigDecimal(CommConstants.CommonVal.SEVEN)) >= CommConstants.CommonVal.ZERO &&
            attendanceStats.totalEarlyLeaveCount == CommConstants.CommonVal.ZERO &&
            attendanceStats.avgLateCount <= CommConstants.CommonVal.TEN) {

            data.levelReason = String.format("总工时(%.1f)≥7小时，无早退，平均迟到(%.1f)≤10次",
                avgDailyWorkTime.doubleValue(), attendanceStats.avgLateCount);
            return ScoreLevelEnum.SCORE_B.getCode();
        }

        data.levelReason = "默认B级";
        return ScoreLevelEnum.SCORE_B.getCode();
    }

    /**
     * 计算前几名的数据
     */
    private static boolean isTopPercent(List<WorkTotalVO> workTotalVOList, WorkTotalVO target, Float proportion) {
        List<WorkTotalVO> sorted = workTotalVOList.stream()
            .sorted(Comparator.comparing(WorkTotalVO::getWorkTime).reversed())
            .collect(Collectors.toList());

        // 2. 计算前 proportion 的人数
        int n = sorted.size();
        int topSize = Math.max(CommConstants.CommonVal.ONE, (int) (n * proportion));
        for (int i = topSize; i < sorted.size(); i++) {
            if (sorted.get(topSize - CommConstants.CommonVal.ONE).getWorkTime().compareTo(sorted.get(i).getWorkTime()) == CommConstants.CommonVal.ZERO) {
                topSize++;
            }
        }

        // 3. 直接判断 target 的排名
        int rank = IntStream.range(CommConstants.CommonVal.ZERO, n)
            .filter(i -> sorted.get(i).equals(target))
            .findFirst()
            .orElse(-CommConstants.CommonVal.ONE);
        return rank >= CommConstants.CommonVal.ZERO && rank < topSize;
    }

    @Override
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level) {
        return "";
    }

    /**
     * 生成详细日志内容
     */
    private String generateLogContent(TeamCalculationData data, String level) {
        StringBuilder logContent = new StringBuilder();

        logContent.append(String.format("[%s]在%s年%s月份的本组组员平均月考勤指标",
            data.managerName, data.year, data.month));

        // 基本统计信息
        logContent.append(String.format("，团队总人数：%d人", data.allMembers.size()));
        logContent.append(String.format("，有效成员数(排除请假)：%d人", data.activeMembers.size()));

        if (data.attendanceStatistics != null) {
            AttendanceStatistics stats = data.attendanceStatistics;
            logContent.append(String.format("，总迟到：%d次", stats.totalLateCount));
            logContent.append(String.format("，平均迟到：%.1f次", stats.avgLateCount));
            logContent.append(String.format("，总早退：%d次", stats.totalEarlyLeaveCount));
            logContent.append(String.format("，总旷工：%d天", stats.totalAbsentCount));
        }

        logContent.append("，评级：").append(level);

        // 成员考勤分布
        appendMemberAttendanceDistribution(logContent, data);

        // 请假成员信息
        if (!data.absentMembers.isEmpty()) {
            logContent.append("\n  请假成员（已排除）：");
            for (SysUser absentMember : data.absentMembers) {
                logContent.append(String.format("\n    %s：当月请假未上班", absentMember.getNickName()));
            }
        }

        // 评级原因
        if (data.hasDlevelMember) {
            logContent.append("\n  计算原因：组内有成员考勤指标为D级");
        } else if (data.levelReason != null) {
            logContent.append("\n  计算原因：").append(data.levelReason);
        }

        return logContent.toString();
    }

    /**
     * 添加成员考勤分布统计
     */
    private void appendMemberAttendanceDistribution(StringBuilder logContent, TeamCalculationData data) {
        if (data.attendanceStatistics != null &&
            data.attendanceStatistics.personalStats != null &&
            !data.attendanceStatistics.personalStats.isEmpty()) {

            logContent.append("\n  成员考勤分布：");
            for (Map.Entry<String, PersonalAttendance> entry : data.attendanceStatistics.personalStats.entrySet()) {
                String memberName = entry.getKey();
                PersonalAttendance attendance = entry.getValue();
                logContent.append(String.format("\n    %s：迟到%d次，早退%d次，旷工%d天",
                    memberName, attendance.lateCount, attendance.earlyLeaveCount, attendance.absentCount));
            }
        }
    }

    /**
     * 考勤统计数据类
     */
    private static class AttendanceStatistics {
        final Long totalLateCount;
        final Long totalEarlyLeaveCount;
        final Long totalAbsentCount;
        final double avgLateCount;
        final Map<String, PersonalAttendance> personalStats;

        AttendanceStatistics(Long totalLateCount, Long totalEarlyLeaveCount, Long totalAbsentCount,
                           double avgLateCount, Map<String, PersonalAttendance> personalStats) {
            this.totalLateCount = totalLateCount;
            this.totalEarlyLeaveCount = totalEarlyLeaveCount;
            this.totalAbsentCount = totalAbsentCount;
            this.avgLateCount = avgLateCount;
            this.personalStats = personalStats;
        }
    }

    /**
     * 个人考勤数据类
     */
    private static class PersonalAttendance {
        final int lateCount;
        final int earlyLeaveCount;
        final int absentCount;

        PersonalAttendance(int lateCount, int earlyLeaveCount, int absentCount) {
            this.lateCount = lateCount;
            this.earlyLeaveCount = earlyLeaveCount;
            this.absentCount = absentCount;
        }
    }

    /**
     * 工作时长数据类
     */
    private static class WorkTimeData {
        final BigDecimal avgDailyWorkTime;
        final List<WorkTotalVO> workTotalList;
        final WorkTotalVO workTotal;

        WorkTimeData(BigDecimal avgDailyWorkTime, List<WorkTotalVO> workTotalList, WorkTotalVO workTotal) {
            this.avgDailyWorkTime = avgDailyWorkTime;
            this.workTotalList = workTotalList;
            this.workTotal = workTotal;
        }
    }

    /**
     * 团队计算数据类
     */
    private static class TeamCalculationData {
        String managerName;
        Integer year;
        Integer month;
        SysUser manager;
        List<SysUser> allMembers;
        List<SysUser> activeMembers;
        List<SysUser> absentMembers;
        List<Long> performanceIds;

        boolean hasDlevelMember;
        AttendanceStatistics attendanceStatistics;
        WorkTimeData workTimeData;
        String levelReason;
    }
}

