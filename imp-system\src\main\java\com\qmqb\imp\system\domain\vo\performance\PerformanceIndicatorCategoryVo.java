package com.qmqb.imp.system.domain.vo.performance;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 绩效指标分类视图对象 tb_performance_indicator_category
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@ExcelIgnoreUnannotated
public class PerformanceIndicatorCategoryVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 绩效主表ID
     */
    @ExcelProperty(value = "绩效主表ID")
    private Long performanceId;

    /**
     * 分类编码
     */
    @ExcelProperty(value = "分类编码")
    private String categoryCode;

    /**
     * 分类名称
     */
    @ExcelProperty(value = "分类名称")
    private String categoryName;

    /**
     * 分类评分等级S/A/B/C/D
     */
    @ExcelProperty(value = "分类评分等级")
    private String categoryLevel;

    /**
     * 分类权重（百分比，如30表示30%）
     */
    @ExcelProperty(value = "分类权重")
    private Integer categoryWeight;

    /**
     * 分类得分（数值，如85.5）
     */
    @ExcelProperty(value = "分类得分")
    private Double categoryScore;

    /**
     * 分类计算日志
     */
    @ExcelProperty(value = "分类计算日志")
    private String categoryLog;
} 