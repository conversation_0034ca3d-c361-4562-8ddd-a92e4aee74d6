package com.qmqb.imp.web.controller.business;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.constant.HttpStatus;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.dto.TaskInfoQueryDTO;
import com.qmqb.imp.common.core.domain.dto.TaskQueryDTO;
import com.qmqb.imp.common.core.domain.dto.TaskStatisticsDTO;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.QueryGroup;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.bo.WorkDetailBo;
import com.qmqb.imp.system.domain.vo.*;
import com.qmqb.imp.system.service.IZtTaskService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 任务查询控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-30
 */
@RestController
@RequestMapping("/task")
public class TaskController extends BaseController {


    @Autowired
    private IZtTaskService taskService;

    @GetMapping("/query/page")
    public R<Page<TaskVO>> page(TaskQueryDTO request) {
        return R.ok(taskService.page(request));
    }

    /**
     * 个人任务明细
     * @param workDetail
     * @return
     */
    @GetMapping("/queryUserTask")
    public R<Page<TaskVO>> queryUserTask(@Validated(QueryGroup.class) WorkDetailBo workDetail) {
        return R.ok(taskService.queryUserTask(workDetail));
    }

    /**
     * 导出个人任务明细
     * @param workDetail
     * @param response
     */
    @PostMapping("/queryUserTask/export")
    public void exportUserTask(WorkDetailBo workDetail, HttpServletResponse response) {
        workDetail.setPageSize(Integer.MAX_VALUE);
        Page<TaskVO> taskVoPage = taskService.queryUserTask(workDetail);
        List<TaskVO> records = taskVoPage.getRecords();
        List<TaskExportVO> exportList = records.stream().map(item -> {
            TaskExportVO taskExportVO = new TaskExportVO();
            BeanUtils.copyProperties(item, taskExportVO);
            return taskExportVO;
        }).collect(Collectors.toList());
        ExcelUtil.exportCsv(exportList, "个人任务明细", TaskExportVO.class,response);
    }


    @GetMapping("/query/getTaskListByGroup")
    public TableDataInfo<TaskInfoVO> listTaskInfo(TaskInfoQueryDTO request) {
        return taskService.getTaskListByGroupAndMonth(request);
    }

    /**
     * 任务统计
     *
     * @param request
     * @return
     */
    @GetMapping("/statistics/noDoing")
    public TableDataInfo<TaskStatisticsVO> statisticsNoDoing(TaskStatisticsDTO request) {
        return taskService.statisticsNoDoing(request);
    }

    @PostMapping("/export")
    public void export(TaskQueryDTO request, HttpServletResponse response) {
        request.setPageNo(1);
        request.setPageSize(30000);
        Page<TaskVO> page = taskService.page(request);
        List<TaskExportVO> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(page.getRecords())) {
            List<TaskVO> records = page.getRecords();
            list = BeanUtil.copyToList(records, TaskExportVO.class);
        }
        ExcelUtil.exportCsv(list, "任务明细", TaskExportVO.class, response);
    }

}
