package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 【请填写功能名称】对象 zt_build
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@TableName("zt_build")
public class ZtBuild {

    private static final long serialVersionUID=1L;

    /**
     *
     */
    @TableId(value = "id")
    private Integer id;
    /**
     *
     */
    private Integer project;
    /**
     *
     */
    private Integer product;
    /**
     *
     */
    private String branch;
    /**
     *
     */
    private Integer execution;
    /**
     *
     */
    private String builds;
    /**
     *
     */
    private String name;
    /**
     *
     */
    private Integer system;
    /**
     *
     */
    @TableField("scmPath")
    private String scmPath;
    /**
     *
     */
    @TableField("filePath")
    private String filePath;
    /**
     *
     */
    private Date date;
    /**
     *
     */
    private String stories;
    /**
     *
     */
    private String bugs;
    /**
     *
     */
    @TableField("artifactRepoID")
    private Integer artifactRepoId;
    /**
     *
     */
    private String builder;
    /**
     *
     */
    @TableField("`desc`")
    private String desc;
    /**
     *
     */
    @TableField("`createdBy`")
    private String createdBy;
    /**
     *
     */
    @TableField("`createdDate`")
    private Date createdDate;
    /**
     *
     */
    private String deleted;

}
