package com.qmqb.imp.system.service.indicator;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qmqb.imp.system.domain.performance.PerformanceFeedbackMainIndicatorResult;
import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackMainIndicatorResultBo;
import com.qmqb.imp.system.domain.vo.performance.PerformanceFeedbackMainIndicatorResultVo;

import java.util.List;

/**
 * 绩效反馈主表与绩效指标结果中间表Service接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface IPerformanceFeedbackMainIndicatorResultService extends IService<PerformanceFeedbackMainIndicatorResult> {

    /**
     * 批量插入中间表数据
     * @param mainId 主表ID
     * @param indicatorResultIds 指标结果ID列表
     * @return 是否成功
     */
    boolean saveBatchByMainId(Long mainId, List<Long> indicatorResultIds);

    /**
     * 根据主表ID查询所有指标结果ID
     * @param mainId 主表ID
     * @return 指标结果ID列表
     */
    List<Long> getIndicatorResultIdsByMainId(Long mainId);
} 