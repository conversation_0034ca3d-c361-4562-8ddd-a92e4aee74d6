package com.qmqb.imp.system.domain.vo.performance;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 绩效指标视图对象 tb_performance_indicator
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
@ExcelIgnoreUnannotated
public class PerformanceIndicatorVo {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 绩效主表ID
     */
    @ExcelProperty(value = "绩效主表ID")
    private Long performanceId;

    /**
     * 指标编码
     */
    @ExcelProperty(value = "指标编码")
    private String indicatorCode;

    /**
     * 指标名称
     */
    @ExcelProperty(value = "指标名称")
    private String indicatorName;

    /**
     * 指标分类编码
     */
    @ExcelProperty(value = "指标分类编码")
    private String categoryCode;

    /**
     * 评价等级S/A/B/C/D
     */
    @ExcelProperty(value = "评价等级S/A/B/C/D")
    private String scoreLevel;

    /**
     * 日志登记内容
     */
    @ExcelProperty(value = "日志登记内容")
    private String logContent;


}
