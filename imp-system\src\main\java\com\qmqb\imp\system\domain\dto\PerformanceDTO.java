package com.qmqb.imp.system.domain.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.qmqb.imp.system.domain.performance.Performance;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
  * <AUTHOR>
  * @date 2025/7/1 9:18
  * @Description TODO
  * @param TODO
  * @MethodName
  */

@Data
@Builder
public class PerformanceDTO  implements Serializable {

    /**
     *
     */
    private Long id;
    /**
     * 员工昵称
     */
    private String nickName;
    /**
     * 绩效年份
     */
    private Integer year;
    /**
     * 绩效月份，格式MM
     */
    private Integer month;
    /**
     * 所属组ID
     */
    private Long groupId;
    /**
     * 所属组
     */
    private String groupName;
    /**
     * 角色ID
     */
    private Long roleId;
    /**
     * 角色
     */
    private String role;
    /**
     * 总评等级S/A/B/C/D
     */
    private String totalLevel;
    /**
     * 评审绩效等级S/A/B/C/D
     */
    private String reviewLevel;
    /**
     * 最终绩效等级S/A/B/C/D
     */
    private String finalLevel;
    /**
     * 核准时间
     */
    private Date approvalTime;
    /**
     * 是否邮件报送，0为不发，1为发送
     */
    private String emailSentFlag;
    /**
     * 邮件报送时间
     */
    private Date emailSentTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 工作成果
     */
    private String workAchievementSummary;

     /**
      * 质量
      */
     private String workQualitySummary;

     /**
      * 工作协助能力
      */
     private String collaborationAbilitySummary;

     /**
      * 工作成果绩效
      */
     private String workAchievement;

     /**
      * 质量绩效
      */
     private String workQuality;

     /**
      * 工作协助能力绩效
      */
     private String collaborationAbility;

     /**
      * 工作协助能力绩效排序
      */
     private String workAchievementSort;
     /**
      * 工作质量绩效排序
      */
     private String workQualitySort;
     /**
      * 工作协调能力排序
      */
     private String collaborationAbilitySort;

     /**
      * 综合等级排序
      */
     private String totalLevelSort;

     /**
      * 评审排序
      */
     private String reviewLevelSort;

     /**
      * 最终排序
      */
     private String finalLevelSort;
}


