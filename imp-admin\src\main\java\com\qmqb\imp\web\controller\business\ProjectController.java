package com.qmqb.imp.web.controller.business;


import cn.dev33.satoken.annotation.SaIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.dto.BranchStatisticsDTO;
import com.qmqb.imp.system.domain.bo.ProjectBo;
import com.qmqb.imp.system.domain.vo.ProjectVo;
import com.qmqb.imp.system.service.IProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/proejct/query")
public class ProjectController extends BaseController {

    @Autowired
    private IProjectService iProjectService;

    /**
     * 分页查询项目信息
     * @param request
     * @return
     */
    @GetMapping("/page")
    public R<Page<ProjectVo>> page(ProjectBo request) {
        return R.ok(iProjectService.page(request));
    }

    /**
     * 编辑项目信息
     * @param request
     * @return
     */
    @PostMapping("/edit")
    public R<Boolean> edit(@RequestBody ProjectBo request){
        return R.ok(iProjectService.edit(request));
    }

    /**
     * 分支数量
     * @param number 大于分支数
     * @return
     */
    @SaIgnore
    @GetMapping("/branch/statistics/list")
    public R<List<BranchStatisticsDTO>> branchStatisticsList(Integer number){
        return R.ok(iProjectService.branchStatisticsList(number));
    }


}
