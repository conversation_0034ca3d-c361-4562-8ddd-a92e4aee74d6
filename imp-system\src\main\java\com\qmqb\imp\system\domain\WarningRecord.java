package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

/**
 * 【请填写功能名称】对象 tb_warning_record
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
@TableName("tb_warning_record")
public class WarningRecord {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 预警类型
     */
    private String type;
    /**
     * 发送时间
     */
    private LocalDate sendTime;
    /**
     * 预警内容
     */
    private String context;
    /**
     * 预警参数
     */
    private String params;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
