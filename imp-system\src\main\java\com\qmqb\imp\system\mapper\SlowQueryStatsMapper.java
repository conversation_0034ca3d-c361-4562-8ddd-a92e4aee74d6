package com.qmqb.imp.system.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.system.domain.SlowQueryStats;
import com.qmqb.imp.system.domain.vo.SlowSqlCountVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10 14:29:51
 */
@DS(DataSource.SLOWSQL)
@Mapper
public interface SlowQueryStatsMapper extends BaseMapper<SlowQueryStats> {

    /**
     * 获取各月慢sql数量统计
     *
     * @param year
     * @param status
     * @return
     */
    List<SlowSqlCountVO> statSlowSqlCount(@Param("year") Integer year,@Param("status")Integer status);
}
