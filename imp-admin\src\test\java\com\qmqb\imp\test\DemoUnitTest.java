package com.qmqb.imp.test;

import com.qmqb.imp.common.config.RuoYiConfig;
import com.qmqb.imp.system.domain.bo.DeptWarnAnalysisBo;
import com.qmqb.imp.system.service.WarnAnalysisService;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 15:44 2025/7/5
 * @Description TODO
 * @MethodName 单元测试
 * @param null
 * @return null
 */
@SpringBootTest
@DisplayName("单元测试案例")
public class DemoUnitTest {

    @Autowired
    private RuoYiConfig ruoYiConfig;
    @Autowired
    private WarnAnalysisService warnAnalysisService;

    @DisplayName("测试 @SpringBootTest @Test @DisplayName 注解")
    @Test
    public void testTest() {
        System.out.println(ruoYiConfig);
    }

    @Disabled
    @DisplayName("测试 @Disabled 注解")
    @Test
    public void testDisabled() {
        System.out.println(ruoYiConfig);
    }

    @Test
    public void testTimeout() throws InterruptedException {
        DeptWarnAnalysisBo bo = new DeptWarnAnalysisBo();
        warnAnalysisService.getDeptWarnAnalysisList(bo);
    }



    @DisplayName("测试 @RepeatedTest 注解")
    @RepeatedTest(3)
    public void testRepeatedTest() {
        System.out.println(666);
    }

    @BeforeAll
    public static void testBeforeAll() {
        System.out.println("@BeforeAll ==================");
    }

    @BeforeEach
    public void testBeforeEach() {
        System.out.println("@BeforeEach ==================");
    }

    @AfterEach
    public void testAfterEach() {
        System.out.println("@AfterEach ==================");
    }

    @AfterAll
    public static void testAfterAll() {
        System.out.println("@AfterAll ==================");
    }

}
