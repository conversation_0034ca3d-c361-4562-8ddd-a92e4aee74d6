package com.qmqb.imp.system.service;

import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.bo.*;
import com.qmqb.imp.system.domain.vo.SlowMonthStatsVo;
import com.qmqb.imp.system.domain.vo.SlowQueryDbListVO;
import com.qmqb.imp.system.domain.vo.SlowQueryDetailVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 慢sql列表Service接口
 * @date 2025/5/8 17:42
 */
public interface ISlowQueryDbListService {


    /**
     * 获取系统列表
     *
     * @return
     */
    List<SlowQueryDbListVO> list();

    /**
     * 分页获取慢sql列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SlowMonthStatsVo> slowSqlPage(SlowMonthStatsBo bo, PageQuery pageQuery);

    /**
     * 分页获取慢sql详细列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SlowQueryDetailVO> slowSqlDetailPage(SlowQueryDetailBO bo, PageQuery pageQuery);


    /**
     * 处理慢sql
     *
     * @param bo
     */
    void process(SlowQueryProcessBo bo);

    /**
     * 指派慢sql
     *
     * @param bo
     */
    void assign(SlowQueryAssignBo bo);

    /**
     * 查找处理人
     *
     * @param bo
     * @return
     */
    List<SysUser> processerList(ProcesserQueryParamsBO bo);
}
