package com.qmqb.imp.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.qmqb.imp.common.annotation.ExcelDictFormat;
import com.qmqb.imp.common.convert.ExcelDictConvert;
import lombok.Data;

import java.util.Date;

/**
 * 扫描项目文件记录视图对象 tb_scan_project_file
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
@ExcelIgnoreUnannotated
public class ScanProjectFileVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 项目id
     */
    @JsonProperty("pId")
    @ExcelProperty(value = "项目id")
    private Long pId;

    /**
     * 项目名称
     */
    @ExcelProperty(value = "项目名称")
    private String scanName;

    /**
     * 扫描版本号
     */
    @ExcelProperty(value = "扫描版本号")
    private Long scanVersion;

    /**
     * 是否最新扫描（1新纪录，0旧记录）
     */
    @ExcelProperty(value = "是否最新扫描", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=新纪录,0=旧记录")
    private Integer lastScanFlag;

    /**
     * 问题文件路径
     */
    @ExcelProperty(value = "问题文件路径")
    private String scanFileUrl;

    /**
     * 严重问题数（master分支）
     */
    @ExcelProperty(value = "严重问题数")
    private Long blockerAmount;

    /**
     * 一般问题数（master分支）
     */
    @ExcelProperty(value = "一般问题数")
    private Long criticalAmount;

    /**
     * 状态（0未指派，1已指派未处理，2已指派已处理）
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=未指派,1=已指派未处理,2=已指派已处理")
    private String status;

    /**
     * 处理人
     */
    @ExcelProperty(value = "处理人")
    private String handleUser;

    /**
     * 处理人id
     */
    @ExcelProperty(value = "处理人id")
    private Long handleUserId;

    /**
     * 处理时间
     */
    @ExcelProperty(value = "处理时间")
    private Date handleTime;

    /**
     * 处理说明
     */
    @ExcelProperty(value = "处理说明")
    private String handleRemark;

    /**
     * 指派人
     */
    @ExcelProperty(value = "指派人")
    private String assignUser;

    /**
     * 指派人id
     */
    @ExcelProperty(value = "指派人id")
    private Long assignUserId;

    /**
     * 指派时间
     */
    @ExcelProperty(value = "指派时间")
    private Date assignTime;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;
} 