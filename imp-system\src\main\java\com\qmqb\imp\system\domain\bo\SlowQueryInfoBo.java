package com.qmqb.imp.system.domain.bo;

import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 慢sql信息业务对象 tb_slow_query_info
 *
 * <AUTHOR>
 * @date 2025-05-28
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class SlowQueryInfoBo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 数据库名称
     */
    private String dbName;
    /**
     * 客户端IP
     */
    private String ip;



    /**
     * 处理状态
     */
    private String processStatus;





}
