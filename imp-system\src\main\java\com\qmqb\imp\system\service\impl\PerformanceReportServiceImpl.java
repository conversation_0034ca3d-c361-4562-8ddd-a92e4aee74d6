package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qmqb.imp.common.enums.PerformanceReportTypeEnum;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.qmqb.imp.system.domain.bo.PerformanceReportBo;
import com.qmqb.imp.system.domain.PerformanceReport;
import com.qmqb.imp.system.mapper.PerformanceReportMapper;
import com.qmqb.imp.system.service.IPerformanceReportService;

import java.util.Date;
import java.util.List;

/**
 * 绩效分析报告Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@RequiredArgsConstructor
@Service
public class PerformanceReportServiceImpl implements IPerformanceReportService {

    private static final Logger log = LoggerFactory.getLogger(PerformanceReportServiceImpl.class);
    private final PerformanceReportMapper baseMapper;

    @Override
    public Boolean saveReport(PerformanceReportBo bo) {
        // 构建查询条件
        LambdaQueryWrapper<PerformanceReport> queryWrapper = new LambdaQueryWrapper<PerformanceReport>()
            .eq(PerformanceReport::getYear, bo.getYear())
            .eq(PerformanceReport::getMonth, bo.getMonth());

        // 根据报告类型设置查询条件
        PerformanceReportTypeEnum reportType = PerformanceReportTypeEnum.getByCode(bo.getReportType());
        switch (reportType) {
            case PERSON:
                queryWrapper.eq(PerformanceReport::getReportType, PerformanceReportTypeEnum.PERSON.getCode());
                queryWrapper.eq(PerformanceReport::getUserName, bo.getUserName());
                break;
            case GROUP:
                queryWrapper.eq(PerformanceReport::getReportType, PerformanceReportTypeEnum.GROUP.getCode());
                queryWrapper.eq(PerformanceReport::getGroupName, bo.getGroupName());
                break;
            case POST:
                queryWrapper.eq(PerformanceReport::getReportType, PerformanceReportTypeEnum.POST.getCode());
                queryWrapper.eq(PerformanceReport::getPostName, bo.getPostName());
                break;
            default:
                throw new IllegalArgumentException("无效的报告类型: " + bo.getReportType());
        }

        PerformanceReport existingReport = baseMapper.selectOne(queryWrapper);

        PerformanceReport report = BeanUtil.toBean(bo, PerformanceReport.class);
        report.setUpdateTime(new Date());

        if (existingReport == null) {
            report.setCreateTime(new Date());
            return baseMapper.insert(report) > 0;
        } else {
            report.setId(existingReport.getId());
            return baseMapper.updateById(report) > 0;
        }
    }

    @Override
    public List<PerformanceReport> getListByDate(String year, String month) {
        return baseMapper.selectList(new LambdaQueryWrapper<PerformanceReport>()
            .eq(PerformanceReport::getYear, year)
            .eq(PerformanceReport::getMonth, month));
    }
}
