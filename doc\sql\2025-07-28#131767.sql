ALTER TABLE `tb_user_leave` ADD COLUMN `leave_type`
    VARCHAR(2) DEFAULT NULL COMMENT '请假类型(1-年假 2-事假 3-病假 4-调休 5-产假 6-陪产假 7-婚假 8-产检假 9-丧假 10-育儿假 11-护理假)';

ALTER table `tb_project` ADD COLUMN `p_has_master`
    tinyint(4) DEFAULT 0 COMMENT '是否有master分支(0无，1有)' after `p_branch_count`;

ALTER table `tb_project` ADD COLUMN `p_master_protected`
    tinyint(4) DEFAULT 0 COMMENT 'master分支是否受保护(0无，1有)' after `p_has_master`;
