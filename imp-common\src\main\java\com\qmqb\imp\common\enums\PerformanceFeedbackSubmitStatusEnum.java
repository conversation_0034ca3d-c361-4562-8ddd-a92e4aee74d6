package com.qmqb.imp.common.enums;

import lombok.Getter;

/**
 * 绩效反馈提交状态枚举
 * <p>
 * 定义了绩效反馈的提交状态，包括：
 * - 未提交：反馈记录已创建但未提交
 * - 已提交：反馈记录已提交等待审核
 * - 待重提：反馈记录被拒绝后等待重新提交
 * </p>
 *
 * <AUTHOR>
 */
@Getter
public enum PerformanceFeedbackSubmitStatusEnum {
    /**
     * 未提交
     */
    NOT_SUBMITTED("NOT_SUBMITTED", "未提交"),

    /**
     * 已提交
     */
    SUBMITTED("SUBMITTED", "已提交"),

    /**
     * 待重提
     */
    PENDING_RESUBMIT("PENDING_RESUBMIT", "待重提");

    /**
     * 状态编码
     */
    private final String code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 构造方法
     *
     * @param code 状态编码
     * @param name 状态名称
     */
    PerformanceFeedbackSubmitStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static PerformanceFeedbackSubmitStatusEnum getByCode(String code) {
        for (PerformanceFeedbackSubmitStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为有效状态
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidStatus(String code) {
        return getByCode(code) != null;
    }
}
