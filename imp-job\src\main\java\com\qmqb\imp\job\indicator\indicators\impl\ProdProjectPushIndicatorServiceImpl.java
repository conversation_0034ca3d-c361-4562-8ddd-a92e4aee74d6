package com.qmqb.imp.job.indicator.indicators.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.bo.ProjectResultBo;
import com.qmqb.imp.system.domain.vo.ProjectResultVo;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.service.IProjectResultService;
import com.qmqb.imp.system.service.ISysUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目成功发布数
 *
 * <AUTHOR>
 */
@Service
public class ProdProjectPushIndicatorServiceImpl implements IndicatorLevelCalcService {

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private IProjectResultService projectResultService;

    @Override
    public String getIndicatorCode() {
        return PerformanceIndicatorEnum.PROJECT_RELEASE_COUNT.getCode();
    }


    /**
     * S级标准：至少完成15个
     */
    private static final int S_LEVEL_COUNT = 15;

    /**
     * A级标准：至少完成10个
     */
    private static final int A_LEVEL_COUNT = 10;

    /**
     * B级标准：至少完成5个
     */
    private static final int B_LEVEL_COUNT = 5;



    @Override
    public IndicatorCalcResult calcLevel(String nickName, Integer month, List<TrackWorkResultVO> trackWorkResults) {
        Optional<TrackWorkResultVO> userWorkResultOpt = trackWorkResults.stream()
            .filter(r -> nickName.equals(r.getWorkUsername()))
            .findFirst();

        if (!userWorkResultOpt.isPresent()) {
            String indicatorName = "未知指标";
            try {
                indicatorName = PerformanceIndicatorEnum.valueOf(getIndicatorCode().toUpperCase()).getName();
            } catch (IllegalArgumentException e) {
                // Ignore if enum not found
            }
            return new IndicatorCalcResult(ScoreLevelEnum.SCORE_C.getCode(),
                String.format("员工[%s]在%s月份的%s数据未找到", nickName, month, indicatorName));
        }

        TrackWorkResultVO userWorkResult = userWorkResultOpt.get();
        List<ProjectResultVo> data = new ArrayList<>();
        String level = calculateLevel(userWorkResult, nickName, trackWorkResults, data);
        String logContent = createLogContent(userWorkResult, nickName, level, data);
        return new IndicatorCalcResult(level, logContent);

    }

    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults, List<ProjectResultVo> data) {
        Calendar cal = Calendar.getInstance();
        cal.clear();
        cal.set(Calendar.YEAR, workResult.getWorkYear());
        // Calendar的月份从0开始
        cal.set(Calendar.MONTH, workResult.getWorkMonth() - 1);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        // 获取当月开始时间
        Date currentMonthStart = cal.getTime();
        cal.add(Calendar.MONTH, 1);
        Date currentMonthEnd = new Date(cal.getTimeInMillis() - 1);

        SysUser sysUser = sysUserService.selectUserByNickName(nickName);

        ProjectResultBo projectResultBo = new ProjectResultBo();
        projectResultBo.setStatus("3");
        projectResultBo.setCompletionTimeStart(currentMonthStart);
        projectResultBo.setCompletionTimeEnd(currentMonthEnd);
        projectResultBo.setProjectManagers(sysUser.getUserName());

        List<ProjectResultVo> projectResults = projectResultService.queryList(projectResultBo);

        // 直接统计数量，因为SQL已经过滤了条件
        int number = CollectionUtil.isEmpty(projectResults) ? 0 : projectResults.size();
        data.addAll(projectResults);
        if (number >= S_LEVEL_COUNT) {
            return ScoreLevelEnum.SCORE_S.getCode();
        }else if (number >= A_LEVEL_COUNT) {
            return ScoreLevelEnum.SCORE_A.getCode();
        }else if (number >= B_LEVEL_COUNT) {
            return ScoreLevelEnum.SCORE_B.getCode();
        }else if (number == 0) {
            return ScoreLevelEnum.SCORE_D.getCode();
        }else {
            return ScoreLevelEnum.SCORE_C.getCode();
        }
    }
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level, List<ProjectResultVo> data) {
        Integer year = workResult.getWorkYear();
        Integer month = workResult.getWorkMonth();
        int size =  CollectionUtil.isEmpty(data) ? 0 :data.size();
        StringBuilder logContent = new StringBuilder();
        logContent.append(String.format("[%s]在%s年%s月份项目成果完成数: %s个",
            nickName, year, month, size));
        logContent.append(String.format("，评级：%s", level));
        String reason = getRatingReason(level, size);
        if (reason != null) {
            logContent.append(String.format("，原因：%s", reason));
        }
        return logContent.toString();
    }

    /**
     * 获取评级原因
     */
    private String getRatingReason(String level, int size) {
        switch (level) {
            case "S":
                return String.format("月项目成果完成数(%d个)达到S级标准(≥%d个)", size, S_LEVEL_COUNT);
            case "A":
                return String.format("月项目成果完成数(%d个)达到A级标准(≥%d个)",size, A_LEVEL_COUNT);
            case "B":
                return String.format("月项目成果完成数(%d个)达到B级标准(≥%d个)",size, B_LEVEL_COUNT);
            case "C":
                return String.format("月项目成果完成数(%d个)达到C级标准(<%d个)",size, B_LEVEL_COUNT);
            case "D":
                return "月项目成果完成数达到D级标准(无完成数)";
            default:
                return null;
        }
    }
    @Override
    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults) {
        return "";
    }

    @Override
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level) {
        return "";
    }
}
