package com.qmqb.imp.system.domain.bo.performance;

import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-07-01 17:14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PerformanceTutoringQueryBo extends BaseEntity {

    /**
     * 一类指标
     */
    private String firstIndecator;
    /**
     * 二类指标
     */
    private String secondIndecator;
    /**
     * 绩效级别(S/A/B/C/D)
     */
    private String level;

    /**
     * 姓名
     */
    private String nickName;

    /**
     * 岗位
     */
    private Long personType;

    /**
     * 所属组
     */
    private Long groupId;

}
