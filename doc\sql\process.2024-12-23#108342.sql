begin;

-- 新增菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1868939801902202881, '钉钉流程管理', 0, 1, 'process', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'tree', 'admin', '2024-12-17 16:42:27', 'admin', '2024-12-18 09:54:23', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1868940838159532033, '运维技术协助申请', 1868939801902202881, 10, 'maintenanceAssistance', 'process/maintenanceAssistance/index', NULL, 1, 0, 'C', '0', '0', 'system:processMaintenanceAssistance:list', 'job', 'admin', '2024-12-17 16:46:34', 'admin', '2024-12-23 11:10:53', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869199803057467393, '业务敏感信息导出申请', 1868939801902202881, 11, 'dataExport', 'process/dataExport/index', '', 1, 0, 'C', '0', '0', 'system:processDataExport:list', 'education', 'admin', '2024-12-18 09:55:36', 'admin', '2024-12-18 09:55:49', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869222286766702593, '生产数据库查询权限申请', 1868939801902202881, 12, 'dbQueryPermission', 'process/dbQueryPermission/index', NULL, 1, 0, 'C', '0', '0', 'system:processDbQueryPermission:list', 'search', 'admin', '2024-12-18 11:24:56', 'admin', '2024-12-18 11:24:56', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869273764491489281, '设备借用流程', 1868939801902202881, 13, 'equipmentBorrowing', 'process/equipmentBorrowing/index', NULL, 1, 0, 'C', '0', '0', 'system:processEquipmentBorrowing:list', 'tree-table', 'admin', '2024-12-18 14:49:30', 'admin', '2024-12-18 14:49:30', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869274173683593217, '生产应用每周巡检流程', 1868939801902202881, 14, 'cycleInspection', 'process/cycleInspection/index', NULL, 1, 0, 'C', '0', '0', 'system:processCycleInspection:list', 'clipboard', 'admin', '2024-12-18 14:51:07', 'admin', '2024-12-18 15:25:34', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869680091311181826, '敏感用户数据加解密', 1868939801902202881, 15, 'userDataEncryption', 'process/userDataEncryption/index', NULL, 1, 0, 'C', '0', '0', 'system:processUserDataEncryption:list', 'peoples', 'admin', '2024-12-19 17:44:05', 'admin', '2024-12-19 17:44:05', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869952138180300801, '运维&DBA&BI生产环境计划性维护操作申请', 1868939801902202881, 16, 'maintenanceEnvironment', 'process/maintenanceEnvironment/index', '', 1, 0, 'C', '0', '0', 'system:processMaintenanceEnvironment:list', 'example', 'admin', '2024-12-20 11:45:06', 'admin', '2024-12-20 11:45:17', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871102929560768514, '内部管理系统功能需求', 1868939801902202881, 1, 'internalManagementSystem', 'process/internalManagementSystem/index', NULL, 1, 0, 'C', '0', '0', 'system:processInternalManagementSystem:list', 'documentation', 'admin', '2024-12-23 15:57:56', 'admin', '2024-12-23 15:59:55', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871108766031253506, '信息系统账号及权限申请', 1868939801902202881, 9, 'accountPermissionRequest', 'process/accountPermissionRequest/index', NULL, 1, 0, 'C', '0', '0', 'system:processAccountPermissionRequest:list', 'lock', 'admin', '2024-12-23 16:21:08', 'admin', '2024-12-23 16:21:32', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871116167472058370, '风控规则开发', 1868939801902202881, 2, 'riskControlRules', 'process/riskControlRules/index', NULL, 1, 0, 'C', '0', '0', 'system:processRiskControlRules:list', 'component', 'admin', '2024-12-23 16:50:33', 'admin', '2024-12-23 16:50:58', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871128354315931650, '催收\\财务\\运营生产数据修复', 1868939801902202881, 8, 'financialDataModification', 'process/financialDataModification/index', NULL, 1, 0, 'C', '0', '0', 'system:processFinancialDataModification:list', 'logininfor', 'admin', '2024-12-23 17:38:58', 'admin', '2024-12-24 09:17:18', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871363392135598082, '生产维护SQL执行', 1868939801902202881, 7, 'sqlExecution', 'process/sqlExecution/index', NULL, 1, 0, 'C', '0', '0', 'system:processSqlExecution:list', 'zip', 'admin', '2024-12-24 09:12:56', 'admin', '2024-12-24 09:17:12', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871374612079153154, '系统功能异常报障处理', 1868939801902202881, 6, 'systemAlarm', 'process/systemAlarm/index', NULL, 1, 0, 'C', '0', '0', 'system:processSystemAlarm:list', 'bug', 'admin', '2024-12-24 09:57:31', 'admin', '2024-12-24 09:57:31', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871382138195644418, '移动端应用发布', 1868939801902202881, 5, 'mobileRelease', 'process/mobileRelease/index', NULL, 1, 0, 'C', '0', '0', 'system:processMobileRelease:list', 'phone', 'admin', '2024-12-24 10:27:25', 'admin', '2024-12-24 10:27:25', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871392357386653698, '内部管理系统发布', 1868939801902202881, 4, 'internalSystemRelease', 'process/internalSystemRelease/index', NULL, 1, 0, 'C', '0', '0', 'system:processInternalSystemRelease:list', 'cascader', 'admin', '2024-12-24 11:08:01', 'admin', '2024-12-24 11:08:01', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871436221329805313, '运营看板及报表需求', 1868939801902202881, 3, 'operationsDashboard', 'process/operationsDashboard/index', NULL, 1, 0, 'C', '0', '0', 'system:processOperationsDashboard:list', 'chart', 'admin', '2024-12-24 14:02:19', 'admin', '2024-12-24 14:02:19', '');

-- 新增字典类型
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869572309262061569, '运维技术协助申请', 'process_maintenance_assistance', '0', 'admin', '2024-12-19 10:35:48', 'admin', '2024-12-19 10:46:59', '运维技术协助申请下拉选择框数据');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869575291814858754, '业务敏感信息导出申请', 'process_data_export', '0', 'admin', '2024-12-19 10:47:39', 'admin', '2024-12-19 10:47:55', '业务敏感信息导出申请下拉选择数据');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869632921526669314, '生产数据库查询权限申请', 'process_db_query_permission', '0', 'admin', '2024-12-19 14:36:39', 'admin', '2024-12-19 14:36:39', '生产数据库查询权限申请下拉框数据');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869635589548609537, '设备借用流程', 'process_equipment_borrowing', '0', 'admin', '2024-12-19 14:47:15', 'admin', '2024-12-19 14:47:15', '设备借用流程下拉框数据');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869636812259528706, '生产应用每周巡检流程', 'process_cycle_inspection', '0', 'admin', '2024-12-19 14:52:07', 'admin', '2024-12-19 14:52:07', '生产应用每周巡检流程下拉框数据');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869640365317361665, '敏感用户数据加解密', 'process_user_data_encryption', '0', 'admin', '2024-12-19 15:06:14', 'admin', '2024-12-19 15:06:26', '敏感用户数据加解密下拉框数据\n');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869641578813394945, '运维&DBA&BI生产环境维护操作申请', 'process_maintenance_environment', '0', 'admin', '2024-12-19 15:11:03', 'admin', '2024-12-23 09:31:03', '运维&DBA&BI生产环境维护操作申请下拉框数据');
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871107286142390274, '信息系统账号及权限申请', 'process_account_permission_request', '0', 'admin', '2024-12-23 16:15:15', 'admin', '2024-12-23 16:24:31', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871128622550061058, '催收\\财务\\运营生产数据修复', 'process_financial_data_modification', '0', 'admin', '2024-12-23 17:40:02', 'admin', '2024-12-23 17:40:02', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871362479719288833, '生产维护SQL执行', 'process_sql_execution', '0', 'admin', '2024-12-24 09:09:18', 'admin', '2024-12-24 09:09:18', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871373095183945730, '系统功能异常报障处理', 'process_system_alarm', '0', 'admin', '2024-12-24 09:51:29', 'admin', '2024-12-24 09:51:29', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871380449438830593, '移动端应用发布', 'process_mobile_release', '0', 'admin', '2024-12-24 10:20:42', 'admin', '2024-12-24 10:20:42', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871390877871083521, '内部管理系统发布', 'process_internal_system_release', '0', 'admin', '2024-12-24 11:02:09', 'admin', '2024-12-24 11:02:09', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871401583555219457, '运营看板及报表需求', 'process_operations_dashboard', '0', 'admin', '2024-12-24 11:44:41', 'admin', '2024-12-24 11:44:41', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871732749567098881, '内部管理系统功能需求', 'process_internal_management_system', '0', 'admin', '2024-12-25 09:40:37', 'admin', '2024-12-25 09:40:37', NULL);
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871741492358905858, '风控规则开发需求', 'process_risk_control_rules', '0', 'admin', '2024-12-25 10:15:22', 'admin', '2024-12-25 10:15:22', NULL);


-- 新增字典数据
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869572982582071298, 0, '当前审批结果', '审批通过,审批未通过', 'process_maintenance_assistance', NULL, 'default', 'N', '0', 'admin', '2024-12-19 10:38:29', 'admin', '2024-12-19 15:26:08', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869574325187170305, 1, '工作类型', '软硬件环境搭建,域名/服务器购置及备案,外部合作方技术支持,网络、线路方案规划,生产应用紧急处理,添加白名单,阿里云资源包购置,其他操作类工作', 'process_maintenance_assistance', NULL, 'default', 'N', '0', 'admin', '2024-12-19 10:43:49', 'admin', '2024-12-19 14:17:26', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869574691890974722, 2, '当前审批状态', '审批中,已结束,终止', 'process_maintenance_assistance', NULL, 'default', 'N', '0', 'admin', '2024-12-19 10:45:16', 'admin', '2024-12-19 10:45:22', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869632269379506177, 0, '当前审批结果', '审批通过,审批未通过', 'process_data_export', NULL, 'default', 'N', '0', 'admin', '2024-12-19 14:34:04', 'admin', '2024-12-19 15:23:49', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869632715317907457, 1, '当前审批状态', '审批中,已结束,终止', 'process_data_export', NULL, 'default', 'N', '0', 'admin', '2024-12-19 14:35:50', 'admin', '2024-12-19 15:24:15', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869634966895792129, 0, '当前审批状态', '审批中,已结束,终止', 'process_db_query_permission', NULL, 'default', 'N', '0', 'admin', '2024-12-19 14:44:47', 'admin', '2024-12-19 14:44:47', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869635118058508289, 1, '当前审批结果', '审批通过,审批未通过', 'process_db_query_permission', NULL, 'default', 'N', '0', 'admin', '2024-12-19 14:45:23', 'admin', '2024-12-19 14:45:23', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869636312013279234, 0, '当前审批状态', '审批中,已结束,终止', 'process_equipment_borrowing', NULL, 'default', 'N', '0', 'admin', '2024-12-19 14:50:08', 'admin', '2024-12-19 14:50:08', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869636537088020481, 1, '当前审批结果', '审批通过,审批未通过', 'process_equipment_borrowing', NULL, 'default', 'N', '0', 'admin', '2024-12-19 14:51:01', 'admin', '2024-12-19 14:51:01', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869637017386160129, 0, '当前审批状态', '审批中,已结束,终止', 'process_cycle_inspection', NULL, 'default', 'N', '0', 'admin', '2024-12-19 14:52:56', 'admin', '2024-12-19 14:52:56', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869637517720489986, 1, '当前审批结果', '审批通过,审批未通过', 'process_cycle_inspection', NULL, 'default', 'N', '0', 'admin', '2024-12-19 14:54:55', 'admin', '2024-12-19 14:54:55', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869640704137433090, 0, '当前审批状态', '审批中,已结束,终止', 'process_user_data_encryption', NULL, 'default', 'N', '0', 'admin', '2024-12-19 15:07:35', 'admin', '2024-12-19 15:07:35', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869640834827751426, 1, '当前审批结果', '审批通过,审批未通过', 'process_user_data_encryption', NULL, 'default', 'N', '0', 'admin', '2024-12-19 15:08:06', 'admin', '2024-12-19 15:08:06', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869991695370162178, 0, '操作分类_BI', '产品归类调整,数据库表字段调整,SQL优化,数据模型优化,其他', 'process_maintenance_environment', NULL, 'default', 'N', '0', 'admin', '2024-12-20 14:22:18', 'admin', '2024-12-20 14:22:18', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869992136724189185, 1, '操作分类_运维', '服务器调整,网络调整,白名单调整,域名调整,应用调整,新项目部署,其他', 'process_maintenance_environment', NULL, 'default', 'N', '0', 'admin', '2024-12-20 14:24:03', 'admin', '2024-12-20 14:24:03', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869992700447035393, 2, '操作分类_DBA', '数据库迁移,数据库升级,数据库优化,数据库部署,数据库管理,备份恢复,故障处理,数据同步,数据修复,数据归档,数据安全,SQL优化', 'process_maintenance_environment', NULL, 'default', 'N', '0', 'admin', '2024-12-20 14:26:17', 'admin', '2024-12-20 14:26:25', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869993217608912898, 3, '当前审批结果', '审批通过,审批未通过', 'process_maintenance_environment', NULL, 'default', 'N', '0', 'admin', '2024-12-20 14:28:21', 'admin', '2024-12-20 14:28:21', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869993430092353538, 4, '操作岗位', '运维岗,DBA岗,BI岗', 'process_maintenance_environment', NULL, 'default', 'N', '0', 'admin', '2024-12-20 14:29:11', 'admin', '2024-12-20 14:29:11', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869993709940510722, 5, '操作风险等级', 'P0,P1,P2,P3', 'process_maintenance_environment', NULL, 'default', 'N', '0', 'admin', '2024-12-20 14:30:18', 'admin', '2024-12-23 09:57:56', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1869993************, 6, '当前审批状态', '审批中,已结束,终止', 'process_maintenance_environment', NULL, 'default', 'N', '0', 'admin', '2024-12-20 14:31:04', 'admin', '2024-12-20 14:31:04', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871107755501785089, 0, '当前审批结果', '审批通过,审批未通过', 'process_account_permission_request', NULL, 'default', 'N', '0', 'admin', '2024-12-23 16:17:07', 'admin', '2024-12-23 16:17:07', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871108087745187841, 0, '当前审批状态', '审批中,已结束,终止', 'process_account_permission_request', NULL, 'default', 'N', '0', 'admin', '2024-12-23 16:18:26', 'admin', '2024-12-23 16:18:26', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871128900707913729, 0, '当前审批结果', '审批通过,审批未通过', 'process_financial_data_modification', NULL, 'default', 'N', '0', 'admin', '2024-12-23 17:41:08', 'admin', '2024-12-23 17:41:08', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871129092811231233, 0, '当前审批状态', '审批中,已结束,终止', 'process_financial_data_modification', NULL, 'default', 'N', '0', 'admin', '2024-12-23 17:41:54', 'admin', '2024-12-23 17:41:54', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871129978950225921, 0, '所属业务', '国内助贷业务,国内风控业务,国内贷后业务,国内电商业务,国内游戏业务,海外金融业务,海外游戏业务', 'process_financial_data_modification', NULL, 'default', 'N', '0', 'admin', '2024-12-23 17:45:26', 'admin', '2024-12-23 17:45:26', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871362745365532674, 0, '当前审批结果', '审批通过,审批未通过', 'process_sql_execution', NULL, 'default', 'N', '0', 'admin', '2024-12-24 09:10:21', 'admin', '2024-12-24 09:10:21', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871362889410514946, 0, '当前审批状态', '审批中,已结束,终止', 'process_sql_execution', NULL, 'default', 'N', '0', 'admin', '2024-12-24 09:10:56', 'admin', '2024-12-24 09:10:56', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871363101776515073, 0, '操作类型', '发生时补充执行,生产数据修复,查询生产数据,其他', 'process_sql_execution', NULL, 'default', 'N', '0', 'admin', '2024-12-24 09:11:46', 'admin', '2024-12-24 09:11:46', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871373511326011394, 0, '当前审批结果', '审批通过,审批未通过', 'process_system_alarm', NULL, 'default', 'N', '0', 'admin', '2024-12-24 09:53:08', 'admin', '2024-12-24 09:53:08', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871373586898980865, 0, '当前审批状态', '审批中,已结束,终止', 'process_system_alarm', NULL, 'default', 'N', '0', 'admin', '2024-12-24 09:53:26', 'admin', '2024-12-24 09:53:26', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871373800372277249, 0, '故障类型', '系统崩溃,访问出错/无提示,权限错误,显示出错,功能异常,界面错乱,操作无反应,流程混乱,不好定位,误操作', 'process_system_alarm', NULL, 'default', 'N', '0', 'admin', '2024-12-24 09:54:17', 'admin', '2024-12-24 10:00:03', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871381439089053697, 0, '当前审批结果', '审批通过,审批未通过', 'process_mobile_release', NULL, 'default', 'N', '0', 'admin', '2024-12-24 10:24:38', 'admin', '2024-12-24 10:24:38', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871381529518247937, 0, '当前审批状态', '审批中,已结束,终止', 'process_mobile_release', NULL, 'default', 'N', '0', 'admin', '2024-12-24 10:25:00', 'admin', '2024-12-24 10:25:00', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871381790789832705, 0, '移动端类型', 'Android,IOS,微信公众号,微信小程序', 'process_mobile_release', NULL, 'default', 'N', '0', 'admin', '2024-12-24 10:26:02', 'admin', '2024-12-24 10:26:02', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871391010574667777, 0, '当前审批状态', '审批中,已结束,终止', 'process_internal_system_release', NULL, 'default', 'N', '0', 'admin', '2024-12-24 11:02:40', 'admin', '2024-12-24 11:02:40', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871391077884858369, 0, '当前审批结果', '审批通过,审批未通过', 'process_internal_system_release', NULL, 'default', 'N', '0', 'admin', '2024-12-24 11:02:56', 'admin', '2024-12-24 11:02:56', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871391287767830529, 0, '发布的应用类型', '前端H5,后端JAVA,后端Python,插件或脚本,其他', 'process_internal_system_release', NULL, 'default', 'N', '0', 'admin', '2024-12-24 11:03:46', 'admin', '2024-12-24 11:03:46', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871391932897923073, 0, '业务大类', '助贷业务,风控业务,贷后业务,电商业务,海外金融业务,天金业务,BI,创新业务,其他', 'process_internal_system_release', NULL, 'default', 'N', '0', 'admin', '2024-12-24 11:06:20', 'admin', '2024-12-24 11:06:20', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871401815705751553, 0, '看板类型', '看板系统,metabase,新版业务驾驶仓,其他', 'process_operations_dashboard', NULL, 'default', 'N', '0', 'admin', '2024-12-24 11:45:36', 'admin', '2024-12-24 11:45:36', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871401884257456130, 0, '当前审批结果', '审批通过,审批未通过', 'process_operations_dashboard', NULL, 'default', 'N', '0', 'admin', '2024-12-24 11:45:53', 'admin', '2024-12-24 11:45:53', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871402203976667137, 0, '业务大类', '国内助贷业务,国内风控业务,国内贷后业务,国内电商业务,国内游戏业务,海外金融业务,海外游戏业务', 'process_operations_dashboard', NULL, 'default', 'N', '0', 'admin', '2024-12-24 11:47:09', 'admin', '2024-12-24 11:47:09', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871402394842664961, 0, '需求优先度', '重要且紧急,重要不紧急,不重要可排期开发,有空再处理', 'process_operations_dashboard', NULL, 'default', 'N', '0', 'admin', '2024-12-24 11:47:55', 'admin', '2024-12-24 11:47:55', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871402473922072578, 0, '当前审批状态', '审批中,已结束,终止', 'process_operations_dashboard', NULL, 'default', 'N', '0', 'admin', '2024-12-24 11:48:13', 'admin', '2024-12-24 11:48:13', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871732998402572290, 0, '需求优先度', '重要且紧急,重要且不紧急,重要但可逐步排期,可有空再处理', 'process_internal_management_system', NULL, 'default', 'N', '0', 'admin', '2024-12-25 09:41:37', 'admin', '2024-12-25 09:41:37', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871733226849533954, 0, '当前审批状态', '审批中,已结束,终止', 'process_internal_management_system', NULL, 'default', 'N', '0', 'admin', '2024-12-25 09:42:31', 'admin', '2024-12-25 09:42:44', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871733369699139586, 0, '当前审批结果', '审批通过,审批未通过', 'process_internal_management_system', NULL, 'default', 'N', '0', 'admin', '2024-12-25 09:43:05', 'admin', '2024-12-25 09:43:05', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871733549643169793, 0, '业务大类', '国内助贷业务,国内风控业务,国内贷后业务,海外金融业务', 'process_internal_management_system', NULL, 'default', 'N', '0', 'admin', '2024-12-25 09:43:48', 'admin', '2024-12-25 09:43:48', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871741672810446849, 0, '需求优先度', '重要且紧急,重要不紧急,不重要可排期处理,有空再处理', 'process_risk_control_rules', NULL, 'default', 'N', '0', 'admin', '2024-12-25 10:16:05', 'admin', '2024-12-25 10:16:05', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871741779157024769, 0, '当前审批状态', '审批中,已结束,终止', 'process_risk_control_rules', NULL, 'default', 'N', '0', 'admin', '2024-12-25 10:16:30', 'admin', '2024-12-25 10:16:30', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871741851944976385, 0, '当前审批结果', '审批通过,审批未通过', 'process_risk_control_rules', NULL, 'default', 'N', '0', 'admin', '2024-12-25 10:16:47', 'admin', '2024-12-25 10:16:47', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1871742003485179905, 0, '业务大类', '国内助贷业务,国内风控服务,海外借贷业务,海外风控服务', 'process_risk_control_rules', NULL, 'default', 'N', '0', 'admin', '2024-12-25 10:17:24', 'admin', '2024-12-25 10:17:24', NULL);


commit;

-- 新增16个审批单数据表
CREATE TABLE `tb_process_account_permission_request` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `process_instance_id` varchar(255) NOT NULL COMMENT '审批实例业务编号',
  `system_name` varchar(255) NOT NULL COMMENT '系统名称',
  `role_permission` text COMMENT '权限角色要求',
  `expected_open_date` datetime NOT NULL COMMENT '期望开通日期',
  `result` varchar(255) NOT NULL COMMENT '审批结果',
  `status` varchar(50) NOT NULL COMMENT '审批状态',
  `current_node` varchar(255) NOT NULL COMMENT '当前节点',
  `current_responsible_person` varchar(255) NOT NULL COMMENT '当前负责人',
  `last_action_time` datetime NOT NULL COMMENT '更新时间',
  `originator_user` varchar(255) NOT NULL COMMENT '创建人',
  `originator_time` datetime NOT NULL COMMENT '创建时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据表修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_pid` (`process_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='信息系统账号及权限申请';

CREATE TABLE `tb_process_cycle_inspection` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `process_instance_id` varchar(255) NOT NULL COMMENT '审批实例业务编号',
  `year` varchar(4) DEFAULT NULL COMMENT '年份',
  `month` varchar(2) DEFAULT NULL COMMENT '月份',
  `week_number` varchar(10) DEFAULT NULL COMMENT '周次',
  `inspector` varchar(255) DEFAULT NULL COMMENT '巡检人',
  `owning_development_team` varchar(255) DEFAULT NULL COMMENT '所属开发组',
  `inspection_detail` text COMMENT '应用巡检明细',
  `result` varchar(255) NOT NULL COMMENT '审批结果',
  `status` varchar(50) NOT NULL COMMENT '审批状态',
  `current_node` varchar(255) NOT NULL COMMENT '当前节点',
  `current_responsible_person` varchar(255) NOT NULL COMMENT '当前负责人',
  `last_action_time` datetime NOT NULL COMMENT '更新时间',
  `originator_user` varchar(255) NOT NULL COMMENT '创建人',
  `originator_time` datetime NOT NULL COMMENT '创建时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据表修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_pid` (`process_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='生产应用每周巡检流程';

CREATE TABLE `tb_process_data_export` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `process_instance_id` varchar(255) NOT NULL COMMENT '审批实例业务编号',
  `title` varchar(255) DEFAULT NULL COMMENT '标题',
  `data_environment` varchar(255) DEFAULT NULL COMMENT '涉及敏感数据项',
  `export_purpose` text COMMENT '导出用途',
  `expected_export_date` datetime NOT NULL COMMENT '期望导出日期',
  `result` varchar(255) NOT NULL COMMENT '审批结果',
  `status` varchar(50) NOT NULL COMMENT '审批状态',
  `current_node` varchar(255) NOT NULL COMMENT '当前节点',
  `current_responsible_person` varchar(255) NOT NULL COMMENT '当前负责人',
  `last_action_time` datetime NOT NULL COMMENT '更新时间',
  `originator_user` varchar(255) NOT NULL COMMENT '创建人',
  `originator_time` datetime NOT NULL COMMENT '创建时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据表修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_pid` (`process_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='业务敏感数据导出申请';

CREATE TABLE `tb_process_db_query_permission` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `process_instance_id` varchar(255) NOT NULL COMMENT '审批实例业务编号',
  `query_reason` text COMMENT '查询原因',
  `expected_user_name` varchar(255) DEFAULT NULL COMMENT '期望用户名',
  `expected_open_date` datetime NOT NULL COMMENT '期望开通日期',
  `result` varchar(255) NOT NULL COMMENT '审批结果',
  `status` varchar(50) NOT NULL COMMENT '审批状态',
  `current_node` varchar(255) NOT NULL COMMENT '当前节点',
  `current_responsible_person` varchar(255) NOT NULL COMMENT '当前负责人',
  `last_action_time` datetime NOT NULL COMMENT '更新时间',
  `originator_user` varchar(255) NOT NULL COMMENT '创建人',
  `originator_time` datetime NOT NULL COMMENT '创建时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据表修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_pid` (`process_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='生产数据库查询权限申请';

CREATE TABLE `tb_process_equipment_borrowing` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `process_instance_id` varchar(255) NOT NULL COMMENT '审批实例业务编号',
  `title` varchar(255) DEFAULT NULL COMMENT '标题',
  `equipment_details` varchar(255) DEFAULT NULL COMMENT '物品明细',
  `planned_return_date` datetime NOT NULL COMMENT '计划归还日期',
  `result` varchar(255) NOT NULL COMMENT '审批结果',
  `status` varchar(50) NOT NULL COMMENT '审批状态',
  `current_node` varchar(255) NOT NULL COMMENT '当前节点',
  `current_responsible_person` varchar(255) NOT NULL COMMENT '当前负责人',
  `last_action_time` datetime NOT NULL COMMENT '更新时间',
  `originator_user` varchar(255) NOT NULL COMMENT '创建人',
  `originator_time` datetime NOT NULL COMMENT '创建时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据表修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_pid` (`process_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备借用流程';

CREATE TABLE `tb_process_financial_data_modification` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `process_instance_id` varchar(255) NOT NULL COMMENT '审批实例业务编号',
  `title` varchar(255) DEFAULT NULL COMMENT '标题',
  `related_business` varchar(255) NOT NULL COMMENT '所属业务',
  `result` varchar(255) NOT NULL COMMENT '审批结果',
  `status` varchar(50) NOT NULL COMMENT '审批状态',
  `current_node` varchar(255) NOT NULL COMMENT '当前节点',
  `current_responsible_person` varchar(255) NOT NULL COMMENT '当前负责人',
  `last_action_time` datetime NOT NULL COMMENT '更新时间',
  `originator_user` varchar(255) NOT NULL COMMENT '创建人',
  `originator_time` datetime NOT NULL COMMENT '创建时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据表修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_pid` (`process_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='催收财务运营生产数据修复';

CREATE TABLE `tb_process_internal_management_system` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `process_instance_id` varchar(255) NOT NULL COMMENT '审批实例业务编号',
  `business_category` varchar(255) NOT NULL COMMENT '业务大类',
  `requirement_details` text COMMENT '需求明细',
  `expected_completion_date` datetime DEFAULT NULL COMMENT '期望完成日期',
  `priority_level` varchar(50) NOT NULL COMMENT '需求优先度',
  `result` varchar(255) NOT NULL COMMENT '审批结果',
  `status` varchar(50) NOT NULL COMMENT '审批状态',
  `current_responsible_person` varchar(255) NOT NULL COMMENT '当前负责人',
  `current_node` varchar(255) NOT NULL COMMENT '当前节点',
  `last_action_time` datetime NOT NULL COMMENT '更新时间',
  `originator_user` varchar(255) NOT NULL COMMENT '创建人',
  `originator_time` datetime NOT NULL COMMENT '创建时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据表修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_pid` (`process_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='内部管理系统功能需求';

CREATE TABLE `tb_process_internal_system_release` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `process_instance_id` varchar(255) NOT NULL COMMENT '审批实例业务编号',
  `business_category` varchar(255) NOT NULL COMMENT '业务大类',
  `system_name` varchar(255) DEFAULT NULL COMMENT '系统名称',
  `release_application_type` varchar(255) NOT NULL COMMENT '发布的应用类型',
  `scheduled_release_time` datetime DEFAULT NULL COMMENT '指定发布时间',
  `result` varchar(255) NOT NULL COMMENT '审批结果',
  `status` varchar(50) NOT NULL COMMENT '审批状态',
  `current_responsible_person` varchar(255) NOT NULL COMMENT '当前负责人',
  `current_node` varchar(255) NOT NULL COMMENT '当前节点',
  `last_action_time` datetime NOT NULL COMMENT '更新时间',
  `originator_user` varchar(255) NOT NULL COMMENT '创建人',
  `originator_time` datetime NOT NULL COMMENT '创建时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据表修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_pid` (`process_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='内部管理系统发布';

CREATE TABLE `tb_process_maintenance_assistance` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `process_instance_id` varchar(255) NOT NULL COMMENT '审批实例业务编号',
  `title` varchar(255) DEFAULT NULL COMMENT '标题',
  `work_type` varchar(255) NOT NULL COMMENT '工作类型',
  `demand_description` text COMMENT '需求说明',
  `planned_start_time` datetime DEFAULT NULL COMMENT '期望开始时间',
  `planned_end_time` datetime DEFAULT NULL COMMENT '期望结束时间',
  `result` varchar(255) NOT NULL COMMENT '审批结果',
  `status` varchar(50) NOT NULL COMMENT '审批状态',
  `current_node` varchar(255) NOT NULL COMMENT '当前节点',
  `current_responsible_person` varchar(255) NOT NULL COMMENT '当前负责人',
  `last_action_time` datetime NOT NULL COMMENT '更新时间',
  `originator_user` varchar(255) NOT NULL COMMENT '创建人',
  `originator_time` datetime NOT NULL COMMENT '创建时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据表修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_pid` (`process_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='运维技术协助申请';

CREATE TABLE `tb_process_maintenance_environment` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `process_instance_id` varchar(255) NOT NULL COMMENT '审批实例业务编号',
  `operation_category` varchar(255) NOT NULL COMMENT '操作分类',
  `operation_purpose` varchar(255) DEFAULT NULL COMMENT '操作目的',
  `operation_post` varchar(255) NOT NULL COMMENT '操作岗位',
  `risk_level` varchar(255) DEFAULT NULL COMMENT '操作风险等级',
  `planned_start_time` datetime NOT NULL COMMENT '计划开始时间',
  `planned_end_time` datetime NOT NULL COMMENT '计划结束时间',
  `result` varchar(255) NOT NULL COMMENT '审批结果',
  `status` varchar(50) NOT NULL COMMENT '审批状态',
  `current_node` varchar(255) NOT NULL COMMENT '当前节点',
  `current_responsible_person` varchar(255) NOT NULL COMMENT '当前负责人',
  `last_action_time` datetime NOT NULL COMMENT '更新时间',
  `originator_user` varchar(255) NOT NULL COMMENT '创建人',
  `originator_time` datetime NOT NULL COMMENT '创建时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据表修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_pid` (`process_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='运维&DBA&BI生产环境维护操作申请';

CREATE TABLE `tb_process_mobile_release` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `process_instance_id` varchar(255) NOT NULL COMMENT '审批实例业务编号',
  `title` varchar(255) DEFAULT NULL COMMENT '标题',
  `mobile_app_name` varchar(255) DEFAULT NULL COMMENT '移动端名称',
  `mobile_app_type` varchar(255) NOT NULL COMMENT '移动端类型',
  `result` varchar(255) NOT NULL COMMENT '审批结果',
  `status` varchar(50) NOT NULL COMMENT '审批状态',
  `current_node` varchar(255) NOT NULL COMMENT '当前节点',
  `current_responsible_person` varchar(255) NOT NULL COMMENT '当前负责人',
  `last_action_time` datetime NOT NULL COMMENT '更新时间',
  `originator_user` varchar(255) NOT NULL COMMENT '创建人',
  `originator_time` datetime NOT NULL COMMENT '创建时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据表修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_pid` (`process_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='移动端应用发布';

CREATE TABLE `tb_process_operations_dashboard` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `process_instance_id` varchar(255) NOT NULL COMMENT '审批实例业务编号',
  `business_category` varchar(255) NOT NULL COMMENT '业务大类',
  `board_type` varchar(255) NOT NULL COMMENT '看板类型',
  `requirement_title` varchar(255) DEFAULT NULL COMMENT '需求标题',
  `expected_completion_date` datetime NOT NULL COMMENT '期望完成日期',
  `priority_level` varchar(50) NOT NULL COMMENT '需求优先度',
  `result` varchar(255) NOT NULL COMMENT '审批结果',
  `status` varchar(50) NOT NULL COMMENT '审批状态',
  `current_responsible_person` varchar(255) NOT NULL COMMENT '当前负责人',
  `current_node` varchar(255) NOT NULL COMMENT '当前节点',
  `last_action_time` datetime NOT NULL COMMENT '更新时间',
  `originator_user` varchar(255) NOT NULL COMMENT '创建人',
  `originator_time` datetime NOT NULL COMMENT '创建时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据表修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_pid` (`process_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='运营看板及报表需求';

CREATE TABLE `tb_process_risk_control_rules` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `process_instance_id` varchar(255) NOT NULL COMMENT '审批实例业务编号',
  `business_category` varchar(255) NOT NULL COMMENT '业务大类',
  `requirement_title` varchar(255) DEFAULT NULL COMMENT '需求标题',
  `expected_completion_date` datetime NOT NULL COMMENT '期望完成日期',
  `priority_level` varchar(50) NOT NULL COMMENT '需求优先度',
  `result` varchar(255) NOT NULL COMMENT '审批结果',
  `status` varchar(50) NOT NULL COMMENT '审批状态',
  `current_responsible_person` varchar(255) NOT NULL COMMENT '当前负责人',
  `current_node` varchar(255) NOT NULL COMMENT '当前节点',
  `last_action_time` datetime NOT NULL COMMENT '更新时间',
  `originator_user` varchar(255) NOT NULL COMMENT '创建人',
  `originator_time` datetime NOT NULL COMMENT '创建时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据表修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_pid` (`process_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='风控规则开发需求';

CREATE TABLE `tb_process_sql_execution` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `process_instance_id` varchar(255) NOT NULL COMMENT '审批实例业务编号',
  `title` varchar(255) DEFAULT NULL COMMENT '标题',
  `operation_type` varchar(255) DEFAULT NULL COMMENT '操作类型',
  `result` varchar(255) NOT NULL COMMENT '审批结果',
  `status` varchar(50) NOT NULL COMMENT '审批状态',
  `current_responsible_person` varchar(255) NOT NULL COMMENT '当前负责人',
  `current_node` varchar(255) NOT NULL COMMENT '当前节点',
  `last_action_time` datetime NOT NULL COMMENT '更新时间',
  `originator_user` varchar(255) NOT NULL COMMENT '创建人',
  `originator_time` datetime NOT NULL COMMENT '创建时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据表修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_pid` (`process_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='生产维护SQL执行';

CREATE TABLE `tb_process_system_alarm` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `process_instance_id` varchar(255) NOT NULL COMMENT '审批实例业务编号',
  `fault_title` varchar(255) DEFAULT NULL COMMENT '故障标题',
  `fault_product_name` varchar(255) DEFAULT NULL COMMENT '故障产品名称',
  `fault_type` varchar(255) DEFAULT NULL COMMENT '故障类型',
  `fault_occurrence_time` datetime NOT NULL COMMENT '故障发生时间',
  `potential_impacts` varchar(255) DEFAULT NULL COMMENT '可能引发后果',
  `result` varchar(255) NOT NULL COMMENT '审批结果',
  `status` varchar(50) NOT NULL COMMENT '审批状态',
  `current_node` varchar(255) NOT NULL COMMENT '当前节点',
  `current_responsible_person` varchar(255) NOT NULL COMMENT '当前负责人',
  `last_action_time` datetime NOT NULL COMMENT '更新时间',
  `originator_user` varchar(255) NOT NULL COMMENT '创建人',
  `originator_time` datetime NOT NULL COMMENT '创建时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据表修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_pid` (`process_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统功能异常报障处理';

CREATE TABLE `tb_process_user_data_encryption` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `process_instance_id` varchar(255) NOT NULL COMMENT '审批实例业务编号',
  `task_description` text COMMENT '事项说明',
  `encryption_type` varchar(255) DEFAULT NULL COMMENT '数据处理类型',
  `result` varchar(255) NOT NULL COMMENT '审批结果',
  `status` varchar(50) NOT NULL COMMENT '审批状态',
  `current_node` varchar(255) NOT NULL COMMENT '当前节点',
  `current_responsible_person` varchar(255) NOT NULL COMMENT '当前负责人',
  `last_action_time` datetime NOT NULL COMMENT '更新时间',
  `originator_user` varchar(255) NOT NULL COMMENT '创建人',
  `originator_time` datetime NOT NULL COMMENT '创建时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据表修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uidx_pid` (`process_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='敏感用户数据加解密';
