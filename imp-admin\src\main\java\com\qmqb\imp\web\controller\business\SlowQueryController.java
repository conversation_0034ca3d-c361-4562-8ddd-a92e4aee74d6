package com.qmqb.imp.web.controller.business;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import com.baomidou.lock.executor.RedissonLockExecutor;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.RedisKey;
import com.qmqb.imp.system.domain.bo.*;
import com.qmqb.imp.system.domain.vo.SlowMonthStatsVo;
import com.qmqb.imp.system.domain.vo.SlowQueryDbListVO;
import com.qmqb.imp.system.domain.vo.SlowQueryDetailVO;
import com.qmqb.imp.system.domain.vo.SlowSqlStatVO;
import com.qmqb.imp.system.service.ISlowQueryDbListService;
import com.qmqb.imp.system.service.ISlowQueryStatsService;
import com.qmqb.imp.system.service.SiliconFlowAiService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 慢sql控制器
 *
 * <AUTHOR>
 * @date 2025/5/8 17:46
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/slowQuery")
public class SlowQueryController {

    private final ISlowQueryDbListService iSlowQueryDbListService;
    private final SiliconFlowAiService siliconFlowAiService;
    private final LockTemplate lockTemplate;

    private final ISlowQueryStatsService slowQueryStatsService;

    /**
     * 获取系统列表
     *
     * @return
     */
    @GetMapping("/system/list")
    public R<List<SlowQueryDbListVO>> list() {
        return R.ok(iSlowQueryDbListService.list());
    }


    /**
     * 分页获取慢sql列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @GetMapping("/sql/page")
    public R<TableDataInfo<SlowMonthStatsVo>> sqlPage(SlowMonthStatsBo bo, PageQuery pageQuery) {
        return R.ok(iSlowQueryDbListService.slowSqlPage(bo, pageQuery));
    }

    /**
     * 分页获取慢sql详细列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    @GetMapping("/sql/detail/page")
    public R<TableDataInfo<SlowQueryDetailVO>> sqlDetailPage(SlowQueryDetailBO bo, PageQuery pageQuery) {
        return R.ok(iSlowQueryDbListService.slowSqlDetailPage(bo, pageQuery));
    }

    /**
     * AI分析SQL语句
     *
     * @param sqlHash SQL语句hash值
     * @return 分析结果
     */
    @PostMapping("/sql/analyze")
    public R<String> analyzeSql(@RequestBody String sqlHash) {
        final LockInfo lockInfo = lockTemplate.lock(RedisKey.ANALYZE_SQL.getRedisKey(sqlHash), 30000L, 3000L, RedissonLockExecutor.class);
        if (null == lockInfo) {
            return R.fail("AI分析SQL处理中,请勿重复操作,请稍后再试");
        }
        try {
            String result = siliconFlowAiService.analyzeSql(sqlHash);
            return R.ok("AI分析成功", result);
        } catch (Exception e) {
            return R.fail("AI分析失败，请稍后重试");
        } finally {
            //释放锁
            lockTemplate.releaseLock(lockInfo);
        }
    }

    /**
     * 慢sql分析结果
     *
     * @param sqlHash
     * @return
     */
    @GetMapping("/sql/analyze/result")
    public R<String> getAnalyzeResult(String sqlHash) {
        return R.ok("操作成功", siliconFlowAiService.getAnalyzeResult(sqlHash));
    }

    /**
     * 指派慢sql
     *
     * @param bo
     * @return
     */
    @SaCheckPermission("system:slowQuery:assign")
    @PostMapping("/sql/assign")
    public R<String> assign(@RequestBody SlowQueryAssignBo bo) {
        iSlowQueryDbListService.assign(bo);
        return R.ok();
    }

    /**
     * 处理慢sql
     *
     * @param bo
     * @return
     */
    @PostMapping("/sql/process")
    public R<String> process(@RequestBody SlowQueryProcessBo bo) {
        iSlowQueryDbListService.process(bo);
        return R.ok();
    }

    /**
     * 获取各月慢sql数量统计
     *
     * @param year
     * @return
     */
    @SaCheckPermission("system:slowQuery:statistic")
    @GetMapping("/sql/stat")
    public R<List<SlowSqlStatVO>> slowSqlStat(Integer year,Integer status) {
        return R.ok(slowQueryStatsService.slowSqlStat(year,status));
    }

    /**
     * 查询处理人
     *
     * @param bo
     * @return
     */
    @PostMapping("/processer/list")
    public R<List<SysUser>> processerList(@RequestBody ProcesserQueryParamsBO bo) {
        return R.ok(iSlowQueryDbListService.processerList(bo));
    }


}
