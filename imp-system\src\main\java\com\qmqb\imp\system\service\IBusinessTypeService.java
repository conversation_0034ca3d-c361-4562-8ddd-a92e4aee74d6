package com.qmqb.imp.system.service;

import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.bo.BusinessTypeBo;
import com.qmqb.imp.system.domain.vo.BusinessTypeVo;

import java.util.Collection;
import java.util.List;

/**
 * 业务类型Service接口
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
public interface IBusinessTypeService {

    /**
     * 查询业务类型
     * @param id 业务类型ID
     * @return 业务类型信
     */
    BusinessTypeVo queryById(Long id);

    /**
     * 查询业务类型列表
     * @param bo 业务类型查询条件
     * @param pageQuery 分页查询条件
     * @return 业务类型列表
     */
    TableDataInfo<BusinessTypeVo> queryPageList(BusinessTypeBo bo, PageQuery pageQuery);

    /**
     * 查询业务类型列表
     * @param bo 业务类型查询条件
     * @return 业务类型列表
     */
    List<BusinessTypeVo> queryList(BusinessTypeBo bo);

    /**
     * 新增业务类型
     * @param bo 业务类型新增条件
     * @return 是否新增成功
     */
    Boolean insertByBo(BusinessTypeBo bo);

    /**
     * 修改业务类型
     * @param bo 业务类型修改条件
     * @return 是否修改成功
     */
    Boolean updateByBo(BusinessTypeBo bo);

    /**
     * 校验并批量删除业务类型信息
     * @param ids 业务类型ID集合
     * @param isValid 是否有效
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

}
