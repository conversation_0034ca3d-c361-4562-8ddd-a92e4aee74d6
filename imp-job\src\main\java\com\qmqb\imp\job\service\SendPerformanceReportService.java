package com.qmqb.imp.job.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.UserConstants;
import com.qmqb.imp.common.core.domain.entity.SysDept;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.common.enums.PerformanceReportTypeEnum;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.domain.PerformanceReport;
import com.qmqb.imp.system.domain.bo.message.BaseMsgBo;
import com.qmqb.imp.system.domain.bo.message.EmailMsgBo;
import com.qmqb.imp.system.service.IPerformanceReportService;
import com.qmqb.imp.system.service.ISysDeptService;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.message.IMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-06-25 15:11
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class SendPerformanceReportService {

    private final IPerformanceReportService iPerformanceReportService;

    private final ISysUserService sysUserService;

    private final IMessageService messageService;

    private final ISysDeptService sysDeptService;

    @TraceId("发送AI绩效分析报告邮件")
    @XxlJob("sendPerformanceReportJobHandler")
    public ReturnT<String> sendPerformanceReportJobHandler(String param) {
        try {
            XxlJobLogger.log("开始发送AI绩效分析报告邮件...");
            log.info("开始发送AI绩效分析报告邮件");

            LocalDate last = LocalDate.now().minusMonths(1);
            if (StringUtils.isNotBlank(param)) {
                last = LocalDate.parse(param, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
            String year = String.valueOf(last.getYear());
            String month = String.valueOf(last.getMonthValue());

            Map<String, SysUser> userMap = sysUserService.selectAllUser().stream().collect(Collectors.toMap(SysUser::getNickName, a -> a));

            List<PerformanceReport> reportList = iPerformanceReportService.getListByDate(year,month);
            Map<String, List<PerformanceReport>> reportTypeMap = reportList.stream().collect(Collectors.groupingBy(PerformanceReport::getReportType));
            reportTypeMap.forEach((key,value)->{
                if (key.equals(PerformanceReportTypeEnum.PERSON.getCode())) {
                    //发送个人绩效分析报告邮件
                    sendPersonalReport(value, userMap);
                } else if (key.equals(PerformanceReportTypeEnum.GROUP.getCode())) {
                    //发送团队绩效分析报告邮件给组长
                    sendGroupReport(value, userMap);
                } else if (key.equals(PerformanceReportTypeEnum.POST.getCode())) {
                    //发送岗位绩效分析报告邮件
                    sendPostReport(value);
                }
            });
            return ReturnT.SUCCESS;
        }catch (Exception e){
            XxlJobLogger.log("发送AI绩效分析报告邮件异常",e);
            log.error("发送AI绩效分析报告邮件异常",e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 发送个人绩效分析报告邮件
     * @param personalReports 个人报告列表
     * @param userMap 用户映射
     */
    private void sendPersonalReport(List<PerformanceReport> personalReports, Map<String, SysUser> userMap) {
        for (PerformanceReport performanceReport : personalReports) {
            if (StrUtil.isBlank(performanceReport.getUserName())) {
                log.warn("个人报告用户名为空，跳过发送: {}", performanceReport);
                continue;
            }
            SysUser user = userMap.get(performanceReport.getUserName());
            if (user == null || StrUtil.isBlank(user.getEmail())) {
                log.warn("用户 {} 未找到或邮箱为空，跳过发送个人报告", performanceReport.getUserName());
                continue;
            }
            StringBuilder htmlContent = new StringBuilder();
            htmlContent.append("<html><body>");
            htmlContent.append("<h3>小组绩效分析报告汇总</h3>");
            htmlContent.append("<img src=\"")
                .append(performanceReport.getReportUrl())
                .append("\" style=\"max-width:100%;\"><br>");
            htmlContent.append("</body></html>");
            BaseMsgBo baseMsgBo = EmailMsgBo.builder().from("技术中心综合管理平台").text(htmlContent.toString())
                .subject("个人AI绩效分析报告-" + performanceReport.getUserName())
                .to(Collections.singletonList(user.getEmail())).type("html").build();
            baseMsgBo.setChannelType(MessageChannelTypeEnum.EMAIL.getType());
            messageService.sendBase(baseMsgBo);
        }
    }

    /**
     * 发送团队报告给组长
     * @param groupReports 团队报告列表
     * @param userMap 用户映射
     */
    private void sendGroupReport(List<PerformanceReport> groupReports, Map<String, SysUser> userMap) {
        // 获取所有部门信息
        Map<String, SysDept> deptMap = sysDeptService.selectAllDeptList(new SysDept()).stream().collect(Collectors.toMap(SysDept::getDeptName, a -> a));
        for (PerformanceReport groupReport : groupReports) {
            String deptName = groupReport.getGroupName();
            if (StrUtil.isBlank(deptName)) {
                log.warn("团队报告部门名称为空，跳过发送: {}", groupReport);
                continue;
            }
            // 根据部门名称查找对应的部门信息
            SysDept sysDept = deptMap.get(deptName);
            if (ObjectUtils.isNotNull(sysDept) && StrUtil.isNotBlank(sysDept.getLeader())) {
                String leader = sysDept.getLeader();
                // 处理多个组长的情况
                String[] leaders = leader.split(",");
                List<String> leaderEmails = new ArrayList<>();
                for (String leaderName : leaders) {
                    SysUser leaderUser = userMap.get(leaderName.trim());
                    if (leaderUser != null && StrUtil.isNotBlank(leaderUser.getEmail())) {
                        leaderEmails.add(leaderUser.getEmail());
                    }
                }
                StringBuilder htmlContent = new StringBuilder();
                htmlContent.append("<html><body>");
                htmlContent.append("<h3>小组绩效分析报告汇总</h3>");
                htmlContent.append("<img src=\"")
                    .append(groupReport.getReportUrl())
                    .append("\" style=\"max-width:100%;\"><br>");
                htmlContent.append("</body></html>");

                if (!leaderEmails.isEmpty()) {
                    BaseMsgBo baseMsgBo = EmailMsgBo.builder().from("技术中心综合管理平台").text(htmlContent.toString())
                        .subject("团队AI绩效分析报告 - " + deptName).to(leaderEmails).type("html").build();
                    baseMsgBo.setChannelType(MessageChannelTypeEnum.EMAIL.getType());
                    messageService.sendBase(baseMsgBo);
                    log.info("团队报告已发送给组长: {}, 部门: {}, 收件人: {}", leader, deptName, leaderEmails);
                }
            }
        }
    }

    /**
     * 发送岗位绩效分析报告邮件
     * @param postReports 岗位报告列表
     */
    private void sendPostReport(List<PerformanceReport> postReports) {
        String ctoEmail = sysUserService.selectUserById(UserConstants.JSZX_ADMIN_ID).getEmail();

        if (postReports.isEmpty()) {
            log.info("没有岗位报告需要发送");
            return;
        }
        // 1. 构建包含所有图片的HTML内容
        StringBuilder htmlContent = new StringBuilder();
        htmlContent.append("<html><body>");
        htmlContent.append("<h3>岗位AI绩效分析报告汇总</h3>");
        // 2. 遍历所有岗位报告，将每个图片添加到HTML中
        for (PerformanceReport postReport : postReports) {
            String postName = postReport.getPostName();
            if (StrUtil.isBlank(postName)) {
                log.warn("岗位报告岗位名称为空，跳过: {}", postReport);
                continue;
            }
            // 添加岗位名称作为标题
            htmlContent.append("<h4>").append(postName).append("</h4>");
            // 添加图片（使用URL链接）
            htmlContent.append("<img src=\"")
                .append(postReport.getReportUrl())
                .append("\" style=\"max-width:100%;\"><br>");
        }
        htmlContent.append("</body></html>");

        // 3. 发送包含所有图片的单一邮件
        BaseMsgBo baseMsgBo = EmailMsgBo.builder()
            .from("技术中心综合管理平台")
            .text(htmlContent.toString())
            .subject("岗位AI绩效分析报告汇总")
            .to(Collections.singletonList(ctoEmail))
            .type("html")
            .build();
        baseMsgBo.setChannelType(MessageChannelTypeEnum.EMAIL.getType());
        messageService.sendBase(baseMsgBo);
    }




}
