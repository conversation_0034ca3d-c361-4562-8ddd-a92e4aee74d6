package com.qmqb.imp.system.service.impl.performance;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceIndicatorBo;
import com.qmqb.imp.system.domain.performance.PerformanceIndicator;
import com.qmqb.imp.system.domain.vo.performance.PerformanceIndicatorVo;
import com.qmqb.imp.system.mapper.performance.PerformanceIndicatorMapper;
import com.qmqb.imp.system.service.indicator.IPerformanceIndicatorService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 绩效指标Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@RequiredArgsConstructor
@Service
public class PerformanceIndicatorServiceImpl extends ServiceImpl<PerformanceIndicatorMapper, PerformanceIndicator> implements IPerformanceIndicatorService {

    private final PerformanceIndicatorMapper baseMapper;

    /**
     * 查询绩效指标
     */
    @Override
    public PerformanceIndicatorVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询绩效指标列表
     */
    @Override
    public TableDataInfo<PerformanceIndicatorVo> queryPageList(PerformanceIndicatorBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PerformanceIndicator> lqw = buildQueryWrapper(bo);
        Page<PerformanceIndicatorVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询绩效指标列表
     */
    @Override
    public List<PerformanceIndicatorVo> queryList(PerformanceIndicatorBo bo) {
        LambdaQueryWrapper<PerformanceIndicator> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PerformanceIndicator> buildQueryWrapper(PerformanceIndicatorBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PerformanceIndicator> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getPerformanceId() != null, PerformanceIndicator::getPerformanceId, bo.getPerformanceId());
        lqw.eq(StringUtils.isNotBlank(bo.getIndicatorCode()), PerformanceIndicator::getIndicatorCode, bo.getIndicatorCode());
        lqw.like(StringUtils.isNotBlank(bo.getIndicatorName()), PerformanceIndicator::getIndicatorName, bo.getIndicatorName());
        lqw.eq(StringUtils.isNotBlank(bo.getScoreLevel()), PerformanceIndicator::getScoreLevel, bo.getScoreLevel());
        lqw.eq(StringUtils.isNotBlank(bo.getLogContent()), PerformanceIndicator::getLogContent, bo.getLogContent());
        return lqw;
    }

    /**
     * 新增绩效指标
     */
    @Override
    public Boolean insertByBo(PerformanceIndicatorBo bo) {
        PerformanceIndicator add = BeanUtil.toBean(bo, PerformanceIndicator.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改绩效指标
     */
    @Override
    public Boolean updateByBo(PerformanceIndicatorBo bo) {
        PerformanceIndicator update = BeanUtil.toBean(bo, PerformanceIndicator.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PerformanceIndicator entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除绩效指标
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<PerformanceIndicator> getByPerformanceId(Long performanceId) {
        return Collections.emptyList();
    }
}
