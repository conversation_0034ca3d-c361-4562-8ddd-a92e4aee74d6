package com.qmqb.imp.system.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 分支详细信息对象 tb_branch
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
@Data
public class SlowQueryDbListBO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Schema(description = "id")
    private Long id;
    /**
     * dbCode
     */
    @Schema(description = "dbCode")
    private String dbCode;
    /**
     * dbName
     */
    @Schema(description = "dbName")
    private String dbName;
    /**
     * 慢SQL表名称
     */
    @Schema(description = "慢SQL表名称")
    private String tabName;
    /**
     * TOP SQL表名称
     */
    @Schema(description = "TOP SQL表名称")
    private String topName;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

}
