package com.qmqb.imp.system.domain.vo.performance;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-07-01 14:43
 */
@Data
@NoArgsConstructor
public class IndicatorCategoryVo {

    private String code;
    private String name;
    private List<IndicatorVO> secondaryIndicators;

    @Data
    public static class IndicatorVO {
        private String code;
        private String name;
        /**
         * 是否系统生成
         */
        private Boolean systemGenerated;
    }

}
