package com.qmqb.imp.app.controller;

import java.util.List;

import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.system.domain.vo.MonitorEventRecordVo;
import com.qmqb.imp.system.domain.bo.MonitorEventRecordBo;
import com.qmqb.imp.system.service.IMonitorEventRecordService;

/**
 * 监控预警记录
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/monitorEventRecord")
public class MonitorEventRecordController extends BaseController {

    private final IMonitorEventRecordService iMonitorEventRecordService;

    /**
     * 查询监控预警记录列表
     */
    @GetMapping("/list")
    public R<TableDataInfo<MonitorEventRecordVo>> list(MonitorEventRecordBo bo, PageQuery pageQuery) {
        return R.ok(iMonitorEventRecordService.queryPageList(bo, pageQuery));
    }


    /**
     * 处理监控预警记录,受理、去处理
     */
    @Log(title = "监控预警记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/handle")
    public R<Void> handle(@RequestBody MonitorEventRecordBo bo) {
        return toAjax(iMonitorEventRecordService.handle(bo));
    }
}
