package com.qmqb.imp.system.domain;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10 14:29:51
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_slow_query_stats")
@Schema(name = "SlowQueryStatsDO对象", description = "")
public class SlowQueryStats implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "`id`")
    private Long id;

    @Schema(description = "数据库名称")
    @TableField("`DBName`")
    private String dbName;

    @Schema(description = "统计月份")
    @TableField("`month`")
    private String month;

    @Schema(description = "同类SQL的HASH值")
    @TableField("`SQLHash`")
    private String sqlHash;

    @Schema(description = "SQL文本")
    @TableField("`SQLText`")
    private String sqlText;

    @Schema(description = "总查询次数（本月）")
    @TableField("`total_query_times`")
    private Long totalQueryTimes;

    @Schema(description = "总查询毫秒时间（本月）")
    @TableField("`total_sum_time`")
    private Long totalSumTime;

    @Schema(description = "平均查询毫秒时间（本月）")
    @TableField("`avg_query_time`")
    private Long avgQueryTime;

    @Schema(description = "平均返回记录数（本月）")
    @TableField("`avg_returnRowCounts`")
    private Long avgReturnrowcounts;

    @Schema(description = "平均解析涉及的记录条数（本月）")
    @TableField("`avg_parseRowCounts`")
    private Long avgParserowcounts;

    @Schema(description = "慢查询入围规则（returnNull：返回空值、returnRowCounts：返回记录数过多、parseRowCounts：解析记录数过多、query_time：查询时间过长、query_hot：慢查询且频繁）")
    @TableField("`slow_rule`")
    private String slowRule;

    @Schema(description = "告警级别（P0、P1、P2）")
    @TableField("`warn_level`")
    private String warnLevel;

    @Schema(description = "状态：0未指派、1已指派未处理、2已指派已处理")
    @TableField("`status`")
    private String status;

    @Schema(description = "客户端IP")
    @TableField("IP")
    private String ip;

    @Schema(description = "客户端用户名")
    @TableField("User")
    private String user;

    @Schema(description = "最近执行开始时间")
    @TableField("last_executionStartTime")
    private LocalDateTime lastExecutionStartTime;

}
