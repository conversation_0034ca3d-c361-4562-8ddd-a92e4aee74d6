package com.qmqb.imp.system.domain.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qmqb.imp.common.core.domain.dto.BasePageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
;

/**
 * 项目管理业务对象 tb_project
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectBo extends BasePageDTO {

    /**
     * 项目ID
     */
    @JsonProperty("pId")
    private Long pId;
    /**
     * 项目名称
     */
    private String pName;
    /**
     * 项目路径
     */
    private String pNamespace;

    /**
     * 项目描述
     */
    private String pDesc;

    /**
     * 负责开发组
     */
    @JsonProperty("pDevDept")
    private String pDevDept;
    /**
     * 负责测试组
     */
    @JsonProperty("pTestDept")
    private String pTestDept;
    /**
     * 大类业务
     */
    @JsonProperty("pBroadBusiness")
    private Long pBroadBusiness;
    /**
     * 小类业务
     */
    @JsonProperty("pNarrowBusiness")
    private Long pNarrowBusiness;

    /**
     * 分支数
     */
    private Long pBranchCount;

    /**
     * 是否有master分支(0无，1有)
     */
    private Integer pHasMaster;

    /**
     * master分支是否受保护(0无，1有)
     */
    private Integer pMasterProtected;
}
