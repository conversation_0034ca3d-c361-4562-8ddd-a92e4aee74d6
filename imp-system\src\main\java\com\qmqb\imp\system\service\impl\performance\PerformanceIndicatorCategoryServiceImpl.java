package com.qmqb.imp.system.service.impl.performance;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.common.utils.ScoreLevelUtil;
import com.qmqb.imp.system.domain.bo.performance.PerformanceIndicatorCategoryBo;
import com.qmqb.imp.system.domain.performance.PerformanceIndicatorCategory;
import com.qmqb.imp.system.domain.performance.PerformanceIndicator;
import com.qmqb.imp.system.domain.vo.performance.PerformanceIndicatorCategoryVo;
import com.qmqb.imp.system.mapper.performance.PerformanceIndicatorCategoryMapper;
import com.qmqb.imp.system.service.indicator.IPerformanceIndicatorCategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 绩效指标分类Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@RequiredArgsConstructor
@Service
public class PerformanceIndicatorCategoryServiceImpl extends ServiceImpl<PerformanceIndicatorCategoryMapper, PerformanceIndicatorCategory> implements IPerformanceIndicatorCategoryService {

    private final PerformanceIndicatorCategoryMapper baseMapper;

    /**
     * 查询绩效指标分类
     */
    @Override
    public PerformanceIndicatorCategoryVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    @Override
    public List<PerformanceIndicatorCategoryVo> queryByPerformanceIds(List<Long> ids) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<PerformanceIndicatorCategory>().in(PerformanceIndicatorCategory::getPerformanceId, ids));
    }

    /**
     * 查询绩效指标分类列表
     */
    @Override
    public TableDataInfo<PerformanceIndicatorCategoryVo> queryPageList(PerformanceIndicatorCategoryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PerformanceIndicatorCategory> lqw = buildQueryWrapper(bo);
        Page<PerformanceIndicatorCategoryVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询绩效指标分类列表
     */
    @Override
    public List<PerformanceIndicatorCategoryVo> queryList(PerformanceIndicatorCategoryBo bo) {
        LambdaQueryWrapper<PerformanceIndicatorCategory> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PerformanceIndicatorCategory> buildQueryWrapper(PerformanceIndicatorCategoryBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PerformanceIndicatorCategory> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getPerformanceId() != null, PerformanceIndicatorCategory::getPerformanceId, bo.getPerformanceId());
        lqw.eq(StringUtils.isNotBlank(bo.getCategoryCode()), PerformanceIndicatorCategory::getCategoryCode, bo.getCategoryCode());
        lqw.like(StringUtils.isNotBlank(bo.getCategoryName()), PerformanceIndicatorCategory::getCategoryName, bo.getCategoryName());
        lqw.eq(StringUtils.isNotBlank(bo.getCategoryLevel()), PerformanceIndicatorCategory::getCategoryLevel, bo.getCategoryLevel());
        lqw.eq(bo.getCategoryWeight() != null, PerformanceIndicatorCategory::getCategoryWeight, bo.getCategoryWeight());
        return lqw;
    }

    /**
     * 新增绩效指标分类
     */
    @Override
    public Boolean insertByBo(PerformanceIndicatorCategoryBo bo) {
        PerformanceIndicatorCategory add = BeanUtil.toBean(bo, PerformanceIndicatorCategory.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改绩效指标分类
     */
    @Override
    public Boolean updateByBo(PerformanceIndicatorCategoryBo bo) {
        PerformanceIndicatorCategory update = BeanUtil.toBean(bo, PerformanceIndicatorCategory.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PerformanceIndicatorCategory entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除绩效指标分类
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 计算分类等级
     * 规则：
     * 1. 将各指标等级转换为A级数量：
     * - S级 = 2个A
     * - A级 = 1个A
     * - B级 = 0个A
     * - C级 = -0.5个A（抵消2个A）
     * - D级 = -1个A
     * 2. 根据最终A级数量确定分类等级：
     * - 4个A及以上：S级
     * - 2-3个A：A级
     * - 0-1个A或C<1：B级
     * - C>1：C级
     */
    @Override
    public PerformanceIndicatorCategory calculateCategoryLevel(List<PerformanceIndicator> indicators, String categoryCode, String categoryName, Integer categoryWeight) {
        if (indicators == null || indicators.isEmpty()) {
            return createDefaultCategory(categoryCode, categoryName, categoryWeight);
        }

        // 计算A级数量
        double totalA = 0.0;
        int cCount = 0;
        StringBuilder logBuilder = new StringBuilder();
        logBuilder.append("分类[").append(categoryName).append("]计算过程：\n");

        for (PerformanceIndicator indicator : indicators) {
            double aValue = ScoreLevelUtil.convertLevelToAvalue(indicator.getScoreLevel());
            totalA += aValue;

            if (ScoreLevelEnum.SCORE_C.getCode().equals(indicator.getScoreLevel())) {
                cCount++;
            }

            logBuilder.append("- ").append(indicator.getIndicatorName())
                    .append("：").append(indicator.getScoreLevel())
                    .append("(").append(String.format("%.1f", aValue)).append("A)\n");
        }

        // 获取全部levels
        List<String> levels = indicators.stream()
                .map(PerformanceIndicator::getScoreLevel)
                .distinct().collect(Collectors.toList());

        // 确定分类等级
        String categoryLevel = ScoreLevelUtil.determineCategoryLevel(totalA, cCount,levels);

        logBuilder.append("总A级数量：").append(String.format("%.1f", totalA))
                .append("，C级数量：").append(cCount)
                .append("，分类等级：").append(categoryLevel);

        PerformanceIndicatorCategory category = new PerformanceIndicatorCategory();
        category.setCategoryCode(categoryCode);
        category.setCategoryName(categoryName);
        category.setCategoryLevel(categoryLevel);
        category.setCategoryWeight(categoryWeight != null ? categoryWeight : 0);
        category.setCategoryScore(null);
        category.setCategoryLog(logBuilder.toString());

        return category;
    }

    /**
     * 根据绩效ID查询所有分类
     */
    @Override
    public List<PerformanceIndicatorCategory> getByPerformanceId(Long performanceId) {
        return this.list(new LambdaQueryWrapper<PerformanceIndicatorCategory>()
                .eq(PerformanceIndicatorCategory::getPerformanceId, performanceId));
    }

    /**
     * 创建默认分类（当没有指标时）
     */
    private PerformanceIndicatorCategory createDefaultCategory(String categoryCode, String categoryName, Integer categoryWeight) {
        PerformanceIndicatorCategory category = new PerformanceIndicatorCategory();
        category.setCategoryCode(categoryCode);
        category.setCategoryName(categoryName);
        category.setCategoryLevel(ScoreLevelEnum.SCORE_B.getCode());
        category.setCategoryWeight(categoryWeight != null ? categoryWeight : 0);
        category.setCategoryScore(65.0);
        category.setCategoryLog("分类[" + categoryName + "]：无相关指标数据，默认评为B级");
        return category;
    }
}
