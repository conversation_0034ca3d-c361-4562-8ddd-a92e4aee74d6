package com.qmqb.imp.system.domain.bo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 慢sql信息视图对象 tb_slow_query_info
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Data
public class SlowQueryProcessInfoBo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    private Long id;

    /**
     * 同类SQL的HASH值（唯一标识）
     */
    private String sqlHash;


    /**
     * 处理状态：NO=未处理，YES=已处理
     */
    private String processStatus;
    /**
     * 处理时间
     */
    private LocalDateTime processTime;
    /**
     * 处理人
     */
    private String processBy;
    /**
     * 处理结果
     */
    private String processResult;

    /**
     * AI分析结果（存储分析结论/优化建议）
     */
    private String analysisResult;


}
