package com.qmqb.imp.web.controller.business.process;

import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.system.domain.vo.process.ProcessMaintenanceAssistanceVo;
import com.qmqb.imp.system.domain.bo.process.ProcessMaintenanceAssistanceBo;
import com.qmqb.imp.system.service.process.IProcessMaintenanceAssistanceService;
import com.qmqb.imp.common.core.page.TableDataInfo;

/**
 * 运维技术协助申请
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/maintenanceAssistance")
public class ProcessMaintenanceAssistanceController extends BaseController {

    private final IProcessMaintenanceAssistanceService iProcessMaintenanceAssistanceService;

    /**
     * 查询运维技术协助申请列表
     */
    @SaCheckPermission("system:processMaintenanceAssistance:list")
    @GetMapping("/list")
    public TableDataInfo<ProcessMaintenanceAssistanceVo> list(ProcessMaintenanceAssistanceBo bo, PageQuery pageQuery) {
        return iProcessMaintenanceAssistanceService.queryPageList(bo, pageQuery);
    }


}
