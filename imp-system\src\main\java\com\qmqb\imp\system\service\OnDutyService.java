package com.qmqb.imp.system.service;

import com.qmqb.imp.system.domain.bo.OndutyUserImportVo;

import java.util.Collection;
import java.util.List;

/**
 * 值班业务
 *
 * <AUTHOR>
 */
public interface OnDutyService {

    /**
     * 导入
     *
     * @param volist
     */
    void importUser(List<OndutyUserImportVo> volist);

    /**
     * 删除元素
     *
     * @param ids
     * @param sync 是否同步
     * @return
     */
    boolean delete(Collection<Long> ids, Boolean sync);
}
