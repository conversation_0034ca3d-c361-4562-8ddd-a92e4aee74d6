package com.qmqb.imp.web.controller.business.process;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.system.domain.bo.process.ProcessOperationsDashboardBo;
import com.qmqb.imp.system.domain.vo.process.ProcessOperationsDashboardVo;
import com.qmqb.imp.system.service.process.IProcessOperationsDashboardService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 运营看板及报表需求
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/processOperationsDashboard")
public class ProcessOperationsDashboardController extends BaseController {

    private final IProcessOperationsDashboardService iProcessOperationsDashboardService;

    /**
     * 查询运营看板及报表需求列表
     */
    @SaCheckPermission("system:processOperationsDashboard:list")
    @GetMapping("/list")
    public TableDataInfo<ProcessOperationsDashboardVo> list(ProcessOperationsDashboardBo bo, PageQuery pageQuery) {
        return iProcessOperationsDashboardService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取运营看板及报表需求详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:processOperationsDashboard:query")
    @GetMapping("/{id}")
    public R<ProcessOperationsDashboardVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iProcessOperationsDashboardService.queryById(id));
    }

    /**
     * 新增运营看板及报表需求
     */
    @SaCheckPermission("system:processOperationsDashboard:add")
    @Log(title = "运营看板及报表需求", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProcessOperationsDashboardBo bo) {
        return toAjax(iProcessOperationsDashboardService.insertByBo(bo));
    }

    /**
     * 修改运营看板及报表需求
     */
    @SaCheckPermission("system:processOperationsDashboard:edit")
    @Log(title = "运营看板及报表需求", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProcessOperationsDashboardBo bo) {
        return toAjax(iProcessOperationsDashboardService.updateByBo(bo));
    }

    /**
     * 删除运营看板及报表需求
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:processOperationsDashboard:remove")
    @Log(title = "运营看板及报表需求", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iProcessOperationsDashboardService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
