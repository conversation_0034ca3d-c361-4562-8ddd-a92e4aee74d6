package com.qmqb.imp.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.qmqb.imp.common.annotation.ExcelDictFormat;
import com.qmqb.imp.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 绩效分析报告视图对象 tb_performance_report
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@ExcelIgnoreUnannotated
public class PerformanceAnalysisReportVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id")
    private Long id;

    /**
     * 年份
     */
    @ExcelProperty(value = "年份")
    private String year;

    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private String month;

    /**
     * 报告类型 （0-个人 1-小组 2-岗位）
     */
    @ExcelProperty(value = "报告类型 ", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=-个人,1=-小组,2=-岗位")
    private String reportType;

    /**
     * 个人名称
     */
    @ExcelProperty(value = "个人名称")
    private String userName;

    /**
     * 小组名称
     */
    @ExcelProperty(value = "小组名称")
    private String groupName;

    /**
     * 岗位名称
     */
    @ExcelProperty(value = "岗位名称")
    private String postName;

    /**
     * 报告地址
     */
    @ExcelProperty(value = "报告地址")
    private String reportUrl;


}
