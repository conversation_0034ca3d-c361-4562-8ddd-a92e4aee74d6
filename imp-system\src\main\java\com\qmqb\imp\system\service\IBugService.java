package com.qmqb.imp.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qmqb.imp.system.domain.Bug;
import com.qmqb.imp.system.domain.BugStatisticParamsBO;
import com.qmqb.imp.system.domain.bo.BugQueryParamsBO;
import com.qmqb.imp.system.domain.vo.*;
import com.qmqb.imp.system.domain.bo.BugBo;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.domain.PageQuery;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;

/**
 * Bug服务接口
 *
 * 提供Bug相关的查询、新增、修改、删除以及统计功能
 *
 * <AUTHOR>
 * @date 2023-03-04
 */
public interface IBugService {

    /**
     * 根据ID查询Bug详情
     *
     * @param id Bug的唯一标识
     * @return Bug详情视图对象
     */
    BugVo queryById(Long id);

    /**
     * 分页查询Bug列表
     *
     * @param bo Bug查询业务对象
     * @param pageQuery 分页查询参数
     * @return 分页数据结果
     */
    TableDataInfo<BugVo> queryPageList(BugBo bo, PageQuery pageQuery);

    /**
     * 查询Bug列表（不分页）
     *
     * @param bo Bug查询业务对象
     * @return Bug列表
     */
    List<BugVo> queryList(BugBo bo);

    /**
     * 新增Bug
     *
     * @param bo Bug业务对象
     * @return 操作是否成功
     */
    Boolean insertByBo(BugBo bo);

    /**
     * 修改Bug
     *
     * @param bo Bug业务对象
     * @return 操作是否成功
     */
    Boolean updateByBo(BugBo bo);

    /**
     * 校验并批量删除Bug信息
     *
     * @param ids 需要删除的Bug ID集合
     * @param isValid 是否进行有效性校验
     * @return 操作是否成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * Bug高级分页查询
     *
     * @param paramsBO Bug查询参数业务对象
     * @return 分页查询结果
     */
    IPage<BugPageVO> page(BugQueryParamsBO paramsBO);

    /**
     * 根据Bug类型进行统计
     *
     * @param paramsBO Bug统计参数业务对象
     * @return Bug类型统计结果
     */
    BugStatisticByTypeVO statisticByType(BugStatisticParamsBO paramsBO);

    /**
     * 获取Bug类型统计的详细项目列表
     *
     * @param paramsBO Bug统计参数业务对象
     * @return Bug类型统计项目列表
     */
    List<BugStatisticByTypeVO.TypeBugStatisticItem> statisticItemByType(BugStatisticParamsBO paramsBO);

    /**
     * 根据产品进行Bug统计
     *
     * @param paramsBO Bug统计参数业务对象
     * @return 产品Bug统计结果
     */
    BugStatisticByProductVO statisticByProduct(BugStatisticParamsBO paramsBO);

    /**
     * 根据项目进行Bug统计
     *
     * @param paramsBO Bug统计参数业务对象
     * @return 项目Bug统计结果
     */
    BugStatisticByProjectVO statisticByProject(BugStatisticParamsBO paramsBO);

    /**
     * 导出Bug类型统计数据
     *
     * @param paramsBO Bug统计参数业务对象
     * @param response HTTP响应对象，用于写入导出数据
     */
    void statisticByTypeExport(BugStatisticParamsBO paramsBO, HttpServletResponse response);

    /**
     * 导出产品Bug统计数据
     *
     * @param paramsBO Bug统计参数业务对象
     * @param response HTTP响应对象，用于写入导出数据
     */
    void statisticByProductExport(BugStatisticParamsBO paramsBO, HttpServletResponse response);

    /**
     * 导出项目Bug统计数据
     *
     * @param paramsBO Bug统计参数业务对象
     * @param response HTTP响应对象，用于写入导出数据
     */
    void statisticByProjectExport(BugStatisticParamsBO paramsBO, HttpServletResponse response);
}
