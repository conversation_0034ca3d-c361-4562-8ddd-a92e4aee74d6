package com.qmqb.imp.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qmqb.imp.system.domain.ZtUser;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.qmqb.imp.system.mapper.ZtUserMapper;
import com.qmqb.imp.system.service.IZtUserService;

import java.util.List;


/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@RequiredArgsConstructor
@Service
public class ZtUserServiceImpl implements IZtUserService {

    private final ZtUserMapper baseMapper;

    @Override
    public List<ZtUser> selectByAccounts(List<String> accounts) {
         return baseMapper.selectList(new LambdaQueryWrapper<ZtUser>()
            .in(ZtUser::getAccount, accounts));
    }
}
