create table tb_home_trend
(
    trend_time        date                   not null comment '趋势时间，主键'
        primary key,
    create_time       datetime               null comment '创建时间',
    update_by         varchar(64) default '' null comment '更新者',
    update_time       datetime               null comment '更新时间',
    developer_count   int         default 0  not null comment '开发人员人数',
    tester_count      int         default 0  not null comment '测试人员人数',
    pm_count          int         default 0  not null comment '项目管理人数',
    leader_count      int         default 0  not null comment '组长人数',
    task_wait         int         default 0  not null comment '等待任务数',
    task_doing        int         default 0  not null comment '进行中任务数',
    task_done         int         default 0  not null comment '完成任务数',
    task_pause        int         default 0  not null comment '暂停任务数',
    kq_after20        int         default 0  not null comment '20点后考勤人数',
    doc_count         int         default 0  not null comment '新增文档数',
    git_project_count int         default 0  not null comment '新增代码库数',
    create_by         varchar(64) default '' null comment '创建者'
)
    comment '首页数据趋势表';

alter table sys_dept
    add dept_type tinyint null comment '部门类型，1开发、2测试、3运维、4项目管理、5数据管理' after dept_code;
update sys_dept set dept_type = 1 where dept_name like '%开发%' or dept_name='BI组';
update sys_dept set dept_type = 2 where dept_name like '%测试%';
update sys_dept set dept_type = 3 where dept_name like '%运维%';
update sys_dept set dept_type = 4 where dept_name like '%项目管理%';
update sys_dept set dept_type = 5 where dept_name like '%数据管理%';

create table tb_business_config
(
    id          int auto_increment comment '主键id'
        primary key,
    config_type tinyint                               null comment '业务配置类型,1业务大类、2业务小类',
    config_name varchar(32)                           null comment '配置名称',
    del_flag    int         default 0                 not null comment '删除标志（0代表存在 2代表删除）',
    create_by   varchar(64) default ''                null comment '创建者',
    create_time datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    update_by   varchar(64) default ''                null comment '更新者',
    update_time datetime    default CURRENT_TIMESTAMP not null comment '更新时间'
)
    comment '业务配置';

begin;

insert tb_business_config (config_type, config_name)
values
    (1,'国内'),
    (1,'海外'),
    (2,'风控'),
    (2,'资金'),
    (2,'贷后'),
    (2,'资产'),
    (2,'运营'),
    (2,'财务'),
    (2,'中台'),
    (2,'电商'),
    (2,'其他');

alter table tb_project
    add p_dev_dept bigint null comment '负责开发组';

alter table tb_project
    add p_test_dept bigint null comment '负责测试组';

alter table tb_project
    add p_broad_business int null comment '大类业务';

alter table tb_project
    add p_narrow_business int null comment '小类业务';

alter table tb_project
    add p_branch_count int default 0 not null comment '分支数';

UPDATE sys_menu SET `menu_name` = '代码量统计' WHERE `menu_name` = '代码统计' and menu_id = 1600371350308102145;
UPDATE sys_menu SET `menu_name` = '代码库管理', `order_num` = 1 WHERE `menu_name` = '项目管理' and menu_id = 1600740277584273409;
UPDATE sys_menu SET `menu_name` = '提交明细查询', `order_num` = 2 WHERE `menu_name` = '提交明细查询' and menu_id = 1600372222232600578;
UPDATE sys_menu SET `menu_name` = '时间段统计综合', `order_num` = 3 WHERE `menu_name` = '时间段统计综合' and menu_id = 1602119135990513666;

UPDATE sys_menu SET `menu_name` = '请假统计',`order_num` = 1 WHERE `menu_name` = '请假登记' and menu_id = 1676859408619020288;
UPDATE sys_menu SET `menu_name` = '需求查询',`order_num` = 3 WHERE `menu_name` = '需求查询' and menu_id = 1600333306947411969;
UPDATE sys_menu SET `menu_name` = '任务统计', `order_num` = 4 WHERE `menu_name` = '任务异常统计' and menu_id = 1777244184116625409;
UPDATE sys_menu SET `menu_name` = '任务查询', `order_num` = 5 WHERE `menu_name` = '任务查询' and menu_id = 1600371001040019458;

INSERT INTO sys_menu (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`,
                      `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (1859439303951216642, '需求统计', 1600332606792245249, 2, 'demandStatistics', 'zentao/demandStatistics', NULL, 1, 0, 'C', '0', '0',
        NULL, 'chart', 'admin', '2024-11-21 11:30:51', 'admin', '2024-11-21 11:30:51', '');

alter table tb_business_config
    add pinyin_name varchar(32) null comment '拼音(方便中文名称排序)' after config_type;

update tb_business_config set pinyin_name = 'zt' where config_name='中台';
update tb_business_config set pinyin_name = 'zj' where config_name='资金';
update tb_business_config set pinyin_name = 'zc' where config_name='资产';
update tb_business_config set pinyin_name = 'yy' where config_name='运营';
update tb_business_config set pinyin_name = 'qt' where config_name='其他';
update tb_business_config set pinyin_name = 'hw' where config_name='海外';
update tb_business_config set pinyin_name = 'gn' where config_name='国内';
update tb_business_config set pinyin_name = 'fk' where config_name='风控';
update tb_business_config set pinyin_name = 'ds' where config_name='电商';
update tb_business_config set pinyin_name = 'dh' where config_name='贷后';
update tb_business_config set pinyin_name = 'cw' where config_name='财务';

commit;


CREATE TABLE `tb_task_stat`  (
      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
      `dept_id` bigint(20) NULL DEFAULT NULL COMMENT '组ID',
      `dept_name` varchar(30)  NULL DEFAULT NULL COMMENT '组名',
      `year` varchar(20)  NULL DEFAULT NULL COMMENT '年份',
      `month` varchar(20)  NULL DEFAULT NULL COMMENT '月份',
      `task_count` int(11) NULL DEFAULT NULL COMMENT '任务数',
      `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
      `create_by` varchar(30)  NULL DEFAULT NULL COMMENT '创建人',
      `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
      `update_by` varchar(30)  NULL DEFAULT NULL COMMENT '更新人',
      PRIMARY KEY (`id`) USING BTREE
)
comment '任务统计';
