package com.qmqb.imp.web.controller.business;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.bo.WarnTriggerRuleBo;
import com.qmqb.imp.system.domain.vo.WarnTriggerRuleVo;
import com.qmqb.imp.system.service.IWarnTriggerRuleService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 预警配置触发规则
 *
 * <AUTHOR>
 * @date 2023-01-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/warnTriggerRule")
public class WarnTriggerRuleController extends BaseController {

    private final IWarnTriggerRuleService iWarnTriggerRuleService;

    /**
     * 查询预警配置触发规则列表
     */
    @SaCheckPermission("business:warnTriggerRule:list")
    @GetMapping("/list")
    public TableDataInfo<WarnTriggerRuleVo> list(WarnTriggerRuleBo bo, PageQuery pageQuery) {
        return iWarnTriggerRuleService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出预警配置触发规则列表
     */
    @SaCheckPermission("business:warnTriggerRule:export")
    @Log(title = "预警配置触发规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WarnTriggerRuleBo bo, HttpServletResponse response) {
        List<WarnTriggerRuleVo> list = iWarnTriggerRuleService.queryList(bo);
        ExcelUtil.exportExcel(list, "预警配置触发规则", WarnTriggerRuleVo.class, response);
    }

    /**
     * 获取预警配置触发规则详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:warnTriggerRule:query")
    @GetMapping("/{id}")
    public R<WarnTriggerRuleVo> getInfo(@NotNull(message = "主键不能为空")
                                        @PathVariable Long id) {
        return R.ok(iWarnTriggerRuleService.queryById(id));
    }

    /**
     * 新增预警配置触发规则
     */
    @SaCheckPermission("business:warnTriggerRule:add")
    @Log(title = "预警配置触发规则", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WarnTriggerRuleBo bo) {
        return toAjax(iWarnTriggerRuleService.insertByBo(bo));
    }

    /**
     * 修改预警配置触发规则
     */
    @SaCheckPermission("business:warnTriggerRule:edit")
    @Log(title = "预警配置触发规则", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WarnTriggerRuleBo bo) {
        return toAjax(iWarnTriggerRuleService.updateByBo(bo));
    }

    /**
     * 删除预警配置触发规则
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:warnTriggerRule:remove")
    @Log(title = "预警配置触发规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iWarnTriggerRuleService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
