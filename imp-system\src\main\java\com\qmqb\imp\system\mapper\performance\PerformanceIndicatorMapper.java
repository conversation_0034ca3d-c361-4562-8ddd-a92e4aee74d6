package com.qmqb.imp.system.mapper.performance;

import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.performance.PerformanceIndicator;
import com.qmqb.imp.system.domain.vo.performance.PerformanceIndicatorVo;

/**
 * 绩效指标Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface PerformanceIndicatorMapper extends BaseMapperPlus<PerformanceIndicatorMapper, PerformanceIndicator, PerformanceIndicatorVo> {

}
