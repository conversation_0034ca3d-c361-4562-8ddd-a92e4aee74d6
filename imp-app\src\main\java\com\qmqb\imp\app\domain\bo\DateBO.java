package com.qmqb.imp.app.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <p>
 * 时间选择业务对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Data
public class DateBO {

    /**
     * 时间，格式：yyyy-MM，如2023-03
     */
    @NotNull(message = "时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM")
    @JsonFormat(pattern = "yyyy-MM")
    private Date date;
}
