package com.qmqb.imp.system.domain.bo;

import com.qmqb.imp.common.core.domain.BaseEntity;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 监控预警记录业务对象 tb_monitor_event_record
 *
 * <AUTHOR>
 * @date 2025-04-18
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class MonitorEventRecordBo extends BaseEntity {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 事件id
     */
    @NotNull(message = "事件id不能为空", groups = { AddGroup.class, EditGroup.class })
    @Schema(description = "事件id")
    private Long eventId;

    /**
     * 应用组ID
     */
    @NotNull(message = "应用组ID不能为空", groups = { AddGroup.class, EditGroup.class })
    @Schema(description = "应用组ID")
    private Long appGroupId;

    /**
     * 应用组名称
     */
    @NotBlank(message = "应用组名称不能为空", groups = { AddGroup.class, EditGroup.class })
    @Schema(description = "应用组名称")
    private String appGroupName;

    /**
     * 应用编码
     */
    @NotBlank(message = "应用编码不能为空", groups = { AddGroup.class, EditGroup.class })
    @Schema(description = "应用编码")
    private String appCode;

    /**
     * 应用名称
     */
    @NotBlank(message = "应用名称不能为空", groups = { AddGroup.class, EditGroup.class })
    @Schema(description = "应用名称")
    private String appName;

    /**
     * 所属服务器名
     */
    @NotBlank(message = "所属服务器名不能为空", groups = { AddGroup.class, EditGroup.class })
    @Schema(description = "所属服务器名")
    private String serverName;

    /**
     * 应用地址
     */
    @NotBlank(message = "应用地址不能为空", groups = { AddGroup.class, EditGroup.class })
    @Schema(description = "应用地址")
    private String appAddr;

    /**
     * 事件编码
     */
    @NotBlank(message = "事件编码不能为空", groups = { AddGroup.class, EditGroup.class })
    @Schema(description = "事件编码")
    private String eventCode;

    /**
     * 事件名称
     */
    @NotBlank(message = "事件名称不能为空", groups = { AddGroup.class, EditGroup.class })
    @Schema(description = "事件名称")
    private String eventName;

    /**
     * 事件内容
     */
    @NotBlank(message = "事件内容不能为空", groups = { AddGroup.class, EditGroup.class })
    @Schema(description = "事件内容")
    private String eventContent;

    /**
     * 事件类型: 0-运维故障, 1-应用故障, 2-业务系统故障
     */
    @NotNull(message = "事件类型: 0-运维故障, 1-应用故障, 2-业务系统故障不能为空", groups = { AddGroup.class, EditGroup.class })
    @Schema(description = "事件类型: 0-运维故障, 1-应用故障, 2-业务系统故障")
    private Integer eventType;

    /**
     * 告警类型: 0-故障发生告警, 1-故障恢复告警
     */
    @NotNull(message = "告警类型: 0-故障发生告警, 1-故障恢复告警不能为空", groups = { AddGroup.class, EditGroup.class })
    @Schema(description = "告警类型: 0-故障发生告警, 1-故障恢复告警")
    private Integer alertType;

    /**
     * 事件等级: 0-p0, 1-p1, 2-p2, 3-p3, 4-p4
     */
    @NotNull(message = "事件等级: 0-p0, 1-p1, 2-p2, 3-p3, 4-p4不能为空", groups = { AddGroup.class, EditGroup.class })
    @Schema(description = "事件等级: 0-p0, 1-p1, 2-p2, 3-p3, 4-p4")
    private Integer eventLevel;

    /**
     * 处理状态: 0-未处理, 1-处理中, 2-已处理
     */
    @NotNull(message = "处理状态: 0-未处理, 1-处理中, 2-已处理不能为空", groups = { AddGroup.class, EditGroup.class })
    @Schema(description = "处理状态: 0-未处理, 1-处理中, 2-已处理")
    private Integer status;

    /**
     * 处理结果
     */
    @Schema(description = "处理结果")
    private String result;

    /**
     * 处理时间
     */
    @Schema(description = "处理时间")
    private Date handleTime;

    /**
     * 处理人
     */
    @Schema(description = "处理人")
    private String handler;

    /**
     * 联系人
     */
    @Schema(description = "联系人,多个用逗号隔开")
    private String linkMans;

    @Schema(description = "部门ID")
    private Long deptId;

    @Schema(description = "管理员，技术总监是否查看全部数据 0-否 1-是")
    private String adminQueryFlag;

    @Schema(description = "创建时间-开始（范围查询用）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeBegin;

    @Schema(description = "创建时间-结束（范围查询用）")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;

}
