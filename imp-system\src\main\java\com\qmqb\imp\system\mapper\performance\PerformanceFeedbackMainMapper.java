package com.qmqb.imp.system.mapper.performance;

import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackMainQueryBo;
import com.qmqb.imp.system.domain.performance.PerformanceFeedback;
import com.qmqb.imp.system.domain.performance.PerformanceFeedbackMain;
import com.qmqb.imp.system.domain.vo.performance.PerformanceFeedbackMainVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 绩效反馈主表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface PerformanceFeedbackMainMapper extends BaseMapperPlus<PerformanceFeedbackMainMapper, PerformanceFeedbackMain, PerformanceFeedbackMainVo> {

    /**
     * 获取指定日期前缀的最大绩效反馈编码
     *
     * @param datePrefix
     * @return
     */
    String getMaxMainFeedbackCodeByDatePrefix(String datePrefix);


    /**
     * 删除指定日期系统生成的绩效反馈主表记录
     *
     * @param nickName 用户昵称
     * @param year     年份
     * @param month    月份
     * @return 删除的记录数
     */
    List<String> getIdByNickNameAndYearMonth(
        @Param("nickName") String nickName,
        @Param("year") Integer year,
        @Param("month") Integer month
    );

    /**
     * 根据二类指标查询绩效反馈记录
     *
     * @param secondaryIndicator 二类指标
     * @param year 年份
     * @param month 月份
     * @param nickNames 员工昵称列表
     * @return 绩效反馈记录列表
     */
    List<PerformanceFeedback> getBySecondaryIndicator(@Param("secondaryIndicator") String secondaryIndicator,
                                                     @Param("year") Integer year,
                                                     @Param("month") Integer month,
                                                     @Param("nickNames") List<String> nickNames);

    /**
     * 根据反馈表nick_name、group_id、recommended_level及主表条件分页查询主表全部数据
     * @param page 分页参数
     * @param queryBo 查询条件（含主表和反馈表条件）
     * @return 主表分页数据
     */
    IPage<PerformanceFeedbackMainVo> selectMainPageByFeedbackCondition(IPage<?> page, @Param("queryBo") PerformanceFeedbackMainQueryBo queryBo);
}
