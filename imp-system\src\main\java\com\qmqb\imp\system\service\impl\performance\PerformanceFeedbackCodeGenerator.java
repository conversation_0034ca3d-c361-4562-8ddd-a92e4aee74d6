package com.qmqb.imp.system.service.impl.performance;

import com.qmqb.imp.common.constant.PerformanceFeedbackConstants;
import com.qmqb.imp.system.mapper.performance.PerformanceEventMapper;
import com.qmqb.imp.system.mapper.performance.PerformanceFeedbackMainMapper;
import com.qmqb.imp.system.service.indicator.IPerformanceFeedbackMainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 绩效反馈编码生成工具类
 * <p>
 * 负责生成绩效反馈的唯一编码，格式为：FK+YYYYMMDD+4位流水号
 * 例如：FK202401270001
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PerformanceFeedbackCodeGenerator {

    /**
     * 流水号生成器，每天重置
     */
    private static final AtomicInteger ATOMIC_INTEGER = new AtomicInteger(PerformanceFeedbackConstants.DEFAULT_SEQUENCE_START);
    private static String CURRENT_DATE = "";
    @Autowired
    private PerformanceEventMapper performanceEventMapper;

    @PostConstruct
    public void init() {
        // 获取当前日期
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern(PerformanceFeedbackConstants.FEEDBACK_CODE_DATE_FORMAT));
        // 从数据库查询当前日期的最大流水号
        int maxSequence = getMaxSequenceFromDatabase(today);
        ATOMIC_INTEGER.set(maxSequence + 1);
        CURRENT_DATE = today;
    }


    /**
     * 生成反馈编码
     * 格式：FK+YYYYMMDD+4位流水号
     * 例如：FK202401270001
     *
     * @return 反馈编码
     */
    public synchronized String generateFeedbackCode() {
        // 获取当前日期
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern(PerformanceFeedbackConstants.FEEDBACK_CODE_DATE_FORMAT));

        // 如果日期变化，重置流水号
        if (!today.equals(CURRENT_DATE)) {
            CURRENT_DATE = today;
            ATOMIC_INTEGER.set(1);
        }

        // 获取当前流水号并递增
        int currentSequence = ATOMIC_INTEGER.getAndIncrement();

        // 检查流水号是否超出最大值
        if (currentSequence > PerformanceFeedbackConstants.MAX_SEQUENCE_NUMBER) {
            throw new RuntimeException("当日反馈记录数量已达上限：" + PerformanceFeedbackConstants.MAX_SEQUENCE_NUMBER);
        }

        // 格式化为4位数字，不足补0
        String sequenceStr = String.format(PerformanceFeedbackConstants.FEEDBACK_CODE_SEQUENCE_FORMAT, currentSequence);

        // 生成反馈编码：FK+YYYYMMDD+4位流水号
        String feedbackCode = PerformanceFeedbackConstants.FEEDBACK_CODE_PREFIX + today + sequenceStr;
        log.debug("生成绩效反馈编码：{}", feedbackCode);

        return feedbackCode;
    }

    /**
     * 从数据库查询指定日期的最大流水号
     *
     * @param dateStr 日期字符串，格式：yyyyMMdd
     * @return 最大流水号，如果没有记录则返回0
     */
    private int getMaxSequenceFromDatabase(String dateStr) {
        try {
            // 构建日期前缀
            String datePrefix = PerformanceFeedbackConstants.FEEDBACK_CODE_PREFIX + dateStr;

            // 查询数据库中该日期的最大反馈编码
            String maxFeedbackCode = performanceEventMapper.getMaxMainFeedbackCodeByDatePrefix(datePrefix);

            if (maxFeedbackCode == null || !maxFeedbackCode.startsWith(datePrefix)) {
                return 0;
            }

            // 提取流水号部分
            String sequenceStr = maxFeedbackCode.substring(datePrefix.length());
            return Integer.parseInt(sequenceStr);

        } catch (Exception e) {
            log.error("查询数据库最大流水号失败，日期：{}，错误：{}", dateStr, e.getMessage(), e);
            // 发生异常时，返回当前内存中的最大值，避免重复
            return ATOMIC_INTEGER.get();
        }
    }

    /**
     * 重置流水号（主要用于测试）
     */
    public synchronized void resetSequence() {
        CURRENT_DATE = "";
        ATOMIC_INTEGER.set(PerformanceFeedbackConstants.DEFAULT_SEQUENCE_START);
    }

    /**
     * 获取当前流水号（主要用于测试）
     *
     * @return 当前流水号
     */
    public synchronized int getCurrentSequence() {
        return ATOMIC_INTEGER.get();
    }

    /**
     * 获取当前日期字符串（主要用于测试）
     *
     * @return 当前日期字符串
     */
    public synchronized String getCurrentDate() {
        return CURRENT_DATE;
    }
}
