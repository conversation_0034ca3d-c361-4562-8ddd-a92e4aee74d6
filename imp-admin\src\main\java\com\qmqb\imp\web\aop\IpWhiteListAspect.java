package com.qmqb.imp.web.aop;

import com.qmqb.imp.common.exception.ServiceException;
import com.qmqb.imp.common.core.domain.entity.SysDictData;
import com.qmqb.imp.common.utils.ServletUtils;
import com.qmqb.imp.system.service.ISysDictTypeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class IpWhiteListAspect {
    private final ISysDictTypeService dictTypeService;
    private final HttpServletRequest request;

    @Around("@annotation(com.qmqb.imp.web.aop.IpWhiteListCheck)")
    public Object checkIpWhiteList(ProceedingJoinPoint joinPoint) throws Throwable {
        String clientIp = ServletUtils.getClientIP(request);
        log.info("OpenAPI请求来自IP: {}", clientIp);
        List<SysDictData> ipWhiteList = dictTypeService.selectDictDataByType("ip_white_list");
        if (ipWhiteList == null || ipWhiteList.isEmpty()) {
            throw new ServiceException("拒绝访问：IP 不在白名单中");
        }
        boolean inWhiteList = ipWhiteList.stream().anyMatch(dictData -> clientIp.equals(dictData.getDictValue()));
        if (!inWhiteList) {
            throw new ServiceException("拒绝访问：IP 不在白名单中");
        }
        return joinPoint.proceed();
    }
}

