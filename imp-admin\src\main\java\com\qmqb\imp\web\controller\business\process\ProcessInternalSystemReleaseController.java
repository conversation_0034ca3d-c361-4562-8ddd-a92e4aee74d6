package com.qmqb.imp.web.controller.business.process;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.bo.process.ProcessInternalSystemReleaseBo;
import com.qmqb.imp.system.domain.vo.process.ProcessInternalSystemReleaseVo;
import com.qmqb.imp.system.service.process.IProcessInternalSystemReleaseService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 内部管理系统发布
 *
 * <AUTHOR>
 * @date 2024-12-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/processInternalSystemRelease")
public class ProcessInternalSystemReleaseController extends BaseController {

    private final IProcessInternalSystemReleaseService iProcessInternalSystemReleaseService;

    /**
     * 查询内部管理系统发布列表
     */
    @SaCheckPermission("system:processInternalSystemRelease:list")
    @GetMapping("/list")
    public TableDataInfo<ProcessInternalSystemReleaseVo> list(ProcessInternalSystemReleaseBo bo, PageQuery pageQuery) {
        return iProcessInternalSystemReleaseService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出内部管理系统发布列表
     */
    @SaCheckPermission("system:processInternalSystemRelease:export")
    @Log(title = "内部管理系统发布", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProcessInternalSystemReleaseBo bo, HttpServletResponse response) {
        List<ProcessInternalSystemReleaseVo> list = iProcessInternalSystemReleaseService.queryList(bo);
        ExcelUtil.exportExcel(list, "内部管理系统发布", ProcessInternalSystemReleaseVo.class, response);
    }

    /**
     * 获取内部管理系统发布详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:processInternalSystemRelease:query")
    @GetMapping("/{id}")
    public R<ProcessInternalSystemReleaseVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iProcessInternalSystemReleaseService.queryById(id));
    }

    /**
     * 新增内部管理系统发布
     */
    @SaCheckPermission("system:processInternalSystemRelease:add")
    @Log(title = "内部管理系统发布", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProcessInternalSystemReleaseBo bo) {
        return toAjax(iProcessInternalSystemReleaseService.insertByBo(bo));
    }

    /**
     * 修改内部管理系统发布
     */
    @SaCheckPermission("system:processInternalSystemRelease:edit")
    @Log(title = "内部管理系统发布", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProcessInternalSystemReleaseBo bo) {
        return toAjax(iProcessInternalSystemReleaseService.updateByBo(bo));
    }

    /**
     * 删除内部管理系统发布
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:processInternalSystemRelease:remove")
    @Log(title = "内部管理系统发布", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iProcessInternalSystemReleaseService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
