-- ================================================================
-- 代码扫描新需求数据库升级SQL脚本
-- 日期：2024-12-26
-- 说明：为提升代码问题流转效率及可视化程度，对数据库表结构进行升级
-- ================================================================

-- 1. 清除历史数据
-- 因为数据表结构变化，清除tb_scan_project_detail表和tb_scan_project表中问题数据
DELETE FROM tb_scan_project_detail;

DELETE FROM tb_scan_project;

-- 2. 修改tb_scan_project表结构，删除轻微问题列
-- 修改tb_scan_project数据表结构，把轻微问题列删除掉
ALTER TABLE tb_scan_project DROP COLUMN major_amount;

-- 3. 创建扫描项目文件记录表
-- 新增维度：代码库 -> 文件(java类) -> 详细问题
create table tb_scan_project_file
(
    id              bigint                             not null comment '主键id'
        primary key,
    p_id            int                                not null comment '项目id',
    scan_version    bigint                             not null comment '扫描版本号',
    last_scan_flag  tinyint                            null comment '是否最新扫描（1新纪录，0旧记录）',
    scan_file_url   varchar(300)                       null comment '问题文件路径',
    blocker_amount  int                                null comment '严重问题数（master分支）',
    critical_amount int                                null comment '一般问题数（master分支）',
    status          varchar(10) default '0'            null comment '状态（0未指派，1已指派未处理，2已指派已处理）',
    handle_user     varchar(50)                        null comment '处理人',
    handle_user_id  bigint                             null comment '处理人id',
    handle_time     datetime                           null comment '处理时间',
    handle_remark   varchar(2048)                       null comment '处理说明',
    assign_user     varchar(50)                        null comment '指派人',
    assign_user_id  bigint                             null comment '指派人id',
    assign_time     datetime                           null comment '指派时间',
    del_flag        tinyint  default 0                 null comment '删除标志（0代表存在 2代表删除）',
    create_time     datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time     datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint tb_scan_project_file_p_id_scan_version_file_uindex
        unique (p_id, scan_version, scan_file_url)
)
    comment '扫描项目文件记录表';

-- 为tb_scan_project_file表创建索引
CREATE INDEX tb_scan_project_file_p_id_index
    ON tb_scan_project_file (p_id, last_scan_flag);

-- 4. 修改tb_scan_project_detail表，添加与文件表的关联字段
ALTER TABLE tb_scan_project_detail ADD COLUMN file_id BIGINT NULL COMMENT '关联文件表id' AFTER scan_version;

-- 为新增的关联字段创建索引
CREATE INDEX tb_scan_project_detail_file_id_index
    ON tb_scan_project_detail (file_id);

-- 5. 修改字段类型为字符串
-- 修改tb_scan_project表的dev_dept字段为varchar类型
ALTER TABLE tb_scan_project MODIFY COLUMN dev_dept varchar(50) COMMENT '负责开发组';

-- 修改tb_project表的p_dev_dept和p_test_dept字段为varchar类型
ALTER TABLE tb_project MODIFY COLUMN p_dev_dept varchar(50) COMMENT '负责开发组';
ALTER TABLE tb_project MODIFY COLUMN p_test_dept varchar(50) COMMENT '负责测试组';

-- ================================================================
-- SQL执行完成
-- ================================================================

-- 代码管理数据字典

INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938048962680963074, '代码质量管理负责开发组', 'code_dev_dept', '0', 'admin', '2025-06-26 09:37:35', 'admin', '2025-06-26 09:37:35', NULL);

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938049271478206466, 0, '全部', '0', 'code_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-26 09:38:48', 'admin', '2025-06-26 09:38:48', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938049324951388161, 1, '风控开发组', '103', 'code_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-26 09:39:01', 'admin', '2025-06-26 09:40:11', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938049504517931009, 2, 'Android开发组', '106', 'code_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-26 09:39:44', 'admin', '2025-06-26 09:40:18', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938049565884792833, 3, '创新开发组', '107', 'code_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-26 09:39:58', 'admin', '2025-06-26 09:40:21', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938049826153938946, 4, '催收组', '108', 'code_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-26 09:41:00', 'admin', '2025-06-26 09:41:00', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938049882827374593, 5, '前端开发组', '110', 'code_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-26 09:41:14', 'admin', '2025-06-26 09:41:14', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938049950389223425, 6, '资金开发组', '111', 'code_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-26 09:41:30', 'admin', '2025-06-26 09:41:30', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938050002893520897, 7, 'BI组', '113', 'code_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-26 09:41:43', 'admin', '2025-06-26 09:41:43', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938050051136405505, 8, '架构开发组', '114', 'code_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-26 09:41:54', 'admin', '2025-06-26 09:41:54', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938050115808378881, 9, '资产开发一组', '115', 'code_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-26 09:42:09', 'admin', '2025-06-26 09:42:09', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938050189342916610, 10, '财务开发一组', '116', 'code_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-26 09:42:27', 'admin', '2025-06-26 09:42:27', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938050331789869057, 11, '资产运营开发组', '117', 'code_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-26 09:43:01', 'admin', '2025-06-26 09:43:01', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938050398546411521, 12, 'IOS开发组', '119', 'code_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-26 09:43:17', 'admin', '2025-06-26 09:43:17', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938050589575987202, 13, '财务开发二组', '1782659977081524226', 'code_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-26 09:44:02', 'admin', '2025-06-26 09:44:02', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938050690746793985, 14, '资产开发二组', '1830780594219999233', 'code_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-26 09:44:27', 'admin', '2025-06-26 09:44:27', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938050831935455233, 15, '工具开发组', '1843829274434977794', 'code_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-26 09:45:00', 'admin', '2025-06-26 09:45:00', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938051155949633537, 0, '其他组', '1', 'code_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-26 09:46:17', 'admin', '2025-06-26 09:46:17', NULL);

INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938531822886019073, '代码质量管理状态下拉框', 'code_quality_file_status', '0', 'admin', '2025-06-27 17:36:17', 'admin', '2025-06-27 17:36:17', NULL);

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938532079925551106, 3, '已指派已处理', '2', 'code_quality_file_status', NULL, 'default', 'N', '0', 'admin', '2025-06-27 17:37:19', 'admin', '2025-07-01 17:31:17', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938532035679838210, 2, '已指派未处理', '1', 'code_quality_file_status', NULL, 'default', 'N', '0', 'admin', '2025-06-27 17:37:08', 'admin', '2025-07-01 17:31:14', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938531970030592002, 1, '未指派', '0', 'code_quality_file_status', NULL, 'default', 'N', '0', 'admin', '2025-06-27 17:36:52', 'admin', '2025-07-01 17:31:07', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938532620940435458, 0, '全部', '-1', 'code_quality_file_status', NULL, 'default', 'N', '0', 'admin', '2025-06-27 17:39:28', 'admin', '2025-07-01 17:31:03', NULL);


INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938841689416884225, '代码质量管理负责测试组', 'code_test_dept', '0', 'admin', '2025-06-28 14:07:35', 'admin', '2025-06-28 14:07:35', NULL);

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938841925820440578, 0, '全部', '0', 'code_test_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-28 14:08:32', 'admin', '2025-06-28 14:08:32', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938841985144676353, 1, '风控运营测试组', '109', 'code_test_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-28 14:08:46', 'admin', '2025-06-28 14:08:46', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938842048206036993, 2, '助贷测试一组', '118', 'code_test_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-28 14:09:01', 'admin', '2025-06-28 14:09:01', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938842096004325378, 3, '海外测试组', '120', 'code_test_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-28 14:09:12', 'admin', '2025-06-28 14:09:12', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938842169849241601, 4, '助贷测试二组', '1830780365290692610', 'code_test_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-28 14:09:30', 'admin', '2025-06-28 14:09:30', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938842230331105281, 5, '贷后测试组', '1830899936018391041', 'code_test_dept', NULL, 'default', 'N', '0', 'admin', '2025-06-28 14:09:44', 'admin', '2025-06-28 14:09:44', NULL);

#慢sql
UPDATE `sys_menu` SET `menu_name` = '生产慢SQL管理', `parent_id` = 1600332606792245249, `order_num` = 10, `path` = 'querySql', `component` = 'system/slowQuery/index', `query_param` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = NULL, `icon` = 'build', `create_by` = 'admin', `create_time` = '2025-05-08 19:51:01', `update_by` = 'admin', `update_time` = '2025-05-08 20:11:29', `remark` = '' WHERE `menu_id` = 1920446335562645505;
UPDATE `sys_menu` SET `menu_name` = '指派/更换指派', `parent_id` = 1920446335562645505, `order_num` = 1, `path` = '', `component` = NULL, `query_param` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'system:slowQuery:assign', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-06-28 17:08:21', `update_by` = 'admin', `update_time` = '2025-06-30 15:29:41', `remark` = '' WHERE `menu_id` = 1938887179557326849;
UPDATE `sys_menu` SET `menu_name` = '搜索', `parent_id` = 1920446335562645505, `order_num` = 3, `path` = '', `component` = NULL, `query_param` = NULL, `is_frame` = 1, `is_cache` = 0, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = NULL, `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-04 17:19:11', `update_by` = 'admin', `update_time` = '2025-07-04 17:19:11', `remark` = '' WHERE `menu_id` = 1941064234555789314;
UPDATE `sys_menu` SET `menu_name` = '慢sql综合统计', `parent_id` = 1600332606792245249, `order_num` = 11, `path` = 'slowQueryStat', `component` = 'system/slowQuery/statistic', `query_param` = '', `is_frame` = 1, `is_cache` = 0, `menu_type` = 'C', `visible` = '1', `status` = '0', `perms` = 'system:slowQuery:statistic', `icon` = 'chart', `create_by` = 'admin', `create_time` = '2025-05-27 17:02:12', `update_by` = 'admin', `update_time` = '2025-07-04 15:44:47', `remark` = '' WHERE `menu_id` = 1927289218514083841;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1941064234555789314, '搜索', 1920446335562645505, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', NULL, '#', 'admin', '2025-07-04 17:19:11', 'admin', '2025-07-04 17:19:11', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938763994217668609, '数据库权限管理', 1, 11, 'databasePermission', 'system/databasePermission/index', NULL, 1, 0, 'C', '0', '0', 'system:dataBasePermission:list', 'table', 'admin', '2025-06-28 08:58:51', 'admin', '2025-06-30 15:31:43', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1939504779303133185, '编辑', 1938763994217668609, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:dataBasePermission:edit', '#', 'admin', '2025-06-30 10:02:28', 'admin', '2025-06-30 15:32:10', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1939504730351411202, '新增', 1938763994217668609, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:dataBasePermission:add', '#', 'admin', '2025-06-30 10:02:17', 'admin', '2025-06-30 15:31:55', '');
#代码质量
UPDATE `sys_menu` SET `menu_name` = '问题明细', `parent_id` = 1600371350308102145, `order_num` = 2, `path` = 'qualityDetail', `component` = 'code/quality/detail', `query_param` = NULL, `is_frame` = 1, `is_cache` = 1, `menu_type` = 'C', `visible` = '1', `status` = '0', `perms` = 'system:scanProjectFile:list', `icon` = '#', `create_by` = 'admin', `create_time` = '2024-12-09 16:14:22', `update_by` = 'admin', `update_time` = '2025-06-28 11:25:24', `remark` = '' WHERE `menu_id` = 1866033631083450369;
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938871784722489345, '查看详情', 1866033631083450369, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:scanProjectDetail:list', '#', 'admin', '2025-06-28 16:07:11', 'admin', '2025-06-28 16:07:11', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938504607603449857, '处理', 1866033631083450369, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:scanProjectFile:process', '#', 'admin', '2025-06-27 15:48:09', 'admin', '2025-07-08 10:52:03', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1938504529404846082, '指派/更换指派', 1866033631083450369, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:scanProjectFile:assign', '#', 'admin', '2025-06-27 15:47:50', 'admin', '2025-07-08 10:51:55', '');
