package com.qmqb.imp.app.domain.vo;

import lombok.Builder;
import lombok.Data;

/**
 * 关键指标视图对象
 *
 * <AUTHOR> Li
 */

@Data
@Builder
public class WarnDetailVO {

    /**
     * 预警id
     */
    private Long id;
    /**
     * 预警类型
     */
    private Integer warnType;
    /**
     * 预警内容
     */
    private String content;

    /**
     * 用户姓名
     */
    private String nickName;

    /**
     * 预警时间
     */
    private String date;
    /**
     * 处理回复
     */
    @Builder.Default
    private String handleReply = "未处理";
    /**
     * 是否处理：0-否，2-是
     */
    private Integer isHandle;

}
