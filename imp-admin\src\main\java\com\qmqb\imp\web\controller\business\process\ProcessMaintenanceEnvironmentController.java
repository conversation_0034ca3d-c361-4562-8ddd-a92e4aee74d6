package com.qmqb.imp.web.controller.business.process;

import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.system.domain.vo.process.ProcessMaintenanceEnvironmentVo;
import com.qmqb.imp.system.domain.bo.process.ProcessMaintenanceEnvironmentBo;
import com.qmqb.imp.system.service.process.IProcessMaintenanceEnvironmentService;
import com.qmqb.imp.common.core.page.TableDataInfo;

/**
 * 运维&DBA&BI生产环境维护操作申请
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/processMaintenanceEnvironment")
public class ProcessMaintenanceEnvironmentController extends BaseController {

    private final IProcessMaintenanceEnvironmentService iProcessMaintenanceEnvironmentService;

    /**
     * 查询运维&DBA&BI生产环境维护操作申请列表
     */
    @SaCheckPermission("system:processMaintenanceEnvironment:list")
    @GetMapping("/list")
    public TableDataInfo<ProcessMaintenanceEnvironmentVo> list(ProcessMaintenanceEnvironmentBo bo, PageQuery pageQuery) {
        return iProcessMaintenanceEnvironmentService.queryPageList(bo, pageQuery);
    }


}
