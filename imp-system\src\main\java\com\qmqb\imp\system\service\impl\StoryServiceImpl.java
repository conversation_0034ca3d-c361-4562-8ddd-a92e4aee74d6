package com.qmqb.imp.system.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.dto.StoryQueryDTO;
import com.qmqb.imp.common.enums.ZtStoryStatusEnum;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.system.domain.Story;
import com.qmqb.imp.system.domain.ZtAction;
import com.qmqb.imp.system.domain.vo.StoryReleaseCountVO;
import com.qmqb.imp.system.domain.vo.StoryVO;
import com.qmqb.imp.system.mapper.StoryMapper;
import com.qmqb.imp.system.service.IStoryService;
import com.qmqb.imp.system.service.IZtActionService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@Service
public class StoryServiceImpl extends ServiceImpl<StoryMapper, Story> implements IStoryService {

    @Autowired
    private IZtActionService iZtActionService;

    @Override
    @DS("zentao")
    public Page<StoryVO> page(StoryQueryDTO request) {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageSize(request.getPageSize());
        pageQuery.setPageNum(request.getPageNo());
        if (StringUtils.isNotBlank(request.getOrderByField())) {
            pageQuery.setOrderByColumn(request.getOrderByField());
            pageQuery.setIsAsc(request.getOrderRule());
        }
        if (request.getBeginDate() != null) {
            request.setBeginDate(DateUtils.dateToStart(request.getBeginDate()));
        }
        if (request.getEndDate() != null) {
            request.setEndDate(DateUtils.dateToEnd(request.getEndDate()));
        }
        Page<StoryVO> build = pageQuery.build();
        if (CollectionUtils.isEmpty(build.orders())) {
            build.setOrders(Arrays.asList(new OrderItem("s.stage", true), new OrderItem("s.openedDate", false)));
        }
        Page<StoryVO> page = this.baseMapper.getLists(build, request);
        List<StoryVO> lists = page.getRecords();
        // 处理评审人和评审时间 查询zt_action表 action = reviewed
        lists.forEach(storyVO -> {
            storyVO.setRevieweddate(null);
            if (storyVO.getId() != null) {
                List<ZtAction> ztActions = iZtActionService.getActionsByObjIdAndType(storyVO.getId(), "reviewed");
                if (ztActions != null && !ztActions.isEmpty()) {
                    ZtAction ztAction = ztActions.get(0);
                    storyVO.setReviewer(ztAction.getActor());
                    storyVO.setRevieweddate(ztAction.getDate());
                }
            }
        });
        Map<Integer, Integer> releaseCountMap = lists.isEmpty() ? Collections.emptyMap() : this.baseMapper
            .storyReleaseCount(lists.stream().map(StoryVO::getId).collect(Collectors.toList())).stream()
            .collect(Collectors.toMap(StoryReleaseCountVO::getStoryId, StoryReleaseCountVO::getReleaseCount));
        lists.forEach(storyVO -> storyVO.setReleaseCount(releaseCountMap.getOrDefault(storyVO.getId(), 0)));
        return page;
    }


    @Override
    @DS("zentao")
    public Long selectStoryCountByStatus(Date monthStartTime, Date monthEndTime, ZtStoryStatusEnum ztStoryStatusEnum, List<String> usernameList) {
        Long count = baseMapper.selectStoryCountByStatus(ztStoryStatusEnum.getValue(), monthStartTime, monthEndTime, usernameList);
        return count;
    }

    @Override
    @DS("zentao")
    public List<Story> getTitleList(List<Integer> storyIds) {
        List<Story> stories = baseMapper.selectList(
            new LambdaQueryWrapper<Story>()
                .in(Story::getId, storyIds)
                .select(Story::getTitle)
        );
        return stories;
    }

    @Override
    public List<StoryVO> getStoryListByDetail() {
        return baseMapper.getStoryListByDetail();
    }


}
