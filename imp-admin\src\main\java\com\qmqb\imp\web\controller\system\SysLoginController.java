package com.qmqb.imp.web.controller.system;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.stp.StpUtil;
import com.qmqb.imp.common.constant.Constants;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.entity.SysMenu;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginBody;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.core.domain.model.SmsLoginBody;
import com.qmqb.imp.common.core.domain.model.DingTalkLoginBody;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.job.api.client.DingTalkApiClient;
import com.qmqb.imp.job.api.client.DingTalkOApiClient;
import com.qmqb.imp.system.domain.dto.DingTalkUserInfo;
import com.qmqb.imp.system.domain.vo.RouterVo;
import com.qmqb.imp.system.service.ISysMenuService;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.SysLoginService;
import com.qmqb.imp.system.service.SysPermissionService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 登录验证
 *
 * <AUTHOR> Li
 */
@Validated
@RequiredArgsConstructor
@RestController
public class SysLoginController {

    private final SysLoginService loginService;
    private final ISysMenuService menuService;
    private final ISysUserService userService;
    private final SysPermissionService permissionService;
    private final DingTalkApiClient dingTalkApiClient;

    @Value("${indonesia.jixiao.url}")
    private String indonesiaJixiaoUrl;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/login")
    public R<Map<String, Object>> login(@Validated @RequestBody LoginBody loginBody) {
        Map<String, Object> ajax = new HashMap<>(16);
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
            loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * 免登录跳转
     * @param tokenValue
     * @return
     */
    @SaIgnore
    @GetMapping("/checkToken")
    public R<Map<String, Object>> noLoginJump(String tokenValue) {
        Map<String, Object> ajax = new HashMap<>(16);
        String token = loginService.noLoginJump(tokenValue);
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * 获取印尼绩效系统地址
     * @return
     */
    @GetMapping("/getIndonesiaJixiaoUrl")
    public R<String> getIndonesiaJixiaoUrl() {
        return R.ok("获取印尼绩效系统地址", indonesiaJixiaoUrl);
    }

    /**
     * 解析token
     * @param tokenValue
     * @return
     */
    @SaIgnore
    @GetMapping("/analysisToken")
    public R<Long> analysisToken(@RequestParam String tokenValue) {
        try {
            Object loginIdByToken = StpUtil.getLoginIdByToken(tokenValue);
            if (loginIdByToken == null) {
                return R.fail("token无效");
            }
            Long loginId = Long.valueOf(loginIdByToken.toString().split(":")[1]);
            return R.ok(loginId);
        } catch (Exception e) {
            return R.fail("token解析失败");
        }
    }


    /**
     * 短信登录(示例)
     *
     * @param smsLoginBody 登录信息
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/smsLogin")
    public R<Map<String, Object>> smsLogin(@Validated @RequestBody SmsLoginBody smsLoginBody) {
        Map<String, Object> ajax = new HashMap<>(16);
        // 生成令牌
        String token = loginService.smsLogin(smsLoginBody.getPhonenumber(), smsLoginBody.getSmsCode());
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * 小程序登录(示例)
     *
     * @param xcxCode 小程序code
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/xcxLogin")
    public R<Map<String, Object>> xcxLogin(@NotBlank(message = "{xcx.code.not.blank}") String xcxCode) {
        Map<String, Object> ajax = new HashMap<>(16);
        // 生成令牌
        String token = loginService.xcxLogin(xcxCode);
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * 钉钉登录
     *
     * @param dingTalkLoginBody 钉钉登录信息
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/dingTalkLogin")
    public R<Map<String, Object>> dingTalkLogin(@Validated @RequestBody DingTalkLoginBody dingTalkLoginBody) {
        Map<String, Object> ajax = new HashMap<>(16);
        DingTalkUserInfo dingTalkUser = dingTalkApiClient.getDingTalkUser(dingTalkLoginBody.getAuthCode());
        // 生成令牌
        String token = loginService.dingTalkLogin(dingTalkUser);
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * 退出登录
     */
    @SaIgnore
    @PostMapping("/logout")
    public R<Void> logout() {
        loginService.logout();
        return R.ok("退出成功");
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public R<Map<String, Object>> getInfo() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        SysUser user = userService.selectUserById(loginUser.getUserId());
        Map<String, Object> ajax = new HashMap<>(16);
        ajax.put("user", user);
        ajax.put("roles", loginUser.getRolePermission());
        ajax.put("permissions", loginUser.getMenuPermission());
        return R.ok(ajax);
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public R<List<RouterVo>> getRouters() {
        Long userId = LoginHelper.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return R.ok(menuService.buildMenus(menus));
    }
}
