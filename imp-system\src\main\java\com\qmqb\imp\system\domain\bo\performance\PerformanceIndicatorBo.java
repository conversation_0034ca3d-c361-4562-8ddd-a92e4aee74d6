package com.qmqb.imp.system.domain.bo.performance;

import com.qmqb.imp.common.core.domain.BaseEntity;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 绩效指标业务对象 tb_performance_indicator
 *
 * <AUTHOR>
 * @date 2025-06-24
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class PerformanceIndicatorBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 绩效主表ID
     */
    @NotNull(message = "绩效主表ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long performanceId;

    /**
     * 指标编码
     */
    @NotBlank(message = "指标编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String indicatorCode;

    /**
     * 指标名称
     */
    @NotBlank(message = "指标名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String indicatorName;

    /**
     * 指标分类编码
     */
    private String categoryCode;

    /**
     * 评价等级S/A/B/C/D
     */
    @NotBlank(message = "评价等级S/A/B/C/D不能为空", groups = {AddGroup.class, EditGroup.class})
    private String scoreLevel;

    /**
     * 日志登记内容
     */
    @NotBlank(message = "日志登记内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String logContent;


}
