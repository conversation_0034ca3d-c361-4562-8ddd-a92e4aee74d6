package com.qmqb.imp.system.service;

import com.qmqb.imp.system.domain.PerformanceReport;
import com.qmqb.imp.system.domain.bo.PerformanceReportBo;

import java.util.List;

/**
 * 绩效分析报告Service接口
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface IPerformanceReportService {


    /**
     * 批量保存绩效分析报告
     * @param bo
     * @return
     */
    Boolean saveReport(PerformanceReportBo bo);

    /**
     * 根据年份月份查询绩效分析报告列表
     * @param year
     * @param month
     * @return
     */
    List<PerformanceReport> getListByDate(String year, String month);
}
