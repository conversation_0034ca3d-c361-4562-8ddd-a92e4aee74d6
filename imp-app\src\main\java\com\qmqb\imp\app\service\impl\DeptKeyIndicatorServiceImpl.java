package com.qmqb.imp.app.service.impl;

import com.qmqb.imp.app.domain.bo.DeptKeyIndicatorBO;
import com.qmqb.imp.app.domain.vo.KeyIndicatorVO;
import com.qmqb.imp.app.domain.vo.KeyValueVO;
import com.qmqb.imp.app.service.DeptKeyIndicatorService;
import com.qmqb.imp.app.service.HomepageService;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.entity.SysDept;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.service.ISysDeptService;
import com.qmqb.imp.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeptKeyIndicatorServiceImpl implements DeptKeyIndicatorService {

    private final ISysDeptService sysDeptService;
    private final ISysUserService sysUserService;
    private final HomepageService homepageService;


    /**
     * 组列表
     *
     * @return
     */
    @Override
    public R<List<KeyValueVO>> groupSelect() {
        SysDept sysDept = new SysDept();
        sysDept.setParentId(101L);
        List<SysDept> sysDepts = sysDeptService.selectDeptList(sysDept);
        List<KeyValueVO> vos = sysDepts.stream().map(e -> {
            return KeyValueVO.builder().key(e.getDeptName()).value(String.valueOf(e.getDeptId())).build();
        }).collect(Collectors.toList());
        SysUser sysUser = sysUserService.selectUserById(LoginHelper.getUserId());
        if (sysUser.isAdmin() || sysUser.isJszxAdmin()) {
            vos.add(0, KeyValueVO.builder().key("全部").value("-1").build());
        } else {
            vos = vos.stream().filter(vo -> StringUtils.equals(vo.getValue(), String.valueOf(sysUser.getDeptId()))).collect(Collectors.toList());
        }
        return R.ok(vos);
    }

    /**
     * 指标详情
     *
     * @param bo
     * @return
     */
    @Override
    public R<KeyIndicatorVO> detail(DeptKeyIndicatorBO bo) {
        return homepageService.keyIndicator(bo, bo.getDeptId());
    }
}
