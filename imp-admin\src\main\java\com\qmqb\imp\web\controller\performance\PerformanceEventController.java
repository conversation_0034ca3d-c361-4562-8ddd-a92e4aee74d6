package com.qmqb.imp.web.controller.performance;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.bo.performance.PerformanceEventBo;
import com.qmqb.imp.system.domain.dto.BatchSubmitRequest;
import com.qmqb.imp.system.domain.vo.performance.PerformanceEventVo;
import com.qmqb.imp.system.service.indicator.IPerformanceEventService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 绩效事件
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/performanceEvent")
public class PerformanceEventController extends BaseController {

    private final IPerformanceEventService iPerformanceEventService;

    /**
     * 查询绩效事件列表
     */
    @SaCheckPermission("system:performanceEvent:list")
    @GetMapping("/list")
    public TableDataInfo<PerformanceEventVo> list(PerformanceEventBo bo, PageQuery pageQuery) {
        return iPerformanceEventService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出绩效事件列表
     */
    @SaCheckPermission("system:performanceEvent:export")
    @Log(title = "绩效事件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PerformanceEventBo bo, HttpServletResponse response) {
        List<PerformanceEventVo> list = iPerformanceEventService.queryList(bo);
        ExcelUtil.exportExcel(list, "绩效事件", PerformanceEventVo.class, response);
    }

    /**
     * 获取绩效事件详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:performanceEvent:query")
    @GetMapping("/{id}")
    public R<PerformanceEventVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iPerformanceEventService.queryById(id));
    }

    /**
     * 新增绩效事件
     */
    @SaCheckPermission("system:performanceEvent:add")
    @Log(title = "绩效事件", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PerformanceEventBo bo) {
        return toAjax(iPerformanceEventService.insertByBo(bo));
    }

    /**
     * 修改绩效事件
     */
    @SaCheckPermission("system:performanceEvent:edit")
    @Log(title = "绩效事件", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PerformanceEventBo bo) {
        return toAjax(iPerformanceEventService.updateByBo(bo));
    }

    /**
     * 删除绩效事件
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:performanceEvent:remove")
    @Log(title = "绩效事件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iPerformanceEventService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 批量提交绩效事件
     */
    @SaCheckPermission("system:performanceEvent:batchSubmit")
    @Log(title = "绩效事件-批量提交", businessType = BusinessType.UPDATE)
    @PostMapping("/batchSubmit")
    public R<Void> batchSubmit(@RequestBody @Validated BatchSubmitRequest request) {
        String submitter = LoginHelper.getUsername();
        return toAjax(iPerformanceEventService.batchSubmit(request.getIds(), submitter));
    }

    /**
     * 保存并提交绩效事件
     */
    @SaCheckPermission("system:performanceEvent:batchSubmit")
    @Log(title = "绩效事件-保存并提交", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/saveAndSubmit")
    public R<Void> saveAndSubmit(@Validated(AddGroup.class) @RequestBody PerformanceEventBo bo) {
        return toAjax(iPerformanceEventService.saveAndSubmit(bo));
    }
}
