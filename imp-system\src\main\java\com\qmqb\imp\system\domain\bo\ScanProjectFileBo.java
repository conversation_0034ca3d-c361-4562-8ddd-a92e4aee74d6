package com.qmqb.imp.system.domain.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.qmqb.imp.common.core.domain.dto.BasePageDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 扫描项目文件记录业务对象 tb_scan_project_file
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ScanProjectFileBo extends BasePageDTO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 项目id
     */
    @JsonProperty("pId")
    private Long pId;

    /**
     * 扫描版本号
     */
    private Long scanVersion;

    /**
     * 是否最新扫描（1新纪录，0旧记录）
     */
    private Integer lastScanFlag;

    /**
     * 问题文件路径
     */
    private String scanFileUrl;

    /**
     * 严重问题数（master分支）
     */
    private Long blockerAmount;

    /**
     * 一般问题数（master分支）
     */
    private Long criticalAmount;

    /**
     * 状态（0未指派，1已指派未处理，2已指派已处理）
     */
    private String status;

    /**
     * 处理人
     */
    private String handleUser;

    /**
     * 处理人id
     */
    private Long handleUserId;

    /**
     * 处理时间
     */
    private Date handleTime;

    /**
     * 处理时间开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime handleTimeBegin;

    /**
     * 处理时间结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime handleTimeEnd;

    /**
     * 处理说明
     */
    private String handleRemark;

    /**
     * 指派人
     */
    private String assignUser;

    /**
     * 指派人id
     */
    private Long assignUserId;

    /**
     * 指派时间
     */
    private Date assignTime;

    /**
     * 指派时间开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime assignTimeBegin;

    /**
     * 指派时间结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime assignTimeEnd;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
