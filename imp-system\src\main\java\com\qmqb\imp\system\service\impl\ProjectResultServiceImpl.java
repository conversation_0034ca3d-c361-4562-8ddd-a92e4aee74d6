package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.entity.SysDictData;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.common.utils.spring.SpringUtils;
import com.qmqb.imp.system.domain.*;
import com.qmqb.imp.system.domain.bo.BusinessTypeBo;
import com.qmqb.imp.system.domain.bo.ProjectResultBo;
import com.qmqb.imp.system.domain.vo.BusinessTypeVo;
import com.qmqb.imp.system.domain.vo.ProjectResultVo;
import com.qmqb.imp.system.mapper.ProjectResultMapper;
import com.qmqb.imp.system.service.IProjectResultService;
import com.qmqb.imp.system.service.IProjectResultNumberService;
import com.qmqb.imp.system.mapper.StoryResultMapper;
import com.qmqb.imp.system.mapper.ZtReleaseMapper;
import com.qmqb.imp.system.service.*;
import com.qmqb.imp.system.service.IStoryResultService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import org.springframework.core.io.ByteArrayResource;
import java.math.BigDecimal;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;
import com.qmqb.imp.system.domain.vo.ProjectResultEmailExportVo;
import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import com.alibaba.excel.EasyExcel;

/**
 * 项目成果表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProjectResultServiceImpl implements IProjectResultService {

    private final ProjectResultMapper baseMapper;
    private final IProjectResultNumberService projectResultNumberService;
    private final IStoryResultService storyResultService;
    private final IBusinessTypeService businessTypeService;

    private final IStoryService storyService;

    private final IZtActionService actionService;

    private final IZtBuildService buildService;

    private final ZtReleaseMapper ztReleaseMapper;

    private final IZtUserService ztUserService;

    private final IZtTaskService taskService;

    private final IZtStoryspecService storyspecService;

    private final StoryResultMapper storyResultMapper;

    private final ISysUserService sysUserService;

    private final JavaMailSender javaMailSender;

    private final ISysDictTypeService dictTypeService;

    private final ISysUserService userService;

    @Value("${spring.mail.username}")
    private String fromEmail;

    /**
     * 查询项目成果表
     */
    @Override
    public ProjectResultVo queryById(Long id) {
        ProjectResultVo vo = baseMapper.selectVoByIdWithBusinessType(id);
        Map<String,String> nickNameMap = userService.selectAllUser2().stream().collect(Collectors.toMap(SysUser::getNickName, SysUser::getNickName,(v1,v2)->v1));
        if (vo!=null) {
            vo.setCreateBy(nickNameMap.get(vo.getCreateBy()));
        }
        return vo;
    }

    /**
     * 查询项目成果表列表
     */
    @Override
    public TableDataInfo<ProjectResultVo> queryPageList(ProjectResultBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProjectResult> lqw = buildQueryWrapper(bo);

        // 构建分页对象，但不包含排序信息（避免重复排序）
        Page<ProjectResult> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());

        // 处理排序参数
        String orderByColumn = pageQuery.getOrderByColumn();
        String[] orderByColumns = null;
        String[] isAscArray = null;

        if (StringUtils.isNotBlank(orderByColumn)) {
            orderByColumns = orderByColumn.split(",");
            String isAsc = pageQuery.getIsAsc();
            isAscArray = StringUtils.isNotBlank(isAsc) ? isAsc.split(",") : new String[]{"asc"};

            // 转换字段名为数据库字段名
            for (int i = 0; i < orderByColumns.length; i++) {
                orderByColumns[i] = StringUtils.toUnderScoreCase(orderByColumns[i].trim());
            }
        }

        Page<ProjectResultVo> result = baseMapper.selectVoPageWithBusinessType(page, lqw,
                                                                               bo.getBusinessCategoryMajor(),
                                                                               bo.getBusinessCategoryMinor(),
                                                                               orderByColumn,
                                                                               orderByColumns,
                                                                               isAscArray);
        Map<String,String> nickNameMap = userService.selectAllUser2().stream().collect(Collectors.toMap(SysUser::getUserName, SysUser::getNickName,(v1,v2)->v1));
        if (result != null && CollUtil.isNotEmpty(result.getRecords())) {
            result.getRecords().forEach(vo -> {
               vo.setCreateBy(nickNameMap.get(vo.getCreateBy()));
            });
        }

        return TableDataInfo.build(result);
    }

    /**
     * 查询项目成果表列表
     */
    @Override
    public List<ProjectResultVo> queryList(ProjectResultBo bo) {
        LambdaQueryWrapper<ProjectResult> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoListWithBusinessType(lqw,
                                                       bo.getBusinessCategoryMajor(),
                                                       bo.getBusinessCategoryMinor());
    }

    private LambdaQueryWrapper<ProjectResult> buildQueryWrapper(ProjectResultBo bo) {
        LambdaQueryWrapper<ProjectResult> lqw = Wrappers.lambdaQuery();

        // 业务类型ID查询
        lqw.eq(bo.getBusinessTypeId() != null, ProjectResult::getBusinessTypeId, bo.getBusinessTypeId());
        // 成果编码查询
        lqw.like(StringUtils.isNotBlank(bo.getResultCode()), ProjectResult::getResultCode, bo.getResultCode());
        // 状态查询
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ProjectResult::getStatus, bo.getStatus());
        // 负责项目经理模糊查询
        lqw.like(StringUtils.isNotBlank(bo.getProjectManagers()), ProjectResult::getProjectManagers, bo.getProjectManagers());
        // 项目/任务名称模糊查询
        lqw.like(StringUtils.isNotBlank(bo.getProjectTaskName()), ProjectResult::getProjectTaskName, bo.getProjectTaskName());
        // 成果类型查询
        lqw.eq(StringUtils.isNotBlank(bo.getResultType()), ProjectResult::getResultType, bo.getResultType());
        // 优先级查询
        lqw.eq(StringUtils.isNotBlank(bo.getPriorityLevel()), ProjectResult::getPriorityLevel, bo.getPriorityLevel());
        // 创建时间查询
        lqw.between(bo.getCreateTimeStart() != null && bo.getCreateTimeEnd() != null,
                ProjectResult::getCreateTime, bo.getCreateTimeStart(), bo.getCreateTimeEnd());
        // 完成时间查询
        lqw.between(bo.getCompletionTimeStart() != null && bo.getCompletionTimeEnd() != null,
                ProjectResult::getCompletionTime, bo.getCompletionTimeStart(), bo.getCompletionTimeEnd());

        // 如果明确指定了归档标志，则按指定条件查询；否则默认只查询未归档的数据（archiveFlag != 1）
        if (bo.getArchiveFlag() != null) {
            lqw.eq(ProjectResult::getArchiveFlag, bo.getArchiveFlag());
        } else {
            // 默认不查询已归档的数据
            lqw.eq(ProjectResult::getArchiveFlag, 0);
        }

        return lqw;
    }

    /**
     * 新增项目成果表
     */
    @Override
    public Boolean insertByBo(ProjectResultBo bo) {
        // 处理前端传0值的字段，将其设置为null
        handleZeroToNull(bo);

        // 处理完成时间业务逻辑
        handleCompletionTime(bo);

        // 生成成果编码
        generateResultCode(bo);

        ProjectResult add = BeanUtil.toBean(bo, ProjectResult.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改项目成果表
     */
    @Override
    public Boolean updateByBo(ProjectResultBo bo) {
        // 处理前端传0值的字段，将其设置为null
        handleZeroToNull(bo);

        // 处理完成时间业务逻辑
        handleCompletionTime(bo);

        // 确保成果编码不被修改
        ensureResultCodeNotModified(bo);

        ProjectResult update = BeanUtil.toBean(bo, ProjectResult.class);
        validEntityBeforeSave(update);

        // 使用 LambdaUpdateWrapper 来确保 null 值也能被更新
        LambdaUpdateWrapper<ProjectResult> updateWrapper = new LambdaUpdateWrapper<ProjectResult>()
            .eq(ProjectResult::getId, update.getId())
            .set(ProjectResult::getBusinessTypeId, update.getBusinessTypeId())
            .set(ProjectResult::getResultType, update.getResultType())
            .set(ProjectResult::getProjectTaskName, update.getProjectTaskName())
            .set(ProjectResult::getPriorityLevel, update.getPriorityLevel())
            .set(ProjectResult::getStatus, update.getStatus())
            .set(ProjectResult::getMilestoneRequirements, update.getMilestoneRequirements())
            .set(ProjectResult::getMilestoneDevelopment, update.getMilestoneDevelopment())
            .set(ProjectResult::getMilestoneTest, update.getMilestoneTest())
            .set(ProjectResult::getMilestoneOnline, update.getMilestoneOnline())
            .set(ProjectResult::getRequirementsProgress, update.getRequirementsProgress())
            .set(ProjectResult::getDevelopmentProgress, update.getDevelopmentProgress())
            .set(ProjectResult::getTestProgress, update.getTestProgress())
            .set(ProjectResult::getDevTeams, update.getDevTeams())
            .set(ProjectResult::getTestTeams, update.getTestTeams())
            .set(ProjectResult::getProductManagers, update.getProductManagers())
            .set(ProjectResult::getDevManpower, update.getDevManpower())
            .set(ProjectResult::getTestManpower, update.getTestManpower())
            .set(ProjectResult::getDevWorkload, update.getDevWorkload())
            .set(ProjectResult::getTestWorkload, update.getTestWorkload())
            .set(ProjectResult::getRequirementBackground, update.getRequirementBackground())
            .set(ProjectResult::getProjectManagers, update.getProjectManagers())
            .set(ProjectResult::getCompletionTime, update.getCompletionTime())
            .set(ProjectResult::getArchiveFlag, update.getArchiveFlag());

        return baseMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProjectResult entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 处理前端传0值的字段，将其设置为null
     * 涉及字段：需求评审进度、开发进度、测试进度、投入人力、工作量
     *
     * @param bo 项目成果业务对象
     */
    private void handleZeroToNull(ProjectResultBo bo) {
        // 处理需求评审进度
        if (bo.getRequirementsProgress() != null && bo.getRequirementsProgress().compareTo(BigDecimal.ZERO) == 0) {
            bo.setRequirementsProgress(null);
        }

        // 处理开发进度
        if (bo.getDevelopmentProgress() != null && bo.getDevelopmentProgress().compareTo(BigDecimal.ZERO) == 0) {
            bo.setDevelopmentProgress(null);
        }

        // 处理测试进度
        if (bo.getTestProgress() != null && bo.getTestProgress().compareTo(BigDecimal.ZERO) == 0) {
            bo.setTestProgress(null);
        }

        // 处理开发投入人力
        if (bo.getDevManpower() != null && bo.getDevManpower().equals(0)) {
            bo.setDevManpower(null);
        }

        // 处理测试投入人力
        if (bo.getTestManpower() != null && bo.getTestManpower().equals(0)) {
            bo.setTestManpower(null);
        }

        // 处理开发工作量
        if (bo.getDevWorkload() != null && bo.getDevWorkload().compareTo(BigDecimal.ZERO) == 0) {
            bo.setDevWorkload(null);
        }

        // 处理测试工作量
        if (bo.getTestWorkload() != null && bo.getTestWorkload().compareTo(BigDecimal.ZERO) == 0) {
            bo.setTestWorkload(null);
        }
    }

    /**
     * 处理完成时间业务逻辑
     */
    private void handleCompletionTime(ProjectResultBo bo) {
        // 如果状态不是"已完成"(3)，则将完成时间置空
        if (!CommConstants.CommonValStr.THREE.equals(bo.getStatus())) {
            bo.setCompletionTime(null);
        } else {
            // 如果状态是"已完成"但完成时间为空，则抛出异常
            if (bo.getCompletionTime() == null) {
                throw new ServiceException("状态为已完成时，完成时间不能为空");
            }
        }
    }

    /**
     * 生成成果编码
     */
    private void generateResultCode(ProjectResultBo bo) {
        try {
            // 获取当前日期
            java.util.Date now = new java.util.Date();
            String dateStr = DateUtils.parseDateToStr("yyyyMMdd", now);

            // 获取下一个流水号
            int nextSerial = projectResultNumberService.getNextSerialNumber(dateStr);

            // 生成完整的成果编码
            String resultCode = projectResultNumberService.generateResultCode(dateStr, nextSerial);
            bo.setResultCode(resultCode);

            log.info("生成项目成果编码: {}, 日期: {}, 流水号: {}", resultCode, dateStr, nextSerial);
        } catch (Exception e) {
            throw new ServiceException("生成项目成果编码失败：" + e.getMessage());
        }
    }

    /**
     * 确保成果编码不被修改
     */
    private void ensureResultCodeNotModified(ProjectResultBo bo) {
        if (bo.getId() == null) {
            return;
        }

        // 查询原有记录
        ProjectResult existingRecord = baseMapper.selectById(bo.getId());
        if (existingRecord == null) {
            throw new ServiceException("项目成果不存在");
        }

        // 如果前端传递了成果编码，检查是否与原有记录一致
        if (StringUtils.isNotBlank(bo.getResultCode()) &&
            !bo.getResultCode().equals(existingRecord.getResultCode())) {
            throw new ServiceException("成果编码不允许修改");
        }

        // 确保使用原有的成果编码
        bo.setResultCode(existingRecord.getResultCode());
    }

    /**
     * 同步项目成果
     */
    @Override
    public Boolean sync(ProjectResultBo bo) {
        if(bo.getId() == null){
            throw new ServiceException("项目成果ID不能为空");
        }
        //获取项目成果关联的所有需求
        List<StoryResult> storyResults = storyResultMapper.selectList(new LambdaQueryWrapper<StoryResult>()
            .eq(StoryResult::getResultId, bo.getId()));
        if(CollectionUtils.isEmpty(storyResults)){
            log.warn(String.format("项目成果ID：%d 关联的需求不存在", bo.getId()));
            return true;
        }
        ProjectResult projectResult=BeanUtil.toBean(bo, ProjectResult.class);
        //同步禅道数据
        ProjectResult syncResultZtDate = SpringUtils.getAopProxy(this).syncResultZtDate(projectResult,storyResults.stream().map(StoryResult::getId).collect(Collectors.toList()));
        // 使用 LambdaUpdateWrapper 来确保 null 值也能被更新
        LambdaUpdateWrapper<ProjectResult> updateWrapper = new LambdaUpdateWrapper<ProjectResult>()
            .eq(ProjectResult::getId, bo.getId())
            .set(ProjectResult::getMilestoneRequirements, syncResultZtDate.getMilestoneRequirements())
            .set(ProjectResult::getMilestoneDevelopment, syncResultZtDate.getMilestoneDevelopment())
            .set(ProjectResult::getMilestoneTest, syncResultZtDate.getMilestoneTest())
            .set(ProjectResult::getMilestoneOnline, syncResultZtDate.getMilestoneOnline())
            .set(ProjectResult::getRequirementsProgress, syncResultZtDate.getRequirementsProgress())
            .set(ProjectResult::getDevelopmentProgress, syncResultZtDate.getDevelopmentProgress())
            .set(ProjectResult::getTestProgress, syncResultZtDate.getTestProgress())
            .set(ProjectResult::getDevTeams, syncResultZtDate.getDevTeams())
            .set(ProjectResult::getTestTeams, syncResultZtDate.getTestTeams())
            .set(ProjectResult::getProductManagers, syncResultZtDate.getProductManagers());

        return baseMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 批量删除项目成果表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }

        // 先清空关联需求的成果信息
        Boolean clearResult = storyResultService.clearResultInfoByResultIds(ids);
        if (!clearResult) {
            log.error("清空需求成果关联信息失败，成果ID：{}", ids);
            throw new ServiceException("清空需求成果关联信息失败");
        }

        // 再删除项目成果
        boolean deleteResult = baseMapper.deleteBatchIds(ids) > 0;
        if (!deleteResult) {
            log.error("删除项目成果失败，成果ID：{}", ids);
            throw new ServiceException("删除项目成果失败");
        }

        log.info("成功删除项目成果及其关联的需求信息，成果ID：{}", ids);
        return true;
    }

    /**
     * 归档项目成果
     */
    @Override
    public Boolean archiveById(Long id) {
        // 先查询当前记录
        ProjectResult projectResult = baseMapper.selectById(id);
        if (projectResult == null) {
            throw new ServiceException("项目成果不存在");
        }

        // 检查是否已经归档
        if (projectResult.getArchiveFlag() != null && projectResult.getArchiveFlag() == 1) {
            throw new ServiceException("该项目成果已经归档，无法重复归档");
        }

        // 更新归档标志
        ProjectResult update = new ProjectResult();
        update.setId(id);
        update.setArchiveFlag(1);

        return baseMapper.updateById(update) > 0;
    }


    @Override
    public Boolean removeArchive(Long id) {
        // 先查询当前记录
        ProjectResult projectResult = baseMapper.selectById(id);
        if (projectResult == null) {
            throw new ServiceException("项目成果不存在");
        }
        // 检查是否已经归档
        if (projectResult.getArchiveFlag() == null && projectResult.getArchiveFlag() == 0) {
            throw new ServiceException("该项目成果还未归档");
        }
        // 更新归档标志
        ProjectResult update = new ProjectResult();
        update.setId(id);
        update.setArchiveFlag(0);

        return baseMapper.updateById(update) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean generateResults(ProjectResultBo bo) {
        // 1. 校验需求列表不能为空
        if (CollectionUtils.isEmpty(bo.getStoryIdList())) {
            throw new ServiceException("需求不能为空");
        }
        List<StoryResult> storyResults = storyResultMapper.selectList(new LambdaQueryWrapper<StoryResult>()
            .in(StoryResult::getId, bo.getStoryIdList()));
        if (storyResults.stream().anyMatch(item -> item.getResultId() != null)) {
            throw new ServiceException("所选需求中存在已关联成果的需求");
        }
        // 2. 生成成果编码
        generateResultCode(bo);
        ProjectResult projectResult = BeanUtil.toBean(bo, ProjectResult.class);

        // 3. 同步禅道数据
        ProjectResult result = SpringUtils.getAopProxy(this).syncResultZtDate(projectResult,bo.getStoryIdList());
        // 4. 保存成果数据
        baseMapper.insert(result);
        //5.更新需求数据
        storyResultMapper.update(null,new LambdaUpdateWrapper<StoryResult>()
            .set(StoryResult::getResultId,projectResult.getId())
                .set(StoryResult::getResultCode,bo.getResultCode())
            .in(StoryResult::getId,bo.getStoryIdList()));

        return true;
    }

    @Override
    public Boolean joinResults(ProjectResultBo bo) {
        if (CollectionUtils.isEmpty(bo.getStoryIdList())) {
            throw new ServiceException("需求不能为空");
        }
        List<StoryResult> storyResults = storyResultMapper.selectList(new LambdaQueryWrapper<StoryResult>()
            .in(StoryResult::getId, bo.getStoryIdList()));
        if (storyResults.stream().anyMatch(item -> item.getResultId() != null)) {
            throw new ServiceException("所选需求中存在已关联成果的需求");
        }
        ProjectResult projectResult = baseMapper.selectById(bo.getId());
        if (projectResult == null) {
            throw new ServiceException("项目成果不存在");
        }
        storyResultMapper.update(null,new LambdaUpdateWrapper<StoryResult>()
            .set(StoryResult::getResultId,projectResult.getId())
            .set(StoryResult::getResultCode, projectResult.getResultCode())
            .in(StoryResult::getId,bo.getStoryIdList()));
        return true;
    }

    @Override
    public List<ProjectResultVo> listByDoingOrNotStart(ProjectResultBo bo) {
        List<ProjectResultVo> projectResultVos = baseMapper.listByDoingOrNotStart(bo);
        return projectResultVos;
    }

    @Override
    public Boolean cannelJoinResults(List<Long> storyIds) {
        storyResultMapper.clearResultInfoByStoryIds(storyIds);
        return true;
    }

    /**
     * 同步禅道数据
     * @param bo
     * @param storyIdList
     * @return
     */
    @DS(DataSource.ZENTAO)
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true,rollbackFor = Exception.class)
    public ProjectResult syncResultZtDate(ProjectResult bo, List<Long> storyIdList) {
        List<Story> storyList =  storyService.list(new LambdaQueryWrapper<Story>().in(Story::getId, storyIdList));
        if (CollectionUtils.isEmpty(storyList)) {
            throw new ServiceException("需求不存在");
        }
        //1.项目里程碑
        List<Integer> storyIds =  storyList.stream().map(Story::getId).collect(toList());
        //1.1完成评审时间
        List<ZtAction> requiereActions =  actionService.getByObjIdAndTypeAndAction(storyIds, "story", "reviewed", Collections.singletonList("Pass"));
        requiereActions.stream().min(Comparator.comparing(ZtAction::getDate))
            .ifPresent(action -> {
                bo.setMilestoneRequirements(action.getDate());
                bo.setRequirementsProgress(BigDecimal.valueOf(100));
            });
        //1.2完成开发时间
        List<ZtBuild> builds = buildService.selectByStoryIds(storyIds);
        if (CollUtil.isNotEmpty(builds)) {
            List<String> buildIds = builds.stream().map(build -> String.valueOf(build.getId())).collect(toList());
            List<ZtAction> bulidActions =  actionService.getByObjIdAndTypeAndAction(storyIds, "story", "linked2build", buildIds);
            bulidActions.stream().min(Comparator.comparing(ZtAction::getDate)).ifPresent(action -> {
                bo.setMilestoneDevelopment(action.getDate());
                bo.setDevelopmentProgress(BigDecimal.valueOf(100));
            });
        }

        LambdaQueryWrapper<ZtRelease> wrapper = new LambdaQueryWrapper<>();
        for (Integer storyId : storyIds) {
            wrapper.or(wq -> wq.apply("FIND_IN_SET({0}, stories)", storyId));
        }
        List<ZtRelease> ztReleases = ztReleaseMapper.selectList(wrapper);
        if (CollUtil.isNotEmpty(ztReleases)) {
            //1.3完成测试验收时间
            ztReleases.stream().min(Comparator.comparing(ZtRelease::getCreatedDate)).ifPresent(release -> {
                bo.setMilestoneTest(release.getCreatedDate());
                bo.setTestProgress(BigDecimal.valueOf(100));
            });
            //1.4完成上线时间
            ztReleases.stream().min(Comparator.comparing(ZtRelease::getCreatedDate))
                .ifPresent(release -> {
                    bo.setMilestoneOnline(release.getReleasedDate());
            });
        }

        //2.干系人
        //2.1 产品
        List<String> producterNames = storyList.stream().map(Story::getOpenedby).distinct().collect(toList());
        List<ZtUser> ztUsers = ztUserService.selectByAccounts(producterNames);
        String productManagers = ztUsers.stream().map(ZtUser::getRealname).distinct().collect(joining(","));
        bo.setProductManagers(productManagers);
        //2.2开发组
        List<String> devels = taskService.selectTaskPrincipal(storyIdList, 0, "devel");
        List<Long> devDeptIds = SpringUtils.getAopProxy(this).selectTaskGroup(devels);
        String devTeamsStr = devDeptIds.stream().map(String::valueOf).collect(Collectors.joining(","));
        bo.setDevTeams(devTeamsStr);
        //2.3测试组
        List<String> testers = taskService.selectTaskPrincipal(storyIdList, 0, "test");
        List<Long> testDeptIds = SpringUtils.getAopProxy(this).selectTaskGroup(testers);
        String testTeamsStr = testDeptIds.stream().map(String::valueOf).collect(Collectors.joining(","));
        bo.setTestTeams(testTeamsStr);
        //3.需求背景
        List<ZtStoryspec> storyspecs = storyspecService.selectByStoryId(storyIdList);
        bo.setRequirementBackground(storyspecs.stream().map(ZtStoryspec::getSpec).collect(joining("\n")));
        return bo;
    }

    @DS(DataSource.GITLAB)
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true,rollbackFor = Exception.class)
    public List<Long> selectTaskGroup(List<String> names) {
        List<SysUser> userList = sysUserService.selectByZtUserNames(names);
        return userList.stream().map(SysUser::getDeptId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }

    /**
     * 发送项目成果邮件
     * @param resultIds 项目成果ID列表
     * @return 是否成功
     */
    @Override
    public Boolean sendEmail(List<Long> resultIds) {
        if (CollectionUtils.isEmpty(resultIds)) {
            throw new ServiceException("项目成果ID不能为空");
        }
        // 查询项目成果信息
        List<ProjectResultVo> projectResults = baseMapper.selectVoByIds(resultIds);
        if (CollectionUtils.isEmpty(projectResults)) {
            throw new ServiceException("未找到指定的项目成果");
        }
        // 检查是否有完成上线时间为空的记录
        List<String> projectsWithNullMilestone = projectResults.stream()
            .filter(project -> project.getMilestoneOnline() == null)
            .map(ProjectResultVo::getProjectTaskName)
            .collect(Collectors.toList());
        if (!projectsWithNullMilestone.isEmpty()) {
            String errorMessage = "以下项目的完成上线时间为空,请重新选择项目成果: " + String.join(", ", projectsWithNullMilestone);
            throw new ServiceException(errorMessage);
        }
        Map<String, String> majorList = dictTypeService.selectDictDataByType("project_outcome_business_category_major")
            .stream().collect(toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        Map<String, String> minorList = dictTypeService.selectDictDataByType("project_outcome_business_category_minor")
            .stream().collect(toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        // 获取当前月份
        String currentMonth = DateUtils.parseDateToStr("MM", new Date());
        // 构建邮件内容
        String emailContent = buildEmailContent(projectResults, currentMonth, majorList, minorList);
        // 构建邮件主题
        String subject = "技术中心重点支持项目进展月报";
        // 生成Excel附件
        byte[] excelAttachment = generateExcelAttachment(projectResults, majorList, minorList);

        try {
            // 收件人邮箱
            List<String> toEmailList = dictTypeService.selectDictDataByType("project_outcome_email_to")
                .stream().map(SysDictData::getDictValue).filter(StringUtils::isNotBlank).collect(toList());
            // 抄送人邮箱
            List<String> ccEmailList =  dictTypeService.selectDictDataByType("project_outcome_email_cc")
                .stream().map(SysDictData::getDictValue).filter(StringUtils::isNotBlank).collect(toList());

            MimeMessage mimeMessage = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);
            helper.setFrom(fromEmail);
            helper.setTo(toEmailList.toArray(new String[0]));
            helper.setCc(ccEmailList.toArray(new String[0]));
            helper.setSubject(subject);
            helper.setText(emailContent, true);
            helper.addAttachment(
                "技术中心"+currentMonth+"月重点支持的项目进展月报.xlsx",
                new ByteArrayResource(excelAttachment)
            );
            // 发送邮件
            javaMailSender.send(mimeMessage);
            log.info("项目成果邮件发送成功，涉及{}个项目，主题: {}", projectResults.size(), subject);
            return true;
        } catch (MessagingException e) {
            log.error("项目成果邮件发送失败", e);
            throw new ServiceException("邮件发送失败：" + e.getMessage());
        }
    }

    @Override
    public List<ProjectResultVo> selectByIds(List<Long> ids) {
        return baseMapper.selectVoByIds(ids);
    }

    /**
     * 构建邮件内容
     * @param projectResults 项目成果列表
     * @param currentMonth 当前月份
     * @return HTML格式的邮件内容
     */
    private String buildEmailContent(List<ProjectResultVo> projectResults, String currentMonth,Map<String, String> majorList,Map<String, String> minorList) {
        StringBuilder htmlContent = new StringBuilder();
        // 邮件头部
        htmlContent.append("<html><head><style>")
            .append("th, td { text-align: center; }")
            .append("</style></head><body>");
        htmlContent.append("<h4>Dear</h4>");
        htmlContent.append("<h4>下午好,以下是").append(currentMonth).append("月技术中心重点支持的项目进展月报,整体无风险,请您查阅(也可查阅附件文件)。</h4>");
        // 项目成果表格
        htmlContent.append("<table border='1' cellpadding='5' cellspacing='0' style='border-collapse:collapse; width:100%;'>");
        // 表头
        htmlContent.append("<tr style='background-color:#f2f2f2;'>")
                .append("<th>序号</th>")
                .append("<th>项目/任务名称</th>")
                .append("<th>业务大类</th>")
                .append("<th>业务小类</th>")
                .append("<th>产品负责人</th>")
                .append("<th>业务负责人</th>")
                .append("<th>项目里程碑</th>")
                .append("</tr>");
        // 表格内容
        for (int i = 0; i < projectResults.size(); i++) {
            ProjectResultVo result = projectResults.get(i);
            htmlContent.append("<tr>")
                    .append("<td>").append(i + 1).append("</td>")
                    .append("<td>").append(result.getProjectTaskName() != null ? result.getProjectTaskName() : "").append("</td>")
                    .append("<td>").append(result.getBusinessCategoryMajor() != null ? majorList.getOrDefault(result.getBusinessCategoryMajor(),"") : "").append("</td>")
                    .append("<td>").append(result.getBusinessCategoryMinor() != null ? minorList.getOrDefault(result.getBusinessCategoryMinor(),"") : "").append("</td>")
                    .append("<td>").append(result.getProductManagers() != null ? result.getProductManagers() : "").append("</td>")
                    .append("<td>").append(result.getBusinessManager() != null ? result.getBusinessManager() : "").append("</td>")
                    .append("<td style='color: red;'>").append("完成上线:")
                    .append(result.getMilestoneOnline()!= null ? DateUtils.parseDateToStr("yyyy-MM-dd", result.getMilestoneOnline()) : "--").append("</td>")
                    .append("</tr>");
        }
        htmlContent.append("</table>");
        htmlContent.append("</body></html>");
        return htmlContent.toString();
    }

    /**
     * 生成Excel附件
     * @param projectResults 项目成果列表
     * @return Excel文件的字节数组
     */
    private byte[] generateExcelAttachment(List<ProjectResultVo> projectResults,Map<String, String> majorList,Map<String, String> minorList) {
        try {
            // 转换为导出VO
            List<ProjectResultEmailExportVo> exportList = new ArrayList<>();
            for (int i = 0; i < projectResults.size(); i++) {
                ProjectResultVo result = projectResults.get(i);
                ProjectResultEmailExportVo exportVo = new ProjectResultEmailExportVo();
                exportVo.setSerialNumber(i + 1);
                exportVo.setProjectTaskName(result.getProjectTaskName() != null ? result.getProjectTaskName() : "");
                exportVo.setBusinessCategoryMajor(result.getBusinessCategoryMajor() != null ? majorList.getOrDefault(result.getBusinessCategoryMajor(),"") : "");
                exportVo.setBusinessCategoryMinor(result.getBusinessCategoryMinor() != null ? minorList.getOrDefault(result.getBusinessCategoryMinor(),"") : "");
                exportVo.setProductManagers(result.getProductManagers() != null ? result.getProductManagers() : "");
                exportVo.setProjectManagers(result.getBusinessManager() != null ? result.getBusinessManager() : "");
                String milestoneOnlineTime = result.getMilestoneOnline() != null ? DateUtils.parseDateToStr("yyyy-MM-dd", result.getMilestoneOnline()) : "--";
                exportVo.setMilestone("完成上线:" + milestoneOnlineTime);
                exportList.add(exportVo);
            }
           // 使用EasyExcel生成Excel文件
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            EasyExcel.write(outputStream, ProjectResultEmailExportVo.class)
                    .sheet("项目进展")
                    .doWrite(exportList);
            return outputStream.toByteArray();
        } catch (Exception e) {
            log.error("生成Excel附件失败", e);
            throw new ServiceException("生成Excel附件失败：" + e.getMessage());
        }
    }


}

