package com.qmqb.imp.system.mapper.performance;

import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.bo.performance.PerformanceEventBo;
import com.qmqb.imp.system.domain.performance.PerformanceEvent;
import com.qmqb.imp.system.domain.vo.performance.PerformanceEventVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import com.qmqb.imp.system.domain.dto.PerformanceDistributionDTO;
import com.qmqb.imp.system.domain.dto.PerformanceEventStatusDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 绩效事件Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface PerformanceEventMapper extends BaseMapperPlus<PerformanceEventMapper, PerformanceEvent, PerformanceEventVo> {

    /**
     * 查询指定日期前缀的最大反馈编码
     * @param datePrefix 反馈编码前缀
     * @return 最大反馈编码
     */
    String getMaxMainFeedbackCodeByDatePrefix(@Param("datePrefix") String datePrefix);

    /**
     * 批量查询事件ID对应的绩效分布情况
     * @param eventIds 事件ID列表
     * @return 绩效分布DTO列表
     */
    List<PerformanceDistributionDTO> selectPerformanceDistributionByEventIds(@Param("eventIds") List<Long> eventIds);

    /**
     * 批量查询事件ID对应的状态（返回code）
     * @param eventIds 事件ID列表
     * @return 状态DTO列表
     */
    List<PerformanceEventStatusDTO> selectEventStatusByEventIds(@Param("eventIds") List<Long> eventIds);

    /**
     * 分页查询绩效事件列表
     * @param page
     * @param bo
     * @return
     */
    IPage<PerformanceEventVo> selectEventPageByCondition(IPage<?> page, @Param("queryBo") PerformanceEventBo bo);
}
