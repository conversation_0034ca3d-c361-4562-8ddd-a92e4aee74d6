<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.ReleaseRecordMapper">

    <resultMap type="com.qmqb.imp.system.domain.ReleaseRecord" id="ReleaseRecordResult">
        <result property="id" column="id"/>
        <result property="resultCode" column="result_code"/>
        <result property="resultType" column="result_type"/>
        <result property="dataSource" column="data_source"/>
        <result property="businessCategoryMajor" column="business_category_major"/>
        <result property="businessCategoryMinor" column="business_category_minor"/>
        <result property="durationDays" column="duration_days"/>
        <result property="manpowerCost" column="manpower_cost"/>
        <result property="devDepts" column="dev_depts"/>
        <result property="testDepts" column="test_depts"/>
        <result property="otherDepts" column="other_depts"/>
        <result property="resultTitle" column="result_title"/>
        <result property="resultSummary" column="result_summary"/>
        <result property="projectManager" column="project_manager"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="sourceReleaseId" column="source_release_id"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedTime" column="updated_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

</mapper> 