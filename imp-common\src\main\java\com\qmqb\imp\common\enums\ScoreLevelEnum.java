package com.qmqb.imp.common.enums;

import lombok.Getter;

/**
 * 绩效等级枚举。
 * <p>
 * 用于表示绩效考核的等级划分，包括S（优秀）、A（良好）、B（合格）、C（待改进）、D（不合格）。
 * 每个等级包含一个编码（code）和中文名称（name）。
 * </p>
 *
 * <ul>
 *   <li>{@link #SCORE_S} 优秀</li>
 *   <li>{@link #SCORE_A} 良好</li>
 *   <li>{@link #SCORE_B} 合格</li>
 *   <li>{@link #SCORE_C} 待改进</li>
 *   <li>{@link #SCORE_D} 不合格</li>
 * </ul>
 *
 * <AUTHOR>
 */
@Getter
public enum ScoreLevelEnum {
    /**
     * 优秀
     */
    SCORE_S("S", "优秀"),
    /**
     * 良好
     */
    SCORE_A("A", "良好"),
    /**
     * 合格
     */
    SCORE_B("B", "合格"),
    /**
     * 待改进
     */
    SCORE_C("C", "待改进"),
    /**
     * 不合格
     */
    SCORE_D("D", "不合格");

    /**
     * 等级编码
     */
    private final String code;
    /**
     * 等级名称
     */
    private final String name;

    /**
     * 构造方法
     *
     * @param code 等级编码
     * @param name 等级名称
     */
    ScoreLevelEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static ScoreLevelEnum getByCode(String code) {
        for (ScoreLevelEnum level : values()) {
            if (level.getCode().equals(code)) {
                return level;
            }
        }
        return null;
    }

    /**
     * 判断是否为有效等级
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidLevel(String code) {
        return getByCode(code) != null;
    }
}
