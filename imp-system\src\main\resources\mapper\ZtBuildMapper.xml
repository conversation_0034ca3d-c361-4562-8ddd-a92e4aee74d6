<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.ZtBuildMapper">

    <resultMap type="com.qmqb.imp.system.domain.ZtBuild" id="ZtBuildResult">
        <result property="id" column="id"/>
        <result property="project" column="project"/>
        <result property="product" column="product"/>
        <result property="branch" column="branch"/>
        <result property="execution" column="execution"/>
        <result property="builds" column="builds"/>
        <result property="name" column="name"/>
        <result property="system" column="system"/>
        <result property="scmPath" column="scmPath"/>
        <result property="filePath" column="filePath"/>
        <result property="date" column="date"/>
        <result property="stories" column="stories"/>
        <result property="bugs" column="bugs"/>
        <result property="artifactRepoId" column="artifactRepoID"/>
        <result property="builder" column="builder"/>
        <result property="desc" column="desc"/>
        <result property="createdBy" column="createdBy"/>
        <result property="createdDate" column="createdDate"/>
        <result property="deleted" column="deleted"/>
    </resultMap>


</mapper>
