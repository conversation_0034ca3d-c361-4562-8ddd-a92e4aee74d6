package com.qmqb.imp.app.service;

import com.qmqb.imp.app.domain.bo.DeptKeyIndicatorBO;
import com.qmqb.imp.app.domain.vo.KeyIndicatorVO;
import com.qmqb.imp.app.domain.vo.KeyValueVO;
import com.qmqb.imp.common.core.domain.R;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
public interface DeptKeyIndicatorService {

    /**
     * 组列表
     *
     * @return
     */
    R<List<KeyValueVO>> groupSelect();

    /**
     * 指标详情
     *
     * @param bo
     * @return
     */
    R<KeyIndicatorVO> detail(DeptKeyIndicatorBO bo);
}
