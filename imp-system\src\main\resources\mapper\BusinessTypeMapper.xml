<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.BusinessTypeMapper">

    <resultMap type="com.qmqb.imp.system.domain.BusinessType" id="BusinessTypeResult">
        <result property="id" column="id"/>
        <result property="businessTypeName" column="business_type_name"/>
        <result property="businessCategoryMajor" column="business_category_major"/>
        <result property="businessCategoryMinor" column="business_category_minor"/>
        <result property="sort" column="sort"/>
        <result property="businessManager" column="business_manager"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

</mapper>
