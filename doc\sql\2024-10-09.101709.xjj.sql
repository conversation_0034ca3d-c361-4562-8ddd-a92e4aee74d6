-- 备份
CREATE TABLE tb_work_stat_2024100901 LIKE gitlab_tool.tb_work_stat;
INSERT INTO backup.tb_work_stat_2024100901 SELECT * FROM gitlab_tool.tb_work_stat WHERE work_group = '新员工组';
INSERT INTO backup.tb_work_stat_2024100901 SELECT * FROM gitlab_tool.tb_work_stat WHERE work_year = 2024 AND work_month = 9 AND work_username IN ('文海龙', '方俊铭', '刘伟贤', '刘洁娜', '何祖辉');
INSERT INTO backup.tb_work_stat_2024100901 SELECT * FROM gitlab_tool.tb_work_stat WHERE work_group_id IS NULL AND work_year = 2024 AND work_group = 'Android开发组';
INSERT INTO backup.tb_work_stat_2024100901 SELECT * FROM gitlab_tool.tb_work_stat WHERE work_group_id IS NULL AND work_year = 2024 AND work_group = '助贷测试组';
INSERT INTO backup.tb_work_stat_2024100901 SELECT * FROM gitlab_tool.tb_work_stat WHERE work_group_id IS NULL AND work_year = 2024 AND work_group = '自营资产开发组';

-- 工作统计表修数，九月份新员工组数据
UPDATE tb_work_stat SET work_group_id = 1797445716298227713 WHERE work_group = '新员工组';
UPDATE tb_work_stat SET work_group_id = 1797445716298227713, work_group = '新员工组' WHERE work_year = 2024 AND work_month = 9 AND work_username IN ('文海龙', '方俊铭', '刘伟贤', '刘洁娜', '何祖辉');

-- 工作统计表修数，离职人员数据
UPDATE tb_work_stat SET work_group_id = 106 WHERE work_group_id IS NULL AND work_year = 2024 AND work_group = 'Android开发组';
UPDATE tb_work_stat SET work_group_id = 118, work_group = '助贷测试一组' WHERE work_group_id IS NULL AND work_year = 2024 AND work_group = '助贷测试组';
UPDATE tb_work_stat SET work_group_id = 117, work_group = '资产运营开发组' WHERE work_group_id IS NULL AND work_year = 2024 AND work_group = '自营资产开发组';
