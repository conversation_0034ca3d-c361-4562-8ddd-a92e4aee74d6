package com.qmqb.imp.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.core.domain.vo.WarnSelectedGroupVO;
import com.qmqb.imp.system.domain.bo.*;
import com.qmqb.imp.system.domain.vo.GroupAnalysisVo;
import com.qmqb.imp.system.domain.vo.PersonalAnalysisVo;
import com.qmqb.imp.system.domain.vo.DepartmentWarnAnalysisVo;
import com.qmqb.imp.system.domain.vo.WarningAllVO;

import java.util.List;

/**
 * 预警service
 *
 * <AUTHOR>
 */
public interface WarnAnalysisService {

    /**
     * 预警分析
     *
     * @param request
     * @return
     */
    DepartmentWarnAnalysisVo getDeptWarnAnalysisList(DeptWarnAnalysisBo request);

    /**
     * 部门下各组p0，p1，p2的预警情况
     *
     * @param request
     * @return
     */
    List<DepartmentWarnAnalysisVo.MultipleRecord> getMultipleRecord(DeptWarnAnalysisBo request);

    /**
     * 组内分析
     *
     * @param request
     * @return
     */
    GroupAnalysisVo getGroupAnalysisList(WarnAnalysisBo request);

    /**
     * 个人分析
     *
     * @param request
     * @return
     */
    PersonalAnalysisVo getPersonslAnalysisList(PersonalAnalysisBo request);


    /**
     * 个人预警记录
     * @param request
     * @param user
     * @return
     */
    Page<WarningAllVO> getWarnPersonalPage(WarningBaseBO request, LoginUser user);

    /**
     * 组内预警记录
     * @param request
     * @param user
     * @return
     */
    Page<WarningAllVO> getWarnGroupsPage(WarningBaseBO request,LoginUser user);

    /**
     * 全部预警记录
     * @param request
     * @return
     */
    Page<WarningAllVO> getWarnAllPage(WarningBaseAllBO request);

        /**
     * 获取个人预警记录
     * @param workDetail
     * @return
     */
    Page<WarningAllVO> getWarnAllPageByUser(WorkDetailBo workDetail);

    /**
     * 获取组别下拉框记录
     * @return
     */
    List<WarnSelectedGroupVO> getWarnGroupSelets();

    /**
     * 更新个人预警处理内容
     * @param contentBo
     */
    void updateHandleContent(WarnHandleContentBo contentBo);

    /**
     * 更新超过10天工作日没有处理的状态内容
     * @param voList
     */
    void updateOverDueHandleContent(List<WarningAllVO> voList);
}
