package com.qmqb.imp.app.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.secure.BCrypt;
import com.qmqb.imp.app.domain.bo.ChangePasswordBO;
import com.qmqb.imp.app.domain.bo.LoginBO;
import com.qmqb.imp.app.domain.vo.LoginInfoVO;
import com.qmqb.imp.app.domain.vo.LoginVO;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.SysLoginService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 我的
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mine")
public class MineController {

    private final SysLoginService loginService;
    private final ISysUserService userService;

    /**
     * 登录方法
     *
     * @param bo 登录信息
     * @return 结果
     */
    @SaIgnore
    @PostMapping("/login")
    public R<LoginVO> login(@Validated @RequestBody LoginBO bo) {
        // 生成令牌
        String token = loginService.appLogin(bo.getUsername(), bo.getPassword());
        return R.ok(LoginVO.builder().token(token).build());
    }


    /**
     * 退出登录
     */
    @SaIgnore
    @PostMapping("/logout")
    public R<Void> logout() {
        loginService.logout();
        return R.ok("退出成功");
    }

    /**
     * 获取用户信息
     * 角色编码（用于控制权限）
     * 超级管理员	admin
     * 技术经理	tm
     * 开发人员	developer
     * 测试人员	tester
     * 技术总监	cto
     * 项目经理	pm
     * 运维人员	operator
     *
     * @return 用户信息
     */
    @GetMapping("/get-info")
    public R<LoginInfoVO> getInfo() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        SysUser user = userService.selectUserById(loginUser.getUserId());
        return R.ok(LoginInfoVO.builder().user(user).build());
    }

    /**
     * 修改密码
     */
    @PostMapping("/change-password")
    public R<Void> changePassword(@Validated @RequestBody ChangePasswordBO bo) {
        SysUser user = userService.selectUserById(LoginHelper.getUserId());
        String userName = user.getUserName();
        String password = user.getPassword();
        if (!StringUtils.equals(bo.getNewPwd(), bo.getConfirmPwd())) {
            return R.fail("两次输入的密码不一致");
        }
        if (BCrypt.checkpw(bo.getConfirmPwd(), password)) {
            return R.fail("新密码不能与旧密码相同");
        }
        if (userService.resetUserPwd(userName, BCrypt.hashpw(bo.getConfirmPwd())) > 0) {
            return R.ok();
        }
        return R.fail("修改密码异常，请联系管理员");
    }
}
