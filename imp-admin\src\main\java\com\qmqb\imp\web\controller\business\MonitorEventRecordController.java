package com.qmqb.imp.web.controller.business;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;

import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.system.domain.vo.MonitorEventRecordVo;
import com.qmqb.imp.system.domain.bo.MonitorEventRecordBo;
import com.qmqb.imp.system.service.IMonitorEventRecordService;
import com.qmqb.imp.common.core.page.TableDataInfo;

/**
 * 监控预警记录
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/monitorEventRecord")
public class MonitorEventRecordController extends BaseController {

    private final IMonitorEventRecordService iMonitorEventRecordService;

    /**
     * 查询监控预警记录列表
     */
    @SaCheckPermission("system:monitorEventRecord:list")
    @GetMapping("/list")
    public TableDataInfo<MonitorEventRecordVo> list(MonitorEventRecordBo bo, PageQuery pageQuery) {
        return iMonitorEventRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取监控预警记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:monitorEventRecord:query")
    @GetMapping("/{id}")
    public R<MonitorEventRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iMonitorEventRecordService.queryById(id));
    }

    /**
     * 新增监控预警记录
     */
    @SaCheckPermission("system:monitorEventRecord:add")
    @SaIgnore
    @PostMapping()
    public R<Boolean> add(@Validated(AddGroup.class) @RequestBody MonitorEventRecordBo bo) {
        return R.ok(iMonitorEventRecordService.insertByBo(bo));
    }

    /**
     * 修改监控预警记录
     */
    @SaCheckPermission("system:monitorEventRecord:edit")
    @Log(title = "监控预警记录", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MonitorEventRecordBo bo) {
        return toAjax(iMonitorEventRecordService.updateByBo(bo));
    }

    /**
     * 处理监控预警记录,受理、去处理
     */
    @SaCheckPermission("system:monitorEventRecord:handle")
    @Log(title = "监控预警记录", businessType = BusinessType.UPDATE)
    @PostMapping("/handle")
    public R<Void> handle(@RequestBody MonitorEventRecordBo bo) {
        return toAjax(iMonitorEventRecordService.handle(bo));
    }
}
