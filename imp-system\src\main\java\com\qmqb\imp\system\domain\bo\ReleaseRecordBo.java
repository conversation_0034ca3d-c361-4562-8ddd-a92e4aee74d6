package com.qmqb.imp.system.domain.bo;

import com.qmqb.imp.common.core.domain.dto.BasePageDTO;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 发布版本记录业务对象 tb_release_records
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ReleaseRecordBo extends BasePageDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 成果编号
     */
    private String resultCode;

    /**
     * 成果类型：0全部、1新系统、2重要功能模块、3一般功能模块、4事项支撑、5其它
     */
    @NotBlank(message = "成果类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String resultType;

    /**
     * 数据来源：0全部、1禅道同步、2手工添加
     */
    private String dataSource;

    /**
     * 所属业务大类：0全部、1国内、2海外
     */
    @NotBlank(message = "所属业务大类不能为空", groups = {AddGroup.class, EditGroup.class})
    private String businessCategoryMajor;

    /**
     * 所属业务小类：0全部、1风控、2营销、3资金、4资产、5贷后、6自营业务、7综合、8其它
     */
    @NotBlank(message = "所属业务小类不能为空", groups = {AddGroup.class, EditGroup.class})
    private String businessCategoryMinor;

    /**
     * 耗时天数
     */
    @NotNull(message = "耗时天数不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer durationDays;

    /**
     * 耗费人力
     */
    @NotNull(message = "耗费人力不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer manpowerCost;

    /**
     * 主要开发组id,多个用逗号分隔
     */
    private String devDepts;

    /**
     * 测试组id,多个用逗号分隔
     */
    private String testDepts;

    /**
     * 其它组id,多个用逗号分隔
     */
    private String otherDepts;

    /**
     * 成果标题
     */
    @NotBlank(message = "成果标题不能为空", groups = {AddGroup.class, EditGroup.class})
    private String resultTitle;

    /**
     * 成果简介,取发布单关联的所有需求名称，多个需求则进行分行展示
     */
    @Length(max = 1500, message = "成果简介不能超过1500个字符", groups = {AddGroup.class, EditGroup.class})
    private String resultSummary;

    /**
     * 负责项目经理:all全部、lichonggao李崇高、zhengjingxia郑静霞、hujiaxin胡嘉鑫、huangjinyuan黄金媛
     */
    @NotBlank(message = "负责项目经理不能为空", groups = {AddGroup.class, EditGroup.class})
    private String projectManager;

    /**
     * 开始时间
     */
    @NotBlank(message = "开始时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private String startTime;

    /**
     * 结束时间
     */
    @NotBlank(message = "结束时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private String endTime;

    /**
     * 创建时间 开始区间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTimeStart;

    /**
     * 创建时间 结束区间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTimeEnd;

    /**
     * 创建时间 开始区间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTimeStart;

    /**
     * 创建时间 结束区间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTimeEnd;

    /**
     * 关联zt_release表ID
     */
    private Integer sourceReleaseId;

    /**
     * 创建人:all全部、lihaiyang李海洋、lichonggao李崇高、zhengjingxia郑静霞、hujiaxin胡嘉鑫、huangjinyuan黄金媛
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedTime;
}
