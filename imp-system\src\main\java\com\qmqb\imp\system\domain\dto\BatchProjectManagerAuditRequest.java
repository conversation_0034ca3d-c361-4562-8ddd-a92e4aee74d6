package com.qmqb.imp.system.domain.dto;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 批量最终审核请求对象
 * 用于批量处理最终审核状态的请求
 * 包含主键ID列表、审核状态和备注信息
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
public class BatchProjectManagerAuditRequest {
    @NotEmpty(message = "主键不能为空")
    private List<Long> ids;

    @NotBlank(message = "审核状态不能为空")
    private String status;

    private String remark;

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
