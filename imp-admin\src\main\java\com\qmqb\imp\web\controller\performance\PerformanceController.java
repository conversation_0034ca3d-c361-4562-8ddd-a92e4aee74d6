package com.qmqb.imp.web.controller.performance;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.bo.performance.PerformanceBo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceEmailBo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceLevelBo;
import com.qmqb.imp.system.domain.dto.PerformanceDTO;
import com.qmqb.imp.system.domain.vo.performance.PerformanceVo;
import com.qmqb.imp.system.service.indicator.IPerformanceService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 绩效点评主
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/performance")
public class PerformanceController extends BaseController {

    private final IPerformanceService iPerformanceService;

    /**
     * 查询绩效点评主列表
     */
    @SaCheckPermission("system:performance:list")
    @GetMapping("/list")
    public TableDataInfo<PerformanceDTO> list(PerformanceBo bo, PageQuery pageQuery) {
        return iPerformanceService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出绩效点评主列表
     */
    @SaCheckPermission("system:performance:export")
    @Log(title = "绩效点评主", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PerformanceBo bo, HttpServletResponse response) {
        List<PerformanceVo> list = iPerformanceService.queryList(bo);
        ExcelUtil.exportExcel(list, "绩效点评主", PerformanceVo.class, response);
    }

    /**
     * 获取绩效点评主详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:performance:query")
    @GetMapping("/{id}")
    public R<PerformanceVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long id) {
        return R.ok(iPerformanceService.queryById(id));
    }

    /**
     * 评审绩效
     *
     * @param performanceLevelBo
     */
    @SaCheckPermission("system:performance:review")
    @PostMapping("/review")
    public R<Boolean> review(@RequestBody PerformanceLevelBo performanceLevelBo) {
        return R.ok(iPerformanceService.reviewLevel(performanceLevelBo));
    }

    /**
     * 评审最终绩效
     *
     * @param performanceLevelBo
     */
    @SaCheckPermission("system:performance:approval")
    @PostMapping("/approval")
    public R<Boolean> approval(@RequestBody PerformanceLevelBo performanceLevelBo) {
        return R.ok(iPerformanceService.approvalLevel(performanceLevelBo));
    }


    /**
     * 发送邮件
     *
     * @param performanceEmailBo
     */
    @SaCheckPermission("system:performance:send")
    @PostMapping("/send")
    public R<Boolean> send(@RequestBody PerformanceEmailBo performanceEmailBo) {
        return R.ok(iPerformanceService.sendEmail(performanceEmailBo.getIds()));
    }
}
