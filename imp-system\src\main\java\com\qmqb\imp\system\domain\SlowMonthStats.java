package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 当月慢SQL统计对象 t_slow_month_stats
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@TableName("t_slow_month_stats")
public class SlowMonthStats implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * PK
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 月份
     */
    private String month;
    /**
     * 库名
     */
    @TableField("`DBName`")
    private String dbName;
    /**
     * SQLHash值
     */
    @TableField("`SQLHash`")
    private String sqlHash;
    /**
     * 指派人
     */
    private String assigner;
    /**
     * 指派时间
     */
    private Date assignTime;
    /**
     * 处理人
     */
    private String processer;
    /**
     * 处理时间
     */
    private LocalDateTime processTime;
    /**
     * 状态：0未指派、1已指派未处理、2已指派已处理、3无需处理
     */
    private String status;
    /**
     * 处理说明
     */
    private String remark;
    /**
     * ai分析结果
     */
    private String analysisResult;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 指派人id
     */
    private Long assignerId;
    /**
     * 处理人id
     */
    private Long processerId;

}
