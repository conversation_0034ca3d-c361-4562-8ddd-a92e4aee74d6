package com.qmqb.imp.app.service;

import com.qmqb.imp.app.domain.bo.DateBO;
import com.qmqb.imp.app.domain.bo.KeyIndicatorBO;
import com.qmqb.imp.app.domain.vo.KeyIndicatorVO;
import com.qmqb.imp.app.domain.vo.MessageInfoVO;
import com.qmqb.imp.app.domain.vo.PerformanceEvaluateBizVO;
import com.qmqb.imp.app.domain.vo.PerformanceReportVO;
import com.qmqb.imp.common.core.domain.R;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
public interface HomepageService {
    /**
     * 消息处理情况
     *
     * @return
     */
    R<MessageInfoVO> messageInfo();

    /**
     * 个人绩效报告
     *
     * @param bo
     * @return
     */
    R<PerformanceReportVO> personalPerformanceReport(DateBO bo, Long userId);

    /**
     * 组绩效报告
     *
     * @param bo
     * @return
     */
    R<PerformanceReportVO> teamPerformanceReport(DateBO bo);

    /**
     * 最新绩效点评
     *
     * @return
     */
    R<PerformanceEvaluateBizVO> performanceEvaluate();

    /**
     * 关键指标
     *
     * @param bo
     * @return
     */
    R<KeyIndicatorVO> keyIndicator(KeyIndicatorBO bo, Long deptId);
}
