package com.qmqb.imp.system.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONUtil;

import com.alibaba.fastjson.JSON;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.config.ZentaoConfig;
import com.qmqb.imp.common.constant.CacheConstants;
import com.qmqb.imp.common.utils.redis.RedisUtils;
import com.qmqb.imp.system.domain.dto.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 禅道API服务
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ZentaoApiService {

    private final ZentaoConfig zentaoConfig;
    private final Integer MAX_RETRY_COUNT = 3;

    /**
     * 用于Token刷新的同步锁，避免并发问题
     */
    private final Object tokenRefreshLock = new Object();

    /**
     * 获取禅道访问Token（智能缓存版本）
     */
    public String getTokenFromCache() {
        try {
            // 先尝试从Redis获取缓存的Token
            String cachedToken = RedisUtils.getCacheObject(CacheConstants.ZENTAO_TOKEN_KEY);
            if (StrUtil.isNotBlank(cachedToken)) {
                log.info("从缓存中获取到禅道Token");
                return cachedToken;
            }

            // 缓存中没有Token，使用同步锁防止并发刷新
            synchronized (tokenRefreshLock) {
                // 双重检查：可能在等待锁的过程中其他线程已经刷新了Token
                cachedToken = RedisUtils.getCacheObject(CacheConstants.ZENTAO_TOKEN_KEY);
                if (StrUtil.isNotBlank(cachedToken)) {
                    log.info("在同步块中获取到禅道Token（其他线程已刷新）");
                    return cachedToken;
                }

                // 缓存中确实没有Token，重新获取
                log.info("缓存中没有禅道Token，重新获取");
                return refreshTokenInternal();
            }
        } catch (Exception e) {
            log.error("获取禅道Token失败", e);
            throw new ServiceException("获取禅道Token失败: " + e.getMessage());
        }
    }

    /**
     * 内部Token刷新实现
     */
    private String refreshTokenInternal() {
        if (StrUtil.isBlank(zentaoConfig.getUrl())) {
            throw new ServiceException("禅道URL配置不能为空");
        }
        if (StrUtil.isBlank(zentaoConfig.getUsername())) {
            throw new ServiceException("禅道用户名配置不能为空");
        }
        if (StrUtil.isBlank(zentaoConfig.getPassword())) {
            throw new ServiceException("禅道密码配置不能为空");
        }
        if (zentaoConfig.getExecutionId() == null || zentaoConfig.getExecutionId() <= 0) {
            throw new ServiceException("禅道执行ID配置不能为空且必须大于0");
        }

        try {
            log.info("开始刷新禅道Token");
            String url = zentaoConfig.getUrl() + "/api.php/v1/tokens";
            ZentaoTokenRequest request = new ZentaoTokenRequest(zentaoConfig.getUsername(), zentaoConfig.getPassword());

            HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", "application/json")
                .body(JSONUtil.toJsonStr(request))
                .timeout(10000)
                .execute();

            if (response.isOk()) {
                ZentaoTokenResponse tokenResponse = JSONUtil.toBean(response.body(), ZentaoTokenResponse.class);
                String newToken = tokenResponse.getToken();

                if (StrUtil.isNotBlank(newToken)) {
                    // 将新Token缓存到Redis，设置过期时间为1.5小时
                    RedisUtils.setCacheObject(CacheConstants.ZENTAO_TOKEN_KEY, newToken,
                        Duration.ofSeconds(CacheConstants.ZENTAO_TOKEN_TTL));
                    log.info("禅道Token刷新成功并已缓存，有效期{}秒", CacheConstants.ZENTAO_TOKEN_TTL);
                    return newToken;
                } else {
                    throw new ServiceException("获取到的禅道Token为空");
                }
            } else {
                String errorMsg = String.format("刷新禅道Token失败，状态码：%d，响应：%s",
                    response.getStatus(), JSON.parse(response.body()).toString());
                log.error(errorMsg);
                throw new ServiceException(errorMsg);
            }
        } catch (Exception e) {
            log.error("刷新禅道Token异常", e);
            throw new ServiceException("刷新禅道Token失败: " + e.getMessage());
        }
    }

    /**
     * 清除缓存的Token
     */
    private void clearTokenCache() {
        synchronized (tokenRefreshLock) {
            RedisUtils.deleteObject(CacheConstants.ZENTAO_TOKEN_KEY);
            log.info("已清除禅道Token缓存");
        }
    }

    /**
     * 创建禅道任务（带重试机制）
     */
    public ZentaoTaskResponse createTask(String taskName, String assignedTo, String description) {
        return createTaskWithRetry(taskName, assignedTo, description, 0);
    }

    /**
     * 创建禅道任务的内部实现（支持重试）
     */
    private ZentaoTaskResponse createTaskWithRetry(String taskName, String assignedTo, String description, int retryCount) {
        if (StrUtil.isBlank(taskName)) {
            throw new ServiceException("任务名称不能为空");
        }
        if (StrUtil.isBlank(assignedTo)) {
            throw new ServiceException("指派人不能为空");
        }
        if (StrUtil.isBlank(zentaoConfig.getUrl())) {
            throw new ServiceException("禅道URL配置不能为空");
        }
        if (StrUtil.isBlank(zentaoConfig.getUsername())) {
            throw new ServiceException("禅道用户名配置不能为空");
        }
        if (StrUtil.isBlank(zentaoConfig.getPassword())) {
            throw new ServiceException("禅道密码配置不能为空");
        }
        if (zentaoConfig.getExecutionId() == null || zentaoConfig.getExecutionId() <= 0) {
            throw new ServiceException("禅道执行ID配置不能为空且必须大于0");
        }
        try {
            String token = getTokenFromCache();
            ZentaoTaskRequest taskRequest = buildTaskRequest(taskName, assignedTo, description);

            log.info("准备创建禅道任务，token: {}, 重试次数：{}，任务名称：{}",token , retryCount, taskName);

            HttpResponse response = executeTaskCreation(token, taskRequest);

            if (response.isOk()) {
                ZentaoTaskResponse taskResponse = JSONUtil.toBean(response.body(), ZentaoTaskResponse.class);
                log.info("禅道任务创建成功，任务ID：{}，重试次数：{}", taskResponse.getId(), retryCount);
                return taskResponse;
            } else {
                return handleErrorResponse(response, taskName, assignedTo, description, retryCount);
            }
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            return handleGeneralException(e, taskName, assignedTo, description, retryCount);
        }
    }

    /**
     * 构建任务请求对象
     */
    private ZentaoTaskRequest buildTaskRequest(String taskName, String assignedTo, String description) {
        ZentaoTaskRequest taskRequest = new ZentaoTaskRequest();
        taskRequest.setName(taskName);
        taskRequest.setType("devel");
        taskRequest.setAssignedTo(assignedTo);
        taskRequest.setPri(3);
        taskRequest.setEstStarted(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        taskRequest.setDeadline("");
        taskRequest.setDesc(description);
        taskRequest.setEstimate(0.0f);
        return taskRequest;
    }

    /**
     * 执行任务创建请求
     */
    private HttpResponse executeTaskCreation(String token, ZentaoTaskRequest taskRequest) {
        String url = zentaoConfig.getUrl() + "/api.php/v1/executions/" + zentaoConfig.getExecutionId() + "/tasks";

        return HttpRequest.post(url)
            .header("Content-Type", "application/json")
            .header("Token", token)
            .body(JSONUtil.toJsonStr(taskRequest))
            .timeout(15000)
            .execute();
    }

    /**
     * 处理错误响应
     */
    private ZentaoTaskResponse handleErrorResponse(HttpResponse response, String taskName, String assignedTo,
                                                  String description, int retryCount) {
        int statusCode = response.getStatus();
        String responseBody = response.body();
        log.error("禅道API调用失败，状态码：{}，响应：{}，重试次数：{}", statusCode, responseBody, retryCount);

        // 判断是否需要重试
        boolean flag=(statusCode == HttpStatus.HTTP_UNAUTHORIZED)
            && retryCount < MAX_RETRY_COUNT;
        if (flag) {
            return performRetry(taskName, assignedTo, description, retryCount, statusCode);
        } else {
            String errorMsg = String.format("创建禅道任务失败，状态码：%d，响应：%s，已重试%d次",
                statusCode, responseBody, retryCount);
            log.error(errorMsg);

            if (statusCode == HttpStatus.HTTP_UNAUTHORIZED) {
                // TODO 禅道api抽风，同个token偶发性出现401，然后同个token下次请求又可以了，现在暂时让用户重试来解决。
                throw new ServiceException("请重试");
            } else {
                throw new ServiceException("创建禅道任务失败，状态码:" + statusCode + "，错误信息:" + responseBody + "，已重试" + retryCount + "次");
            }
        }
    }

    /**
     * 处理一般异常
     */
    private ZentaoTaskResponse handleGeneralException(Exception e, String taskName, String assignedTo,
                                                     String description, int retryCount) {
        log.error("创建禅道任务发生异常，重试次数：{}", retryCount, e);

        if (retryCount < MAX_RETRY_COUNT) {
            int sleepTime = (retryCount + 1) * 1000;
            try {
                Thread.sleep(sleepTime);
            } catch (InterruptedException exception) {
                Thread.currentThread().interrupt();
                throw new ServiceException("重试等待被中断");
            }
            log.info("发生异常后开始第{}次重试创建禅道任务", retryCount + 1);
            return createTaskWithRetry(taskName, assignedTo, description, retryCount + 1);
        } else {
            throw new ServiceException("创建禅道任务异常，已重试" + retryCount + "次：" + e.getMessage());
        }
    }

    /**
     * 执行重试
     */
    private ZentaoTaskResponse performRetry(String taskName, String assignedTo, String description,
                                           int retryCount, int statusCode) {
        // 如果是401认证错误，清除Token缓存
        if (statusCode == HttpStatus.HTTP_UNAUTHORIZED) {
            log.error("收到401认证错误，清除Token缓存后重试");
            clearTokenCache();
        }
        int sleepTime = (retryCount + 1) * 1000;
        try {
            Thread.sleep(sleepTime);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new ServiceException("重试等待被中断");
        }
        log.info("开始第{}次重试创建禅道任务", retryCount + 1);
        return createTaskWithRetry(taskName, assignedTo, description, retryCount + 1);
    }
}
