package com.qmqb.imp.test;

import cn.dev33.satoken.secure.BCrypt;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 用户测试类
 *
 * <AUTHOR>
 * @since 2024-08-02
 */
@Slf4j
@SpringBootTest
public class UserTest {

    @Test
    public void testUser() {
        String password = "admin123";
        String password1 = BCrypt.hashpw("admin123");
        log.info(password1);
        String password2 = BCrypt.hashpw("admin123");
        log.info(password2);
        log.info(String.valueOf(BCrypt.checkpw(password, password1)));
        log.info(String.valueOf(BCrypt.checkpw(password, password2)));
    }
}
