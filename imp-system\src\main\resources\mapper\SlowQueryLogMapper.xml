<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.SlowQueryLogMapper">


    <select id="listSqlTextBySqlHash" resultType="com.qmqb.imp.system.domain.SlowQueryStats">
        select * from t_slow_query_stats
        where
        SQLHash in
        <foreach collection="sqlHashList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY SQLHash
    </select>

    <select id="slowSqlDetailPage" resultType="com.qmqb.imp.system.domain.vo.SlowQueryDetailVO">
        select id,
               ExecutionStartTime executionStartTime,
               ParseRowCounts     parseRowCounts,
               ReturnRowCounts    returnRowCounts,
               QueryTimeMS        queryTimeMs,
               SQLHash            sqlHash,
               SQLText            sqlText
        from t_slow_query_log
        where SQLHash = #{sqlHash} and ExecutionStartTime >= NOW() - INTERVAL 7 DAY
    </select>



</mapper>
