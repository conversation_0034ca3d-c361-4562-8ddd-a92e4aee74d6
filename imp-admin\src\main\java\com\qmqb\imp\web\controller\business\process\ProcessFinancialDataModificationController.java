package com.qmqb.imp.web.controller.business.process;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.system.domain.bo.process.ProcessFinancialDataModificationBo;
import com.qmqb.imp.system.domain.vo.process.ProcessFinancialDataModificationVo;
import com.qmqb.imp.system.service.process.IProcessFinancialDataModificationService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 催收财务运营生产数据修复
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/processFinancialDataModification")
public class ProcessFinancialDataModificationController extends BaseController {

    private final IProcessFinancialDataModificationService iProcessFinancialDataModificationService;

    /**
     * 查询催收财务运营生产数据修复列表
     */
    @SaCheckPermission("system:processFinancialDataModification:list")
    @GetMapping("/list")
    public TableDataInfo<ProcessFinancialDataModificationVo> list(ProcessFinancialDataModificationBo bo, PageQuery pageQuery) {
        return iProcessFinancialDataModificationService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取催收财务运营生产数据修复详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:processFinancialDataModification:query")
    @GetMapping("/{id}")
    public R<ProcessFinancialDataModificationVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iProcessFinancialDataModificationService.queryById(id));
    }

    /**
     * 新增催收财务运营生产数据修复
     */
    @SaCheckPermission("system:processFinancialDataModification:add")
    @Log(title = "催收财务运营生产数据修复", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProcessFinancialDataModificationBo bo) {
        return toAjax(iProcessFinancialDataModificationService.insertByBo(bo));
    }

    /**
     * 修改催收财务运营生产数据修复
     */
    @SaCheckPermission("system:processFinancialDataModification:edit")
    @Log(title = "催收财务运营生产数据修复", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProcessFinancialDataModificationBo bo) {
        return toAjax(iProcessFinancialDataModificationService.updateByBo(bo));
    }

    /**
     * 删除催收财务运营生产数据修复
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:processFinancialDataModification:remove")
    @Log(title = "催收财务运营生产数据修复", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iProcessFinancialDataModificationService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
