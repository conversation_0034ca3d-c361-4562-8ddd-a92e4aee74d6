package com.qmqb.imp.web.controller.business;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.vo.MonitorEventDeptVo;
import com.qmqb.imp.system.domain.bo.MonitorEventDeptBo;
import com.qmqb.imp.system.service.IMonitorEventDeptService;
import com.qmqb.imp.common.core.page.TableDataInfo;

/**
 * 预警事件和部门关联
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/monitorEventDept")
public class MonitorEventDeptController extends BaseController {

    private final IMonitorEventDeptService iMonitorEventDeptService;

    /**
     * 查询预警事件和部门关联列表
     */
    @SaCheckPermission("system:monitorEventDept:list")
    @GetMapping("/list")
    public TableDataInfo<MonitorEventDeptVo> list(MonitorEventDeptBo bo, PageQuery pageQuery) {
        return iMonitorEventDeptService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出预警事件和部门关联列表
     */
    @SaCheckPermission("system:monitorEventDept:export")
    @Log(title = "预警事件和部门关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MonitorEventDeptBo bo, HttpServletResponse response) {
        List<MonitorEventDeptVo> list = iMonitorEventDeptService.queryList(bo);
        ExcelUtil.exportExcel(list, "预警事件和部门关联", MonitorEventDeptVo.class, response);
    }

    /**
     * 获取预警事件和部门关联详细信息
     *
     * @param eventId 主键
     */
    @SaCheckPermission("system:monitorEventDept:query")
    @GetMapping("/{eventId}")
    public R<MonitorEventDeptVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long eventId) {
        return R.ok(iMonitorEventDeptService.queryById(eventId));
    }

    /**
     * 新增预警事件和部门关联
     */
    @SaCheckPermission("system:monitorEventDept:add")
    @Log(title = "预警事件和部门关联", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MonitorEventDeptBo bo) {
        return toAjax(iMonitorEventDeptService.insertByBo(bo));
    }

    /**
     * 修改预警事件和部门关联
     */
    @SaCheckPermission("system:monitorEventDept:edit")
    @Log(title = "预警事件和部门关联", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MonitorEventDeptBo bo) {
        return toAjax(iMonitorEventDeptService.updateByBo(bo));
    }

    /**
     * 删除预警事件和部门关联
     *
     * @param eventIds 主键串
     */
    @SaCheckPermission("system:monitorEventDept:remove")
    @Log(title = "预警事件和部门关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{eventIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] eventIds) {
        return toAjax(iMonitorEventDeptService.deleteWithValidByIds(Arrays.asList(eventIds), true));
    }
}
