package com.qmqb.imp.job.indicator.indicators.impl;

import com.qmqb.imp.common.core.domain.entity.SysRole;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.PersonTypeEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.job.indicator.GroupIndicatorCalculateManager;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-07-10 10:45
 * 功能交付时效情况
 */
@Service
public class DeliveryTimelinessIndicatorServiceImpl implements IndicatorLevelCalcService {

    @Autowired
    ISysUserService userService;
    @Autowired
    GroupIndicatorCalculateManager groupIndicatorCalculateManager;

    @Override
    public String getIndicatorCode() {
        return PerformanceIndicatorEnum.DELIVERY_TIMELINESS.getCode();
    }

    @Override
    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults) {
        SysUser manager = userService.selectUserByNickName(nickName);
        String roleName = manager.getRoles().stream().findFirst().map(SysRole::getRoleName).orElse(null);
        if (PersonTypeEnum.TECHNICAL_MANAGER.getDesc().equals(roleName)) {
            return groupIndicatorCalculateManager.caculateIndicator(workResult.getWorkYear(), workResult.getWorkMonth(),
                PerformanceIndicatorEnum.DELIVERY_TIMELINESS.getCode(),manager);
        }
        return ScoreLevelEnum.SCORE_B.getCode();
    }

    @Override
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level) {
        SysUser sysUser = userService.selectUserByNickName(nickName);
        String roleName = sysUser.getRoles().stream().findFirst().map(SysRole::getRoleName).orElse(null);
        if (PersonTypeEnum.TECHNICAL_MANAGER.getDesc().equals(roleName)) {
            // 获取计算数据
            GroupIndicatorCalculateManager.CaculateDate caculateDate = groupIndicatorCalculateManager.setCaculateDate(
                workResult.getWorkYear(),
                workResult.getWorkMonth(),
                PerformanceIndicatorEnum.DELIVERY_TIMELINESS.getCode(),
                sysUser);
            // 生成日志内容
            StringBuilder logContent = new StringBuilder();
            logContent.append(String.format("[%s]组在%s年%s月份功能交付时效情况：",
                nickName, workResult.getWorkYear(), workResult.getWorkMonth()));

            // 添加团队规模信息
            logContent.append(String.format("团队人数=%d，", caculateDate.getTeamMembers().size()));

            // 添加组员等级分布信息
            Map<String, Long> levelCounts = caculateDate.getLevelCountMap();
            logContent.append("组员等级分布[");
            levelCounts.forEach((k, v) -> logContent.append(String.format("%s=%d ", k, v)));
            logContent.append("]");

            // 添加评级和原因
            logContent.append(String.format("，评级：%s", level));
            String reason = groupIndicatorCalculateManager.getRatingReason(caculateDate, level);
            if (StringUtils.isNotEmpty(reason)) {
                logContent.append(String.format("，原因：%s", reason));
            }
            return logContent.toString();
        }
        return null;
    }
}
