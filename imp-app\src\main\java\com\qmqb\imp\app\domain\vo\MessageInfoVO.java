package com.qmqb.imp.app.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 消息处理情况视图对象
 *
 * <AUTHOR> Li
 */

@Data
@Builder
public class MessageInfoVO {

    /**
     * 个人未处理消息
     */
    private Long personal;
    /**
     * 团队未处理消息
     */
    private Long group;
    /**
     * P0预警
     */
    private Count p0;
    /**
     * P1预警
     */
    private Count p1;
    /**
     * P2预警
     */
    private Count p2;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Count {
        /**
         * 本人
         */
        private Long self = 0L;
        /**
         * 本组
         */
        private Long group = 0L;
    }
}
