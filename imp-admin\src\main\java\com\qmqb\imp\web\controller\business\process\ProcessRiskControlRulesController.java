package com.qmqb.imp.web.controller.business.process;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.bo.process.ProcessRiskControlRulesBo;
import com.qmqb.imp.system.domain.vo.process.ProcessRiskControlRulesVo;
import com.qmqb.imp.system.service.process.IProcessRiskControlRulesService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 风控规则开发需求
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/processRiskControlRules")
public class ProcessRiskControlRulesController extends BaseController {

    private final IProcessRiskControlRulesService iProcessRiskControlRulesService;

    /**
     * 查询风控规则开发需求列表
     */
    @SaCheckPermission("system:processRiskControlRules:list")
    @GetMapping("/list")
    public TableDataInfo<ProcessRiskControlRulesVo> list(ProcessRiskControlRulesBo bo, PageQuery pageQuery) {
        return iProcessRiskControlRulesService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出风控规则开发需求列表
     */
    @SaCheckPermission("system:processRiskControlRules:export")
    @Log(title = "风控规则开发需求", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProcessRiskControlRulesBo bo, HttpServletResponse response) {
        List<ProcessRiskControlRulesVo> list = iProcessRiskControlRulesService.queryList(bo);
        ExcelUtil.exportExcel(list, "风控规则开发需求", ProcessRiskControlRulesVo.class, response);
    }

    /**
     * 获取风控规则开发需求详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:processRiskControlRules:query")
    @GetMapping("/{id}")
    public R<ProcessRiskControlRulesVo> getInfo(@NotNull(message = "主键不能为空")
                                                @PathVariable Long id) {
        return R.ok(iProcessRiskControlRulesService.queryById(id));
    }

    /**
     * 新增风控规则开发需求
     */
    @SaCheckPermission("system:processRiskControlRules:add")
    @Log(title = "风控规则开发需求", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProcessRiskControlRulesBo bo) {
        return toAjax(iProcessRiskControlRulesService.insertByBo(bo));
    }

    /**
     * 修改风控规则开发需求
     */
    @SaCheckPermission("system:processRiskControlRules:edit")
    @Log(title = "风控规则开发需求", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProcessRiskControlRulesBo bo) {
        return toAjax(iProcessRiskControlRulesService.updateByBo(bo));
    }

    /**
     * 删除风控规则开发需求
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:processRiskControlRules:remove")
    @Log(title = "风控规则开发需求", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iProcessRiskControlRulesService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
