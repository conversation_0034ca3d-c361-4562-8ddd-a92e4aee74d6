package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.system.domain.ReleaseRecord;
import com.qmqb.imp.system.domain.bo.ReleaseRecordBo;
import com.qmqb.imp.system.domain.vo.ReleaseRecordVo;
import com.qmqb.imp.system.mapper.ReleaseRecordMapper;
import com.qmqb.imp.system.service.IReleaseRecordService;
import com.qmqb.imp.system.service.ISysDeptService;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.common.core.domain.entity.SysDept;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.qmqb.imp.system.service.IReleaseRecordNumberService;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;
import java.util.stream.Collectors;
import java.util.function.Function;

/**
 * 发布版本记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ReleaseRecordServiceImpl implements IReleaseRecordService {

    private final ReleaseRecordMapper baseMapper;
    private final ISysDeptService sysDeptService;
    private final ISysUserService sysUserService;
    private final IReleaseRecordNumberService releaseRecordNumberService;

    /**
     * 查询发布版本记录
     */
    @Override
    public ReleaseRecordVo queryById(Long id) {
        ReleaseRecordVo vo = baseMapper.selectVoById(id);
        if (vo != null) {
            convertDeptIdsToNames(vo);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String startTime = "";
            String endTime = "";
            if (vo.getStartTime() != null) {
                startTime = sdf.format(vo.getStartTime());
            }
            if (vo.getEndTime() != null) {
                endTime = sdf.format(vo.getEndTime());
            }
            vo.setStartToEndTime(startTime + " - " + endTime);
        }
        return vo;
    }

    /**
     * 查询发布版本记录列表
     */
    @Override
    public TableDataInfo<ReleaseRecordVo> queryPageList(ReleaseRecordBo bo, PageQuery pageQuery) {
        if (StringUtils.isBlank(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("created_time");
        }
        if (StringUtils.isBlank(pageQuery.getIsAsc())) {
            pageQuery.setIsAsc("desc");
        }

        LambdaQueryWrapper<ReleaseRecord> lqw = buildQueryWrapper(bo);
        Page<ReleaseRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        if (result.getRecords() != null && !result.getRecords().isEmpty()) {
            // 批量转换部门ID为部门名称
            batchConvertDeptIdsToNames(result.getRecords());
            //开始时间-结束时间处理
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            result.getRecords().forEach(item -> {
                String startTime = "";
                String endTime = "";
                if (item.getStartTime() != null) {
                    startTime = sdf.format(item.getStartTime());
                }
                if (item.getEndTime() != null) {
                    endTime = sdf.format(item.getEndTime());
                }
                item.setStartToEndTime(startTime + " - " + endTime);
            });
        }

        return TableDataInfo.build(result);
    }

    /**
     * 查询发布版本记录列表
     */
    @Override
    public List<ReleaseRecordVo> queryList(ReleaseRecordBo bo) {
        LambdaQueryWrapper<ReleaseRecord> lqw = buildQueryWrapper(bo);
        List<ReleaseRecordVo> list = baseMapper.selectVoList(lqw);

        if (list != null && !list.isEmpty()) {
             // 批量转换部门ID为部门名称
            batchConvertDeptIdsToNames(list);
            //开始时间-结束时间处理
            list.forEach(item -> {
                item.setStartToEndTime(item.getStartTime() + " - " + item.getEndTime());
            });
        }

        return list;
    }

    private LambdaQueryWrapper<ReleaseRecord> buildQueryWrapper(ReleaseRecordBo bo) {
        LambdaQueryWrapper<ReleaseRecord> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getResultCode()), ReleaseRecord::getResultCode, bo.getResultCode());
        // 成果类型：0全部时不添加查询条件
        lqw.eq(StringUtils.isNotBlank(bo.getResultType()) && !"0".equals(bo.getResultType()),
               ReleaseRecord::getResultType, bo.getResultType());
        // 数据来源：0全部时不添加查询条件
        lqw.eq(StringUtils.isNotBlank(bo.getDataSource()) && !"0".equals(bo.getDataSource()),
               ReleaseRecord::getDataSource, bo.getDataSource());
        // 所属业务大类：0全部时不添加查询条件
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessCategoryMajor()) && !"0".equals(bo.getBusinessCategoryMajor()),
               ReleaseRecord::getBusinessCategoryMajor, bo.getBusinessCategoryMajor());
        // 所属业务小类：0全部时不添加查询条件
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessCategoryMinor()) && !"0".equals(bo.getBusinessCategoryMinor()),
               ReleaseRecord::getBusinessCategoryMinor, bo.getBusinessCategoryMinor());
        // 负责项目经理：all全部时不添加查询条件
        lqw.like(StringUtils.isNotBlank(bo.getProjectManager()) && !"all".equals(bo.getProjectManager()),
                 ReleaseRecord::getProjectManager, bo.getProjectManager());
        // 创建人：all全部时不添加查询条件
        lqw.like(StringUtils.isNotBlank(bo.getCreatedBy()) && !"all".equals(bo.getCreatedBy()),
                 ReleaseRecord::getCreatedBy, bo.getCreatedBy());
        lqw.like(StringUtils.isNotBlank(bo.getResultTitle()), ReleaseRecord::getResultTitle, bo.getResultTitle());
        // 修改时间范围查询逻辑：查询在指定时间范围内有计划发布或实际发布的记录
        if (StringUtils.isNotBlank(bo.getStartTime()) && StringUtils.isNotBlank(bo.getEndTime())) {
            lqw.and(wrapper -> wrapper
                // startTime在查询范围内
                .ge(ReleaseRecord::getStartTime, bo.getStartTime())
                // endTime在查询范围内
                .le(ReleaseRecord::getEndTime, bo.getEndTime())
            );
        }
        if (bo.getCreatedTimeStart() != null && bo.getCreatedTimeEnd() != null) {
            lqw.between(ReleaseRecord::getCreatedTime, bo.getCreatedTimeStart(), bo.getCreatedTimeEnd());
        }

        if (bo.getEndTimeStart() != null && bo.getEndTimeEnd() != null) {
            lqw.between(ReleaseRecord::getEndTime, bo.getEndTimeStart(), bo.getEndTimeEnd());
        }
        return lqw;
    }

    /**
     * 批量转换部门ID为部门名称 - 优化性能，避免N+1查询
     */
    private void batchConvertDeptIdsToNames(List<ReleaseRecordVo> records) {
        if (records == null || records.isEmpty()) {
            return;
        }
        // 收集所有需要查询的部门ID
        Set<Long> allDeptIds = new HashSet<>();
        for (ReleaseRecordVo vo : records) {
            allDeptIds.addAll(extractDeptIds(vo.getDevDepts()));
            allDeptIds.addAll(extractDeptIds(vo.getTestDepts()));
            allDeptIds.addAll(extractDeptIds(vo.getOtherDepts()));
        }

        // 一次性查询所有部门信息
        Map<Long, String> deptIdNameMap = allDeptIds.isEmpty() ?
            Collections.emptyMap() :
            sysDeptService.listByDeptIds(allDeptIds).stream()
                .collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));

        // 批量转换
        for (ReleaseRecordVo vo : records) {
            convertDeptIdsToNamesWithMap(vo, deptIdNameMap);
        }
    }

    /**
     * 从逗号分隔的部门ID字符串中提取部门ID集合
     */
    private Set<Long> extractDeptIds(String deptIds) {
        Set<Long> ids = new HashSet<>();
        if (StringUtils.isBlank(deptIds)) {
            return ids;
        }

        try {
            String[] idArray = deptIds.split(",");
            for (String id : idArray) {
                if (StringUtils.isNotBlank(id)) {
                    ids.add(Long.parseLong(id.trim()));

                }
            }
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

        return ids;
    }

    /**
     * 使用预加载的部门信息Map进行转换
     */
    private void convertDeptIdsToNamesWithMap(ReleaseRecordVo vo, Map<Long, String> deptIdNameMap) {
        if (vo == null) {
            return;
        }
        // 转换开发组
        if (StringUtils.isNotBlank(vo.getDevDepts())) {
            vo.setDevDeptsName(convertDeptIdsWithMap(vo.getDevDepts(), deptIdNameMap));
        }
        // 转换测试组
        if (StringUtils.isNotBlank(vo.getTestDepts())) {
            vo.setTestDeptsName(convertDeptIdsWithMap(vo.getTestDepts(), deptIdNameMap));
        }
        // 转换其它组
        if (StringUtils.isNotBlank(vo.getOtherDepts())) {
            vo.setOtherDeptsName(convertDeptIdsWithMap(vo.getOtherDepts(), deptIdNameMap));
        }
    }

    /**
     * 使用Map将部门ID字符串转换为部门名称字符串
     */
    private String convertDeptIdsWithMap(String deptIds, Map<Long, String> deptIdNameMap) {
        if (StringUtils.isBlank(deptIds)) {
            return "";
        }

        try {
            String[] ids = deptIds.split(",");
            List<String> deptNames = Arrays.stream(ids)
                .filter(StringUtils::isNotBlank)
                .map(id -> {
                    Long deptId = Long.parseLong(id.trim());
                    return deptIdNameMap.getOrDefault(deptId, id);
                })
                .collect(Collectors.toList());
            return String.join(",", deptNames);
        } catch (Exception e) {
           throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 转换部门ID为部门名称 - 保留原方法用于单个查询
     */
    private void convertDeptIdsToNames(ReleaseRecordVo vo) {
        if (vo == null) {
            return;
        }
        // 收集需要查询的部门ID
        Set<Long> deptIds = new HashSet<>();
        deptIds.addAll(extractDeptIds(vo.getDevDepts()));
        deptIds.addAll(extractDeptIds(vo.getTestDepts()));
        deptIds.addAll(extractDeptIds(vo.getOtherDepts()));
        // 一次性查询所有需要的部门信息
        Map<Long, String> deptIdNameMap = deptIds.isEmpty() ?
            Collections.emptyMap() :
            sysDeptService.listByDeptIds(deptIds).stream()
                .collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        // 转换
        convertDeptIdsToNamesWithMap(vo, deptIdNameMap);
    }

    /**
     * 新增发布版本记录
     */
    @Override
    public Boolean insertByBo(ReleaseRecordBo bo) {
        // 自动设置创建人为当前用户的禅道用户名
        setCurrentUserAsCreator(bo);

        // 设置数据源为手动添加
        bo.setDataSource("2");
        bo.setSourceReleaseId(null);

        // 生成成果编码
        generateResultCode(bo);

        ReleaseRecord add = BeanUtil.toBean(bo, ReleaseRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 生成成果编码
     */
    private void generateResultCode(ReleaseRecordBo bo) {
        try {
            // 获取当前日期
            java.util.Date now = new java.util.Date();
            String dateStr = DateUtils.parseDateToStr("yyyyMMdd", now);

            // 获取下一个流水号
            int nextSerial = releaseRecordNumberService.getNextSerialNumber(dateStr);

            // 生成完整的成果编码
            String resultCode = releaseRecordNumberService.generateResultCode(dateStr, nextSerial);
            bo.setResultCode(resultCode);

            log.info("生成成果编码: {}, 日期: {}, 流水号: {}", resultCode, dateStr, nextSerial);
        } catch (Exception e) {
            throw new ServiceException("生成成果编码失败：" + e.getMessage());
        }
    }

    /**
     * 设置当前用户为创建人
     */
    private void setCurrentUserAsCreator(ReleaseRecordBo bo) {
        try {
            // 获取当前登录用户
            LoginUser loginUser = LoginHelper.getLoginUser();
            if (loginUser == null) {
                throw new ServiceException("当前用户未登录，无法创建记录");
            }

            // 获取用户详细信息
            SysUser currentUser = sysUserService.selectUserById(loginUser.getUserId());
            if (currentUser == null) {
                throw new ServiceException("当前用户信息不存在，无法创建记录");
            }

            // 获取禅道用户名
            String ztUserName = currentUser.getZtUserName();
            if (StringUtils.isBlank(ztUserName)) {
                throw new ServiceException("当前用户未配置禅道用户名，无法创建记录");
            }

            // 设置创建人
            bo.setCreatedBy(ztUserName);
        } catch (Exception e) {
            throw new ServiceException("获取当前用户信息失败：" + e.getMessage());
        }
    }

    /**
     * 修改发布版本记录
     */
    @Override
    public Boolean updateByBo(ReleaseRecordBo bo) {
        // 检查ID是否为空
        if (bo.getId() == null) {
            throw new ServiceException("修改记录时ID不能为空");
        }
        // 检查数据是否存在
        ReleaseRecord existingRecord = baseMapper.selectById(bo.getId());
        if (existingRecord == null) {
            throw new ServiceException("要修改的记录不存在");
        }
        // 直接转换Bo对象为实体对象（利用@Validated的校验结果）
        ReleaseRecord updateRecord = BeanUtil.toBean(bo, ReleaseRecord.class);
        // 强制保护不可变字段，使用旧数据的值
        updateRecord.setResultCode(existingRecord.getResultCode());
        updateRecord.setDataSource(existingRecord.getDataSource());
        updateRecord.setSourceReleaseId(existingRecord.getSourceReleaseId());
        updateRecord.setCreatedBy(existingRecord.getCreatedBy());
        updateRecord.setCreatedTime(existingRecord.getCreatedTime());
        updateRecord.setDelFlag(existingRecord.getDelFlag());

        // 处理时间字段转换（Bo中是String，实体中是Date）
        if (StringUtils.isNotBlank(bo.getStartTime())) {
            updateRecord.setStartTime(parseStringToDate(bo.getStartTime()));
        }
        if (StringUtils.isNotBlank(bo.getEndTime())) {
            updateRecord.setEndTime(parseStringToDate(bo.getEndTime()));
        }

        // 设置更新人为当前用户和更新时间
        setCurrentUserAsUpdater(updateRecord);
        updateRecord.setUpdatedTime(new java.util.Date());

        // 校验更新数据
        validEntityBeforeSave(updateRecord);

        log.info("修改发布版本记录，ID: {}, 成果编号: {}", updateRecord.getId(), updateRecord.getResultCode());

        return baseMapper.updateById(updateRecord) > 0;
    }

    /**
     * 设置当前用户为更新人
     */
    private void setCurrentUserAsUpdater(ReleaseRecord record) {
        try {
            LoginUser loginUser = LoginHelper.getLoginUser();
            if (loginUser != null) {
                SysUser currentUser = sysUserService.selectUserById(loginUser.getUserId());
                if (currentUser != null && StringUtils.isNotBlank(currentUser.getZtUserName())) {
                    record.setUpdatedBy(currentUser.getZtUserName());
                }
            }
        } catch (Exception e) {
            // 更新人设置失败不影响主流程，只记录日志
            System.err.println("设置更新人失败：" + e.getMessage());
        }
    }

    /**
     * 将字符串时间转换为Date对象
     */
    private java.util.Date parseStringToDate(String dateString) {
        if (StringUtils.isBlank(dateString)) {
            return null;
        }
        try {
            // 假设前端传递的是 yyyy-MM-dd 格式
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd");
            return sdf.parse(dateString);
        } catch (Exception e) {
            throw new ServiceException("时间格式不正确，请使用 yyyy-MM-dd 格式");
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ReleaseRecord entity) {
        // 校验开始时间和结束时间
        if (entity.getStartTime() != null && entity.getEndTime() != null) {
            // 校验：结束时间应该大于等于开始时间
            if (entity.getStartTime().after(entity.getEndTime())) {
                throw new ServiceException("结束时间不能早于开始时间");
            }
        }
        // 校验成果简介长度
        final int resultSummaryLength=1500;
        if (StringUtils.isNotBlank(entity.getResultSummary()) && entity.getResultSummary().length() > resultSummaryLength) {
            throw new ServiceException("成果简介不能超过1500个字符");
        }
        // 校验耗时天数范围
        if (entity.getDurationDays() != null && entity.getDurationDays() < 0) {
            throw new ServiceException("耗时天数不能为负数");
        }
        // 检验耗费人力范围
        if (entity.getManpowerCost() != null && entity.getManpowerCost() < 0) {
            throw new ServiceException("耗费人力不能为负数");
        }
        // 校验成果类型是否在有效范围内
        if (StringUtils.isNotBlank(entity.getResultType())) {
            String[] validTypes = {"1", "2", "3", "4", "5"};
            boolean isValid = false;
            for (String validType : validTypes) {
                if (validType.equals(entity.getResultType())) {
                    isValid = true;
                    break;
                }
            }
            if (!isValid) {
                throw new ServiceException("成果类型参数无效");
            }
        }
        // 校验业务大类是否在有效范围内
        if (StringUtils.isNotBlank(entity.getBusinessCategoryMajor())) {
            String[] validMajorCategories = {"1", "2"};
            boolean isValid = false;
            for (String validCategory : validMajorCategories) {
                if (validCategory.equals(entity.getBusinessCategoryMajor())) {
                    isValid = true;
                    break;
                }
            }
            if (!isValid) {
                throw new ServiceException("所属业务大类参数无效");
            }
        }
        // 校验业务小类是否在有效范围内
        if (StringUtils.isNotBlank(entity.getBusinessCategoryMinor())) {
            String[] validMinorCategories = {"1", "2", "3", "4", "5", "6", "7", "8"};
            boolean isValid = false;
            for (String validCategory : validMinorCategories) {
                if (validCategory.equals(entity.getBusinessCategoryMinor())) {
                    isValid = true;
                    break;
                }
            }
            if (!isValid) {
                throw new ServiceException("所属业务小类参数无效");
            }
        }
    }
}
