package com.qmqb.imp.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import com.hzed.structure.common.util.date.DateUtil;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.enums.ProcessCode;
import com.qmqb.imp.common.enums.WarnLevelEnum;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.domain.UserKqStat;
import com.qmqb.imp.system.domain.WorkStat;
import com.qmqb.imp.system.domain.bo.DeptWarnAnalysisBo;
import com.qmqb.imp.system.domain.bo.UserKqStatBo;
import com.qmqb.imp.system.domain.bo.WorkStatBo;
import com.qmqb.imp.system.domain.dto.DeptProcessCountDTO;
import com.qmqb.imp.system.domain.vo.*;
import com.qmqb.imp.system.mapper.UserKqStatMapper;
import com.qmqb.imp.system.mapper.WorkStatMapper;
import com.qmqb.imp.system.runner.HolidayUtil;
import com.qmqb.imp.system.service.*;
import com.qmqb.imp.system.service.process.IProcessOperationUsersService;
import com.qmqb.imp.system.service.PerformStatService;
import com.qmqb.imp.system.service.WarnAnalysisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PerformStatServiceImpl implements PerformStatService {

    private final WorkStatMapper workStatMapper;
    private final UserKqStatMapper userKqStatMapper;
    private final DeptCacheService deptCacheService;
    private final IProcessOperationUsersService iProcessOperationUsersService;
    private final WarnAnalysisService warnAnalysisService;
    private final IUserKqStatService iUserKqStatService;
    private final IUserLeaveService userLeaveService;

    /**
     * 根据年月获取每月各组绩效合计
     *
     * @param doneYear  完成年份
     * @param doneMonth 完成月份
     * @return
     */
    @Override
    public R<List<WorkTotalVO>> getWorkTotal(Integer doneYear, Integer doneMonth) {
        List<WorkStat> workStats = workStatMapper.getWorkStatExcludeDepart(WorkStatBo.builder().workYear(doneYear).workMonth(doneMonth).build());
        Map<Long, List<WorkStat>> workStatMap = workStats.stream().filter(workStat -> !Objects.isNull(workStat.getWorkGroupId())).collect(Collectors.groupingBy(WorkStat::getWorkGroupId));
        if (MapUtil.isEmpty(workStatMap)) {
            return R.ok(Collections.emptyList());
        }
        List<UserKqStat> userKqStats = userKqStatMapper.getUserKqStatExcludeDepart(UserKqStatBo.builder().kqYear(doneYear.toString()).kqMonth(doneMonth.toString()).build());
        List<WorkTotalVO> vos = new ArrayList<>();
        //根据组别获取绩效数据，获取对应组别的人员列表
        Map<Long, Map<String, BigDecimal>> groupUserWorkTimeMap = new HashMap<>(32);
        workStatMap.forEach((groupId, groupWorkStats) -> {
            WorkTotalVO vo = new WorkTotalVO();
            vo.setYear(doneYear);
            vo.setMonth(doneMonth);
            //查询最新组名
            String groupName = deptCacheService.getDeptNameById(groupId);
            vo.setDeptId(groupId);
            vo.setGroupName(groupName);
            int peopleNumber = groupWorkStats.size();
            vo.setPeopleNumber(peopleNumber);
            List<String> nickNames = groupWorkStats.stream().map(WorkStat::getWorkUsername).collect(Collectors.toList());
            vo.setPeopleList(StringUtils.join(nickNames, ";"));
            // 人均完成的任务总数
            BigDecimal taskDoneSum = CollUtil.isEmpty(groupWorkStats) ? BigDecimal.ZERO : groupWorkStats.stream().map(e -> BigDecimal.valueOf(e.getWorkDoneClosedTaskCount())).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTaskCount(NumberUtil.div(taskDoneSum, peopleNumber, 2));
            // 平均工作时长（小时）
            List<UserKqStat> filterKqStats = userKqStats.stream().filter(e -> nickNames.contains(e.getKqUserName())).peek(e ->{
                Map<String, BigDecimal> userWorkTimeMap = groupUserWorkTimeMap.computeIfAbsent(groupId, k -> new HashMap<>(16));
                userWorkTimeMap.put(e.getKqUserName(), BigDecimal.valueOf(e.getKqAttendanceWorkTime()));
            }).collect(Collectors.toList());
            BigDecimal totalWorkTime = filterKqStats.stream().map(e -> BigDecimal.valueOf(e.getKqAttendanceWorkTime())).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setWorkTime(NumberUtil.div(NumberUtil.div(totalWorkTime, 60), peopleNumber, 2));

            // 每任务所耗小时=工作时长(分)/任务总数
            BigDecimal totalPeopleElapsedTime = filterKqStats.stream().map(e -> {
                if (CollUtil.isEmpty(groupWorkStats)) {
                    return BigDecimal.ZERO;
                }
                List<WorkStat> userWorkStat = groupWorkStats.stream().filter(o -> StringUtils.equals(e.getKqUserName(), o.getWorkUsername())).collect(Collectors.toList());
                BigDecimal personTaskSum = CollUtil.isEmpty(userWorkStat) ? BigDecimal.ZERO : userWorkStat.stream().map(workStat -> BigDecimal.valueOf(workStat.getWorkDoneClosedTaskCount())).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 每人每任务所耗小时
                return NumberUtil.equals(personTaskSum, BigDecimal.ZERO) ?
                    BigDecimal.ZERO : NumberUtil.div(NumberUtil.div(BigDecimal.valueOf(e.getKqAttendanceWorkTime()), 60), personTaskSum, 2);
            }).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setElapsedTime(NumberUtil.div(totalPeopleElapsedTime, peopleNumber, 2));
            // 平均每日工时=工作时长(分)/出勤天数
            BigDecimal totalPeopleDailyTime = filterKqStats.stream().map(e -> {
                // 每人每日平均工时
                BigDecimal peopleDailyTime = BigDecimal.ZERO;
                if (!NumberUtil.equals(e.getKqAttendanceDays(), CommConstants.CommonVal.ZERO)) {
                    peopleDailyTime = NumberUtil.div(BigDecimal.valueOf(e.getKqAttendanceWorkTime()), BigDecimal.valueOf(e.getKqAttendanceDays()), 2);
                }
                // 转换成小时
                return NumberUtil.div(peopleDailyTime, 60, 2);
            }).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setDailyWorkTime(NumberUtil.div(totalPeopleDailyTime, peopleNumber, 2));
            // 平均迟到时长（分钟）
            BigDecimal totalLateMinute = filterKqStats.stream().map(e -> BigDecimal.valueOf(e.getKqLateMinute())).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setLateMinute(NumberUtil.div(totalLateMinute, peopleNumber, 2));
            //禅道任务耗时
            BigDecimal sumWorkConsumedSysTime = CollUtil.isEmpty(groupWorkStats) ? BigDecimal.ZERO : groupWorkStats.stream().map(workStat -> BigDecimal.valueOf(workStat.getWorkConsumedSysTime())).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setAvgWorkConsumedSysTime(NumberUtil.div(sumWorkConsumedSysTime, peopleNumber, 2));
            // 总加班时长（小时）
            BigDecimal totalOverTime = filterKqStats.stream().map(e -> BigDecimal.valueOf(e.getKqOvertimeApproveCount())).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setOvertime(totalOverTime);
            vos.add(vo);
        });
        List<Long> groupIdList = vos.stream().map(WorkTotalVO::getDeptId).collect(Collectors.toList());
        Map<Long, String> groupMaxWorkTimeGap = getGroupMaxWorkTimeGap(groupIdList, doneYear, doneMonth, groupUserWorkTimeMap);
        fillWarningAndWorkTimeInfo(doneYear,doneMonth,vos, groupMaxWorkTimeGap);
        return R.ok(vos);
    }

    private Map<Long, String> getGroupMaxWorkTimeGap(List<Long> groupIdList, Integer year, Integer month, Map<Long, Map<String, BigDecimal>> groupUserWorkTimeMap) {
        // 获取超过21小时的请假人员
        Map<Long, String> groupMaxWorkTimeGap = new HashMap<>(32);
        List<UserLeaveVo> userLeaveVos = userLeaveService.statisticUserLeave(groupIdList, year.toString(), month.toString());
        Set<String> groupUserWorkTimes = HolidayUtil.getOverTimeLeaveNickName(userLeaveVos, year, month);
        for (Map.Entry<Long, Map<String, BigDecimal>> entry : groupUserWorkTimeMap.entrySet()) {
            Long groupId = entry.getKey();
            Map<String, BigDecimal> userWorkTimeMap = entry.getValue();

            // 过滤掉请假超过21小时的用户
            List<BigDecimal> filteredWorkTimes = userWorkTimeMap.entrySet().stream()
                // 排除请假 >21小时的人
                .filter(e -> !groupUserWorkTimes.contains(e.getKey()))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());

            if (!filteredWorkTimes.isEmpty()) {
                BigDecimal max = Collections.max(filteredWorkTimes);
                BigDecimal min = Collections.min(filteredWorkTimes);
                String gap = max.subtract(min).divide(new BigDecimal(60), 0, RoundingMode.DOWN).toString();
                groupMaxWorkTimeGap.put(groupId, gap);
            } else {
                // 没有有效数据时设为0
                groupMaxWorkTimeGap.put(groupId, "0");
            }
        }
        return groupMaxWorkTimeGap;
    }

    /**
     * 填充预警和工时信息
     *
     * @param year
     * @param month
     * @param workTotalList
     */
    private void fillWarningAndWorkTimeInfo(Integer year, Integer month, List<WorkTotalVO> workTotalList, Map<Long, String> groupMaxWorkTimeGap) {
        Date beginTime = DateUtils.dateToStart(DateUtils.dateTime(DateUtils.YYYY_MM_DD, DateUtils.getFirstDayOfMonth(year,
            Objects.isNull(month) || month <= 0 ? 1 : month)));
        Date endTime = DateUtils.dateToEnd(DateUtils.dateTime(DateUtils.YYYY_MM_DD, DateUtils.getLastDayOfMonth(year,
            Objects.isNull(month) || month <= 0 ? 12 : month)));

        // 巡检次数信息
        Map<Long, Integer> processCountMap = iProcessOperationUsersService
            .selectDeptProcessStats(ProcessCode.CYCLE_INSPECTION.getCode(), beginTime, endTime)
            .stream().collect(Collectors.toMap(DeptProcessCountDTO::getDeptId, DeptProcessCountDTO::getTotalCount));

        // 绩效预警信息
        DeptWarnAnalysisBo deptWarnAnalysisBo = new DeptWarnAnalysisBo();
        deptWarnAnalysisBo.setStartTime(DateUtil.format(beginTime, "yyyy-MM-dd HH:mm:ss"));
        deptWarnAnalysisBo.setEndTime(DateUtil.format(endTime, "yyyy-MM-dd HH:mm:ss"));
        Map<Long, Map<String, Integer>> jiXiaowarningMap = warnAnalysisService.getMultipleRecord(deptWarnAnalysisBo)
            .stream().collect(Collectors.toMap(DepartmentWarnAnalysisVo.MultipleRecord::getDeptId, vo -> vo.getLevelRecords()
                .stream().collect(Collectors.toMap(DepartmentWarnAnalysisVo.LevelRecord::getLevel, DepartmentWarnAnalysisVo.LevelRecord::getNum))));

        // 工时信息
        Map<Long, GroupKqStatVO> groupKqStatMap = iUserKqStatService.groupStatistics(year, month).stream().collect(Collectors.toMap(GroupKqStatVO::getDeptId, a -> a));

        // 循环填充数据
        workTotalList.forEach(workTotalVO -> {
            workTotalVO.setCycleInspectionCount(processCountMap.getOrDefault(workTotalVO.getDeptId(), 0));
            Map<String, Integer> jiXiaowarningLevelMap = jiXiaowarningMap.getOrDefault(workTotalVO.getDeptId(), Collections.emptyMap());
            workTotalVO.setP0WarningCount(jiXiaowarningLevelMap.getOrDefault(WarnLevelEnum.P0.getName(), 0));
            workTotalVO.setP1WarningCount(jiXiaowarningLevelMap.getOrDefault(WarnLevelEnum.P1.getName(), 0));
            workTotalVO.setP2WarningCount(jiXiaowarningLevelMap.getOrDefault(WarnLevelEnum.P2.getName(), 0));
            GroupKqStatVO groupKqStatVO = groupKqStatMap.get(workTotalVO.getDeptId());
            workTotalVO.setGroupMaxWorkTimeGap(groupMaxWorkTimeGap.get(workTotalVO.getDeptId()));
            workTotalVO.setGroupAvgWorkTimeLtEightHourCount(groupKqStatVO == null ? 0 : groupKqStatVO.getGroupAvgWorkTimeLtEightHourCount());
        });
    }
}
