<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.SlowQueryProcessInfoMapper">

    <resultMap id="BaseResultMap" type="com.qmqb.imp.system.domain.SlowQueryProcessInfo">
        <id property="id" column="id"/>
        <result property="sqlHash" column="sql_hash"/>
        <result property="processStatus" column="process_status"/>
        <result property="processTime" column="process_time"/>
        <result property="processBy" column="process_by"/>
        <result property="processResult" column="process_result"/>
        <result property="analysisResult" column="analysis_result"/>
    </resultMap>


    <insert id="insertOrUpdateProcessInfo">
        insert t_slow_query_process_info(sql_hash, process_status, process_time, process_by, process_result)
        values (#{info.sqlHash}, #{info.processStatus}, #{info.processTime}, #{info.processBy}, #{info.processResult})
        on duplicate key update process_status = VALUES(process_status),
                                process_time   = VALUES(process_time),
                                process_by     = VALUES(process_by),
                                process_result = VALUES(process_result);
    </insert>


</mapper>
