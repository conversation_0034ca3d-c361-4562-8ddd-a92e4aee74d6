package com.qmqb.imp.system.domain.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/4 17:22
 */
@Getter
public enum SlowQueryTopTypeEnum {

    /**
     * 执行次数
     */
    QUERY_TIMES("query_times", "执行次数"),
    /**
     * 总毫秒数
     */
    SUM_TIME("sum_time", "总毫秒数"),
    /**
     * 总毫秒数
     */
    AVG_TIME("avg_time", "平均毫秒数"),
    ;

    private final String code;

    private final String desc;

    SlowQueryTopTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
