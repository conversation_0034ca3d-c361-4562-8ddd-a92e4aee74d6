package com.qmqb.imp;

import cn.hutool.core.net.NetUtil;
import com.hzed.structure.log.annotation.EnableLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.ConfigurableEnvironment;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@Slf4j
@EnableLog
@SpringBootApplication
public class AppApplication {

    public static void main(String[] args) {
        System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication application = new SpringApplication(AppApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        ConfigurableApplicationContext context = application.run(args);
        ConfigurableEnvironment env = context.getEnvironment();
        String ip = NetUtil.getIpByHost(NetUtil.getLocalHostName());
        String name = env.getProperty("spring.application.name");
        String port = env.getProperty("server.port");
        String path = env.getProperty("server.servlet.context-path");
        String active = env.getProperty("spring.profiles.active");
        log.info("\n--------------------------------------------------------------------------\n\t" +
            "Application " + name + " is running, Active " + active + ".\n\t" +
            "Local: \t\thttp://localhost:" + port + path + "/\n\t" +
            "External: \thttp://" + ip + ":" + port + path + "/\n\t" +
            "Health: \thttp://" + ip + ":" + port + path + "/actuator/health\n" +
            "--------------------------------------------------------------------------");
    }

}
