package com.qmqb.imp.common.enums;

import lombok.Getter;

/**
 * 绩效反馈数据来源枚举
 * <p>
 * 定义了绩效反馈的数据来源，包括：
 * - 手工添加：由用户手动录入
 * - 定时任务：由定时任务自动生成
 * - 系统产生：由系统根据业务逻辑自动生成
 * </p>
 *
 * <AUTHOR>
 */
@Getter
public enum PerformanceFeedbackDataSourceEnum {
    /**
     * 手工添加
     */
    MANUAL_ADD("MANUAL_ADD", "手工添加"),

    /**
     * 定时任务
     */
    SCHEDULED_TASK("SCHEDULED_TASK", "定时任务"),

    /**
     * 系统产生
     */
    SYSTEM_GENERATED("SYSTEM_GENERATED", "系统产生");

    /**
     * 来源编码
     */
    private final String code;

    /**
     * 来源名称
     */
    private final String name;

    /**
     * 构造方法
     *
     * @param code 来源编码
     * @param name 来源名称
     */
    PerformanceFeedbackDataSourceEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static PerformanceFeedbackDataSourceEnum getByCode(String code) {
        for (PerformanceFeedbackDataSourceEnum source : values()) {
            if (source.getCode().equals(code)) {
                return source;
            }
        }
        return null;
    }

    /**
     * 判断是否为有效来源
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidSource(String code) {
        return getByCode(code) != null;
    }
}
