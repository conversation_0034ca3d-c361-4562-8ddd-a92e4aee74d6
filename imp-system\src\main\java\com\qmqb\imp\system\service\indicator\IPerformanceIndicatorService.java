package com.qmqb.imp.system.service.indicator;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceIndicatorBo;
import com.qmqb.imp.system.domain.performance.PerformanceIndicator;
import com.qmqb.imp.system.domain.vo.performance.PerformanceIndicatorVo;

import java.util.Collection;
import java.util.List;

/**
 * 绩效指标Service接口
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
public interface IPerformanceIndicatorService extends IService<PerformanceIndicator> {

    /**
     * 查询绩效指标
     *
     * @param id 绩效指标ID
     * @return 绩效指标信息
     */
    PerformanceIndicatorVo queryById(Long id);

    /**
     * 查询绩效指标列表（分页）
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 绩效指标分页列表
     */
    TableDataInfo<PerformanceIndicatorVo> queryPageList(PerformanceIndicatorBo bo, PageQuery pageQuery);

    /**
     * 查询绩效指标列表
     *
     * @param bo 查询条件
     * @return 绩效指标列表
     */
    List<PerformanceIndicatorVo> queryList(PerformanceIndicatorBo bo);

    /**
     * 新增绩效指标
     *
     * @param bo 新增对象
     * @return 是否成功
     */
    Boolean insertByBo(PerformanceIndicatorBo bo);

    /**
     * 修改绩效指标
     *
     * @param bo 修改对象
     * @return 是否成功
     */
    Boolean updateByBo(PerformanceIndicatorBo bo);

    /**
     * 校验并批量删除绩效指标信息
     *
     * @param ids     待删除ID集合
     * @param isValid 是否校验
     * @return 是否成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据绩效ID获取指标列表
     *
     * @param performanceId 绩效ID
     * @return 指标列表
     */
    List<PerformanceIndicator> getByPerformanceId(Long performanceId);
}
