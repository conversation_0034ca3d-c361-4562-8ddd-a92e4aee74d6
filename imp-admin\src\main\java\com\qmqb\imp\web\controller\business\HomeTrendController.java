package com.qmqb.imp.web.controller.business;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.vo.KqAfter20TrendVo;
import com.qmqb.imp.common.core.domain.vo.YearTrendVo;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.enums.HomeTrendTypeEnum;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.bo.HomeTrendBo;
import com.qmqb.imp.system.domain.vo.HomeTrendVo;
import com.qmqb.imp.system.service.IHomeTrendService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 首页数据趋势
 *
 * <AUTHOR>
 * @date 2024-11-21
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/homeTrend")
public class HomeTrendController extends BaseController {

    private final IHomeTrendService iHomeTrendService;

    /**
     * 查询首页数据趋势列表
     */
    @SaCheckPermission("system:homeTrend:list")
    @GetMapping("/list")
    public TableDataInfo<HomeTrendVo> list(HomeTrendBo bo, PageQuery pageQuery) {
        return iHomeTrendService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出首页数据趋势列表
     */
    @SaCheckPermission("system:homeTrend:export")
    @Log(title = "首页数据趋势", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HomeTrendBo bo, HttpServletResponse response) {
        List<HomeTrendVo> list = iHomeTrendService.queryList(bo);
        ExcelUtil.exportExcel(list, "首页数据趋势", HomeTrendVo.class, response);
    }

    /**
     * 获取首页数据趋势详细信息
     *
     * @param trendTime 主键
     */
    @SaCheckPermission("system:homeTrend:query")
    @GetMapping("/{trendTime}")
    public R<HomeTrendVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Date trendTime) {
        return R.ok(iHomeTrendService.queryById(trendTime));
    }

    /**
     * 新增首页数据趋势
     */
    @SaCheckPermission("system:homeTrend:add")
    @Log(title = "首页数据趋势", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody HomeTrendBo bo) {
        return toAjax(iHomeTrendService.insertByBo(bo));
    }

    /**
     * 修改首页数据趋势
     */
    @SaCheckPermission("system:homeTrend:edit")
    @Log(title = "首页数据趋势", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody HomeTrendBo bo) {
        return toAjax(iHomeTrendService.updateByBo(bo));
    }

    /**
     * 删除首页数据趋势
     *
     * @param trendTimes 主键串
     */
    @SaCheckPermission("system:homeTrend:remove")
    @Log(title = "首页数据趋势", businessType = BusinessType.DELETE)
    @DeleteMapping("/{trendTimes}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Date[] trendTimes) {
        return toAjax(iHomeTrendService.deleteWithValidByIds(Arrays.asList(trendTimes), true));
    }

    /**
     * 首页数据趋势
     * @param type 类型,1开发、2测试、3项管、4组长、5等待任务、6进行中任务、7完成任务、8暂停任务、9文档数、10代码库数、11关闭任务数、12取消任务数
     */
    @Log(title = "首页数据趋势")
    @GetMapping("/getHomeTrend")
    public R<YearTrendVo> getHomeTrend(Integer type){
        HomeTrendTypeEnum homeTrendTypeEnum = HomeTrendTypeEnum.getEnumByCode(type);
        return R.ok(iHomeTrendService.getHomeTrend(homeTrendTypeEnum));
    }

    /**
     * 首页晚上20点后考勤趋势
     * @param time 时间，0本月，1上个月,不传默认本月
     */
    @Log(title = "首页晚上20点后考勤")
    @GetMapping("/getKqAfter20")
    public R<KqAfter20TrendVo> getKqAfter20(@RequestParam(required = false,defaultValue = "0") Integer time) {
        return R.ok(iHomeTrendService.getKqAfter20(time));
    }
}
