package com.qmqb.imp.system.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.system.domain.StoryResult;
import com.qmqb.imp.system.domain.bo.StoryResultBo;
import com.qmqb.imp.system.domain.vo.StoryResultVo;
import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;

/**
 * 需求成果Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
public interface StoryResultMapper extends BaseMapperPlus<StoryResultMapper, StoryResult, StoryResultVo> {

    /**
     * 分页查询需求成果列表
     * @param build
     * @param request
     * @return
     */
    Page<StoryResultVo> pageList(@Param("build") Page<Object> build, @Param("request") StoryResultBo request);

    /**
     * 根据成果ID批量清空需求的成果关联信息
     * @param resultIds 成果ID集合
     * @return 更新的记录数
     */
    int clearResultInfoByResultIds(@Param("resultIds") Collection<Long> resultIds);

    /**
     * 根据需求ID批量清空需求的成果关联信息
     * @param storyIds
     * @return
     */
    int clearResultInfoByStoryIds(@Param("storyIds") Collection<Long> storyIds);
}
