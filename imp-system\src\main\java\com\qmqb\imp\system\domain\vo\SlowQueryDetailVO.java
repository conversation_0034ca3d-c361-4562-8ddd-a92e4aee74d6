package com.qmqb.imp.system.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 分支详细信息对象 tb_branch
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
@Data
public class SlowQueryDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Schema(description = "id")
    private Long id;
    /**
     * 执行时间
     */
    @Schema(description = "执行时间")
    private Date executionStartTime;
    /**
     * 解析涉及的记录条数
     */
    @Schema(description = "解析涉及的记录条数")
    private String parseRowCounts;
    /**
     * 查询返回的记录条数
     */
    @Schema(description = "查询返回的记录条数")
    private String returnRowCounts;
    /**
     * 查询耗时毫秒
     */
    @Schema(description = "查询耗时毫秒")
    private Long queryTimeMs;
    /**
     * 同类SQL的HASH值
     */
    @Schema(description = "同类SQL的HASH值")
    private String sqlHash;
    /**
     * SQL文本
     */
    @Schema(description = "SQL文本")
    private String sqlText;


}
