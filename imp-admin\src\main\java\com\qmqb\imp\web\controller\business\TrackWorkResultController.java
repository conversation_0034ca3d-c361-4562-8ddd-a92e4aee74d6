package com.qmqb.imp.web.controller.business;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.bo.TrackWorkResultBO;
import com.qmqb.imp.system.domain.vo.TrackWorkExportVO;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.service.ITrackWorkResultService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;

/**
 * 工作成果跟踪
 * <AUTHOR>
 * @since 2023-08-21
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/business/trackWorkResult")
@Tag(name = "track_workResult-controller", description = "工作成果跟踪")
public class TrackWorkResultController {

    private final ITrackWorkResultService trackWorkResultService;

    /**
     * 工作成果跟踪列表
     */
    @SaCheckPermission("system:trackWorkResult:list")
    @GetMapping("/list")
    public TableDataInfo<TrackWorkResultVO> list(TrackWorkResultBO request) {
        return trackWorkResultService.list(request);
    }

    /**
     * 导出工作成果跟踪列表
     */
    @Log(title = "导出工作成果跟踪列表", businessType = BusinessType.EXPORT)
    @SaCheckPermission("system:trackWorkResult:export")
    @PostMapping("/export")
    public void export(TrackWorkResultBO request, HttpServletResponse response) {
        TableDataInfo<TrackWorkResultVO> tableDataInfo = trackWorkResultService.list(request);
        List<TrackWorkExportVO> list;
        if (ObjectUtil.isNull(tableDataInfo)) {
            list = Collections.emptyList();
        } else {
            list = BeanUtil.copyToList(tableDataInfo.getRows(), TrackWorkExportVO.class);
        }
        ExcelUtil.exportCsv(list, "工作成果跟踪数据", TrackWorkExportVO.class, response);
    }
}
