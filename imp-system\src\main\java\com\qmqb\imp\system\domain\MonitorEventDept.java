package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 预警事件和部门关联对象 tb_monitor_event_dept
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Data
@TableName("tb_monitor_event_dept")
public class MonitorEventDept{

    private static final long serialVersionUID=1L;

    /**
     * 事件id
     */
    private Long eventId;
    /**
     * 部门id
     */
    private Long deptId;

}
