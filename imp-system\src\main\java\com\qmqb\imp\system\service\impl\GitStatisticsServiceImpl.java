package com.qmqb.imp.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.qmqb.imp.common.constant.UserConstants;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.entity.SysDept;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.CommonStatus;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.domain.GitStatisticsInfo;
import com.qmqb.imp.system.domain.vo.CodeStatisticsVO;
import com.qmqb.imp.system.mapper.GitStatisticsInfoMapper;
import com.qmqb.imp.system.mapper.SysDeptMapper;
import com.qmqb.imp.system.mapper.SysUserMapper;
import com.qmqb.imp.system.service.GitStatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GitStatisticsServiceImpl implements GitStatisticsService {

    public static final int INT_12 = 12;
    private final GitStatisticsInfoMapper gitStatisticsInfoMapper;
    private final SysUserMapper sysUserMapper;
    private final SysDeptMapper sysDeptMapper;

    /**
     * 年度工作量曲线
     *
     * @param year         年份
     * @param deptId       开发组
     * @param isNotInclude 不包含技术经理：false-否，true-是
     * @return
     */
    @Override
    public R<List<CodeStatisticsVO>> statistics(Integer year, Long deptId, Boolean isNotInclude) {
        boolean isDept = Objects.nonNull(deptId) && Objects.equals(101L, deptId);
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", UserConstants.USER_NORMAL)
            .ne("u.user_id", UserConstants.ADMIN_ID)
            .ne("u.user_id", UserConstants.JSZX_ADMIN_ID)
            // 组人员统计
            .eq(!isDept, "d.dept_id", deptId)
            // 部门统计
            .eq(isDept, "d.parent_id", deptId)
            .ne(CommonStatus.YES.getBoolVal().equals(isNotInclude), "r.role_name", "技术经理");
        List<SysUser> sysUsers = sysUserMapper.selectUserVoList(wrapper);
        if (CollUtil.isEmpty(sysUsers)) {
            return R.ok(Collections.emptyList());
        }
        DateTime dateTime = DateTime.of(String.format("%s-01-01", year), DatePattern.NORM_DATE_PATTERN);
        DateTime beginOfYear = DateUtil.beginOfYear(dateTime);
        DateTime endOfYear = DateUtil.endOfYear(dateTime);
        List<String> gitAuthorNames = sysUsers.stream().map(SysUser::getGitAuthorName).collect(Collectors.toList());
        List<String> gitCommitterNames = sysUsers.stream().map(SysUser::getGitCommitterName).collect(Collectors.toList());
        Set<String> committers = new HashSet<>();
        committers.addAll(gitAuthorNames);
        committers.addAll(gitCommitterNames);
        List<GitStatisticsInfo> gitStatisticsInfos = gitStatisticsInfoMapper.selectList(Wrappers.lambdaQuery(GitStatisticsInfo.class)
            .in(GitStatisticsInfo::getCommitter, committers)
            .le(GitStatisticsInfo::getAdditionsLine, 1000)
            .between(GitStatisticsInfo::getCommitDate, beginOfYear, endOfYear)
            .apply("message NOT LIKE '%Merge%'")
                .apply("message NOT LIKE '%merge%'")
            .apply("message NOT LIKE '%合并分支%'")
            .apply("message NOT LIKE '%Conflict%'"));
        if (CollUtil.isEmpty(gitStatisticsInfos)) {
            return R.ok(Collections.emptyList());
        }
        List<CodeStatisticsVO> vos = new ArrayList<>();
        if (!isDept) {
            // 提交人分组
            Map<String, List<GitStatisticsInfo>> committerInfoMap = new HashMap<>(sysUsers.size());
            for (SysUser sysUser : sysUsers) {
                List<GitStatisticsInfo> infos = gitStatisticsInfos.stream()
                    .filter(e -> StringUtils.equals(sysUser.getGitAuthorName(), e.getCommitter())
                        || StringUtils.equals(sysUser.getGitCommitterName(), e.getCommitter()))
                    .collect(Collectors.toList());
                committerInfoMap.put(sysUser.getNickName(), infos);
            }
            committerInfoMap.forEach((committer, infos) -> {
                CodeStatisticsVO vo = new CodeStatisticsVO();
                vo.setName(committer);
                List<Integer> data = handleData(infos);
                vo.setData(data);
                vos.add(vo);
            });
        } else {
            Map<Long, List<SysUser>> sysUserMap = sysUsers.stream().collect(Collectors.groupingBy(SysUser::getDeptId));
            List<SysDept> sysDepts = sysDeptMapper.selectBatchIds(sysUserMap.keySet());
            sysUserMap.forEach((k, v) -> {
                String deptName = sysDepts.stream()
                    .filter(e -> Objects.equals(k, e.getDeptId()))
                    .map(SysDept::getDeptName).findFirst()
                    .orElse("默认组");
                CodeStatisticsVO vo = new CodeStatisticsVO();
                vo.setName(deptName);
                List<String> authorNames = v.stream().map(SysUser::getGitAuthorName).collect(Collectors.toList());
                List<String> committerNames = v.stream().map(SysUser::getGitCommitterName).collect(Collectors.toList());
                Set<String> names = new HashSet<>();
                names.addAll(authorNames);
                names.addAll(committerNames);
                List<GitStatisticsInfo> infos = gitStatisticsInfos.stream().filter(e -> names.contains(e.getCommitter())).collect(Collectors.toList());
                List<Integer> data = handleData(infos);
                vo.setData(data);
                vos.add(vo);
            });
        }
        return R.ok(vos);
    }

    /**
     * 月份分组
     *
     * @param infos
     * @return
     */
    private List<Integer> handleData(List<GitStatisticsInfo> infos) {
        List<Integer> data = new ArrayList<>();
        Map<Integer, List<GitStatisticsInfo>> monthInfoMap = infos.stream().collect(Collectors.groupingBy(e -> DateUtil.month(e.getCommitDate())));
        for (int i = 0; i < INT_12; i++) {
            List<GitStatisticsInfo> monthInfos = monthInfoMap.get(i);
            if (CollUtil.isEmpty(monthInfos)) {
                data.add(0);
            } else {
                int sum = monthInfos.stream().mapToInt(GitStatisticsInfo::getTotalLine).sum();
                data.add(sum);
            }
        }
        return data;
    }

}
