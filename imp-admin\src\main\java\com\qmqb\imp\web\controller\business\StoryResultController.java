package com.qmqb.imp.web.controller.business;



import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.system.domain.vo.StoryResultVo;
import com.qmqb.imp.system.domain.bo.StoryResultBo;
import com.qmqb.imp.system.service.IStoryResultService;
import com.qmqb.imp.common.core.page.TableDataInfo;

/**
 * 需求成果
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/storyResult")
public class StoryResultController extends BaseController {

    private final IStoryResultService iStoryResultService;

    /**
     * 查询需求成果列表
     */
    @GetMapping("/list")
    public TableDataInfo<StoryResultVo> list(StoryResultBo bo, PageQuery pageQuery) {
        return iStoryResultService.queryPageList(bo, pageQuery);
    }


}
