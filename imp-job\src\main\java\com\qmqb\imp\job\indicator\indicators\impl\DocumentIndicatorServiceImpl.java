package com.qmqb.imp.job.indicator.indicators.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.domain.vo.ZtDocVo;
import com.qmqb.imp.system.domain.vo.ZtDocContentVo;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.IZtDocService;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-07-02 19:13
 * 文档指标等级计算
 */
@Slf4j
@Service
public class DocumentIndicatorServiceImpl implements IndicatorLevelCalcService {


    @Resource
    IZtDocService ztDocService;
    @Resource
    ISysUserService sysUserService;

    /**
     * 文档字数满足200
     */
    private static final int MIN_WORD_COUNT = 200;

    /**
     * S级标准：至少3篇文档
     */
    private static final int S_LEVEL_DOC_COUNT = 3;
    /**
     * A级标准：至少2篇文档
     */
    private static final int A_LEVEL_DOC_COUNT = 2;
    /**
     * B级标准：至少1篇文档
     */
    private static final int B_LEVEL_DOC_COUNT = 1;

    /**
     * C级标准：当月无文档
     */
    private static final int C_LEVEL_DOC_COUNT = 0;

    /**
     * D级标准：连续两个月无文档
     */
    private static final int D_LEVEL_CONDITION_CONSECUTIVE_ZERO = 0;

    @Override
    public String getIndicatorCode() {
        return PerformanceIndicatorEnum.WORK_DOCS.getCode();
    }

    @Override
    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults) {
        // 1. 获取当前月和上个月
        YearMonth currentMonth = YearMonth.of(workResult.getWorkYear(), workResult.getWorkMonth());
        YearMonth previousMonth = currentMonth.minusMonths(1);
        // 2. 查询两个月的有效文档（字数≥200）
        SysUser sysUser = sysUserService.selectUserByNickName(nickName);
        DocumentIndicatorServiceImpl calcService = ((DocumentIndicatorServiceImpl) AopContext.currentProxy());

        List<ZtDocContentVo> currentMonthDocs = calcService.getValidDocuments(sysUser.getZtUserName(), currentMonth);
        List<ZtDocContentVo> previousMonthDocs = calcService.getValidDocuments(sysUser.getZtUserName(), previousMonth);

        int currentValidCount = currentMonthDocs.size();
        int previousValidCount = previousMonthDocs.size();

        // 3. 评级逻辑
        if (currentValidCount >= S_LEVEL_DOC_COUNT) {
            return ScoreLevelEnum.SCORE_S.getCode();
        } else if (currentValidCount >= A_LEVEL_DOC_COUNT) {
            return ScoreLevelEnum.SCORE_A.getCode();
        } else if (currentValidCount >= B_LEVEL_DOC_COUNT) {
            return ScoreLevelEnum.SCORE_B.getCode();
        } else {
            // 当前月无文档时，检查上月
            if (previousValidCount == 0) {
                return ScoreLevelEnum.SCORE_D.getCode();
            }
            return ScoreLevelEnum.SCORE_C.getCode();
        }
    }

    /**
     * 获取指定月份的有效文档（字数≥200）
     */
    @DS("zentao")
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true,rollbackFor = Exception.class)
    public List<ZtDocContentVo> getValidDocuments(String ztUserName, YearMonth yearMonth) {
        LocalDate startDate = yearMonth.atDay(1);
        LocalDate endDate = yearMonth.atEndOfMonth();
        String startTime = startDate.toString() + " 00:00:00";
        String endTime = endDate.toString() + " 23:59:59";
        List<ZtDocContentVo> docs = ztDocService.getUserDocumentsWithContent(ztUserName, startTime, endTime);
        // 过滤出字数≥200的文档
        return docs.stream()
            .filter(doc -> getWordCount(getDocumentContent(doc)) >= MIN_WORD_COUNT)
            .collect(Collectors.toList());
    }

    /**
     * 获取文档内容（兼容新旧版本数据）
     * @param doc 文档对象
     * @return 文档内容
     */
    private String getDocumentContent(ZtDocContentVo doc) {
        // 优先使用draft字段（旧版本数据）
        if (StringUtils.isNotEmpty(doc.getDraft())) {
            return doc.getDraft();
        }
        // 如果draft为空，使用content字段（新版本数据）
        if (StringUtils.isNotEmpty(doc.getContent())) {
            return doc.getContent();
        }
        // 都为空则返回空字符串
        return "";
    }

    /**
     * 统计文档纯文字字数（去除HTML标签和空格）
     * @param htmlContent
     * @return
     */
    private int getWordCount(String htmlContent) {
        if (StringUtils.isEmpty(htmlContent)) {
            return 0;
        }
        try {
            // 1. 去除HTML标签
            String textOnly = Jsoup.parse(htmlContent).text();
            // 2. 去除所有空白字符后统计长度
            return textOnly.replaceAll("\\s", "").length();
        } catch (Exception e) {
            log.error("统计文档字数失败", e);
            throw new ServiceException("统计文档字数失败: " + e.getMessage());
        }
    }


    @Override
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level) {
        Integer year = workResult.getWorkYear();
        Integer month = workResult.getWorkMonth();
        // 1. 获取当前月
        YearMonth currentMonth = YearMonth.of(year, month);
        // 2. 查询两个月的有效文档（字数≥200）
        SysUser sysUser = sysUserService.selectUserByNickName(nickName);
        DocumentIndicatorServiceImpl calcService = ((DocumentIndicatorServiceImpl) AopContext.currentProxy());
        List<ZtDocContentVo> currentMonthDocs = calcService.getValidDocuments(sysUser.getZtUserName(), currentMonth);
        int currentValidCount = currentMonthDocs.size();

        StringBuilder logContent = new StringBuilder();
        logContent.append(String.format("[%s]在%s年%s月份新增的有效文档数(≥200字)：%d个",
            nickName, year, month, currentValidCount));
        // 添加评级信息
        logContent.append(String.format("，评级：%s", level));
        // 添加评级原因
        String reason = getRatingReason(currentValidCount, level);
        if (reason != null) {
            logContent.append(String.format("，原因：%s", reason));
        }
        return logContent.toString();
    }

    /**
     * 获取评级原因
     */
    private String getRatingReason(int docCount, String level) {
        switch (level) {
            case "S":
                return String.format("不少于200字的文档数量(%d)达到S级标准(≥%d)", docCount, S_LEVEL_DOC_COUNT);
            case "A":
                return String.format("不少于200字的文档数量(%d)达到A级标准(≥%d)", docCount, A_LEVEL_DOC_COUNT);
            case "B":
                return String.format("不少于200字的文档数量(%d)达到B级标准(≥%d)", docCount, B_LEVEL_DOC_COUNT);
            case "C":
                return String.format("不少于200字的文档数量(%d)未达到C级标准(=%d)", docCount,C_LEVEL_DOC_COUNT);
            case "D":
                return String.format("连续2个月不少于200字的文档数量%d篇", D_LEVEL_CONDITION_CONSECUTIVE_ZERO);
            default:
                return null;
        }
    }






}
