package com.qmqb.imp.web.controller.performance;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.bo.performance.PerformanceIndicatorBo;
import com.qmqb.imp.system.domain.vo.performance.PerformanceIndicatorVo;
import com.qmqb.imp.system.service.indicator.IPerformanceIndicatorService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * @className: PerformanceIndicatorController
 * @author: yangjiangqian
 * @description:
 * @date: 2025/6/25 11:13
 * @version: 1.0
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/performanceIndicator")
public class PerformanceIndicatorController extends BaseController {

    private final IPerformanceIndicatorService iPerformanceIndicatorService;

    /**
     * 查询绩效指标列表
     */
    @SaCheckPermission("system:performanceIndicator:list")
    @GetMapping("/list")
    public TableDataInfo<PerformanceIndicatorVo> list(PerformanceIndicatorBo bo, PageQuery pageQuery) {
        return iPerformanceIndicatorService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出绩效指标列表
     */
    @SaCheckPermission("system:performanceIndicator:export")
    @Log(title = "绩效指标", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PerformanceIndicatorBo bo, HttpServletResponse response) {
        List<PerformanceIndicatorVo> list = iPerformanceIndicatorService.queryList(bo);
        ExcelUtil.exportExcel(list, "绩效指标", PerformanceIndicatorVo.class, response);
    }

    /**
     * 获取绩效指标详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:performanceIndicator:query")
    @GetMapping("/{id}")
    public R<PerformanceIndicatorVo> getInfo(@NotNull(message = "主键不能为空")
                                             @PathVariable Long id) {
        return R.ok(iPerformanceIndicatorService.queryById(id));
    }
}
