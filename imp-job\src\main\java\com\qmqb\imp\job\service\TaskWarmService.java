package com.qmqb.imp.job.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.Constants;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.common.enums.WarningTypeEnum;
import com.qmqb.imp.common.enums.ZtTaskStatusEnum;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.common.utils.JsonUtils;
import com.qmqb.imp.job.api.config.DingTalkConfig;
import com.qmqb.imp.system.domain.WarningRecord;
import com.qmqb.imp.system.domain.ZtTask;
import com.qmqb.imp.system.domain.bo.TaskStatusWarningParamsBO;
import com.qmqb.imp.system.domain.bo.message.DdRobotMsgBo;
import com.qmqb.imp.system.mapper.WarningRecordMapper;
import com.qmqb.imp.system.mapper.ZtTaskMapper;
import com.qmqb.imp.system.runner.HolidayUtil;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.message.IMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 禅道任务预警
 * @date 2025/7/7 17:28
 */
@Component
@Slf4j
public class TaskWarmService {

    @Resource
    private ZtTaskMapper taskMapper;

    @Resource
    private IMessageService messageService;

    @Resource
    private DingTalkConfig dingTalkConfig;

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private WarningRecordMapper warningRecordMapper;

    /**
     * 新增
     */
    public static final String ADD = "新增";

    /**
     * 减少
     */
    public static final String SUB = "减少";

    @TraceId("任务状态预警")
    @XxlJob("taskStatusWarnJobHandler")
    public ReturnT<String> taskStatusWarnJobHandler(String param) {
        try {
            XxlJobLogger.log("开始执行任务状态预警定时任务...");
            log.info("开始执行进行任务状态预警定时任务...");
            StopWatch sw = new StopWatch();
            sw.start();
            //节假日不执行定时任务
            if (HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getNowDate()))) {
                log.info("节假日不执行定时任务");
                return ReturnT.SUCCESS;
            }
            Date nowDateStart = DateUtils.dateToStart(DateUtils.getNowDate());
            List<SysUser> sysUsers = sysUserService.selectAllUser();
            List<String> usernameList = sysUsers.stream().map(SysUser::getZtUserName).collect(Collectors.toList());
            Long waitCount = CollectionUtils.isEmpty(usernameList) ? 0 : taskMapper.selectCount(new LambdaQueryWrapper<ZtTask>()
                .eq(ZtTask::getStatus, ZtTaskStatusEnum.WAIT.getValue())
                .eq(ZtTask::getIsParent, 0)
                .in(ZtTask::getAssignedTo, usernameList)
                .lt(ZtTask::getOpenedDate, DateUtils.plusDay(nowDateStart, -5)));
            Long doingCount = CollectionUtils.isEmpty(usernameList) ? 0 : taskMapper.selectCount(new LambdaQueryWrapper<ZtTask>()
                .eq(ZtTask::getStatus, ZtTaskStatusEnum.DOING.getValue())
                .eq(ZtTask::getIsParent, 0)
                .in(ZtTask::getAssignedTo, usernameList)
                .isNull(ZtTask::getActivatedDate)
                .lt(ZtTask::getRealStarted, DateUtils.plusDay(nowDateStart, -10)));
            doingCount += (CollectionUtils.isEmpty(usernameList) ? 0 : taskMapper.selectCount(new LambdaQueryWrapper<ZtTask>()
                .eq(ZtTask::getStatus, ZtTaskStatusEnum.DOING.getValue())
                .eq(ZtTask::getIsParent, 0)
                .in(ZtTask::getAssignedTo, usernameList)
                .lt(ZtTask::getActivatedDate, DateUtils.plusDay(nowDateStart, -10))));
            Long doneCount = CollectionUtils.isEmpty(usernameList) ? 0 : taskMapper.selectCount(new LambdaQueryWrapper<ZtTask>()
                .eq(ZtTask::getStatus, ZtTaskStatusEnum.DONE.getValue())
                .eq(ZtTask::getIsParent, 0)
                .in(ZtTask::getAssignedTo, usernameList)
                .lt(ZtTask::getFinishedDate, DateUtils.plusDay(nowDateStart, -5)));
            Long pauseCount = CollectionUtils.isEmpty(usernameList) ? 0 : taskMapper.selectPauseTimeoutCount(DateUtils.plusDay(nowDateStart, -15), usernameList);
            LocalDate now = LocalDate.now();
            String text = buildText(waitCount, doingCount, doneCount, pauseCount, now);
            // 记录预警
            WarningRecord record = new WarningRecord();
            record.setType(WarningTypeEnum.TASK_STATUS.getCode());
            record.setSendTime(now);
            record.setContext(text);
            record.setParams(JsonUtils.toJsonString(new TaskStatusWarningParamsBO(waitCount, doingCount, doneCount, pauseCount)));
            record.setCreateTime(new Date());
            warningRecordMapper.insert(record);
            // 发送预警
            send(dingTalkConfig.getJszxRobotUrl(), text);
            sw.stop();
            XxlJobLogger.log("任务状态预警定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            log.info("任务状态预警定时任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("任务状态预警定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    private String buildText(Long waitCount, Long doingCount, Long doneCount, Long pauseCount, LocalDate now) {
        StringBuilder content = new StringBuilder();
        List<String> diffList = new ArrayList<>();
        TaskStatusWarningParamsBO params = null;
        String text;
        if (waitCount + doingCount + doneCount + pauseCount > 0) {
            WarningRecord yesterdayWarningRecord = warningRecordMapper.selectOne(new LambdaQueryWrapper<WarningRecord>()
                .eq(WarningRecord::getType, WarningTypeEnum.TASK_STATUS.getCode())
                .eq(WarningRecord::getSendTime, now.plusDays(-1))
                .orderByDesc(WarningRecord::getCreateTime)
                .last("limit 1"));
            if (yesterdayWarningRecord != null && StringUtils.isNotBlank(yesterdayWarningRecord.getParams())) {
                params = JsonUtils.parseObject(yesterdayWarningRecord.getParams(), TaskStatusWarningParamsBO.class);
            }
            if (waitCount > 0) {
                content.append(String.format("\n有%d个状态为【需求中】的任务，已超过5天没处理，请及时进行处理，该暂停及时暂停，该取消及时取消。", waitCount));
                if (params != null) {
                    Long yesterdayWaitCount = Optional.ofNullable(params.getWaitCount()).orElse(0L);
                    if (!waitCount.equals(yesterdayWaitCount)) {
                        diffList.add(String.format("【需求中】任务未处理%s%d条", waitCount > yesterdayWaitCount ? ADD : SUB, Math.abs(waitCount - yesterdayWaitCount)));
                    }
                }
            }
            if (doingCount > 0) {
                content.append(String.format("\n有%d个状态为【进行中】的任务，已超过10天没完成，请提高工作效率进行处理。", doingCount));
                if (params != null) {
                    Long yesterdayDoingCount = Optional.ofNullable(params.getDoingCount()).orElse(0L);
                    if (!doingCount.equals(yesterdayDoingCount)) {
                        diffList.add(String.format("【进行中】任务未完成%s%d条", doingCount > yesterdayDoingCount ? ADD : SUB, Math.abs(doingCount - yesterdayDoingCount)));
                    }
                }
            }
            if (doneCount > 0) {
                content.append(String.format("\n有%d个状态为【已完成】的任务，已超过5天没关闭，请及时关闭。", doneCount));
                if (params != null) {
                    Long yesterdayDoneCount = Optional.ofNullable(params.getDoneCount()).orElse(0L);
                    if (!doneCount.equals(yesterdayDoneCount)) {
                        diffList.add(String.format("【已完成】任务未关闭%s%d条", doneCount > yesterdayDoneCount ? ADD : SUB, Math.abs(doneCount - yesterdayDoneCount)));
                    }
                }
            }
            if (pauseCount > 0) {
                content.append(String.format("\n有%d个状态为【已暂停】的任务，已超过15天没取消，请及时取消。", pauseCount));
                if (params != null) {
                    Long yesterdayPauseCount = Optional.ofNullable(params.getPauseCount()).orElse(0L);
                    if (!pauseCount.equals(yesterdayPauseCount)) {
                        diffList.add(String.format("【已暂停】任务未取消%s%d条。", pauseCount > yesterdayPauseCount ? ADD : SUB, Math.abs(pauseCount - yesterdayPauseCount)));
                    }
                }
            }
            Map<String, String> map = new HashMap<>(10);
            if (diffList.isEmpty()) {
                map.put("content", content.toString());
            } else {
                map.put("content", content + "\n对比昨天：" + StringUtils.join(diffList, "，") + "。");
            }
            text = StrUtil.format(Constants.TASK_STATUS_TIMEOUT_TAG, map);
        } else {
            text = Constants.TASK_STATUS_NOT_TIMEOUT_TAG;
        }
        return text;
    }

    /**
     * 发送预警
     *
     * @param robotUrl 机器人url
     * @param content  内容
     */
    private void send(String robotUrl, String content) {
        DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder()
            .url(robotUrl)
            .msgtype("text")
            .content(content)
            .build();
        robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
        try {
            messageService.sendBase(robotMsgBo);
        } catch (Exception e) {
            log.error("调用消息中心发送消息异常。异常信息：{}", e.getMessage());
        }
    }

}
