package com.qmqb.imp.web.controller.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzed.structure.tool.valid.group.QueryGroup;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.core.domain.vo.WarnSelectedGroupVO;
import com.qmqb.imp.common.core.domain.vo.WarnSelectedVO;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.enums.WarnHandleStatusEnum;
import com.qmqb.imp.common.enums.WarnLevelEnum;
import com.qmqb.imp.common.enums.WarnTypeEnum;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.bo.*;
import com.qmqb.imp.system.domain.vo.*;
import com.qmqb.imp.system.service.WarnAnalysisService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 预警记录controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/warn/query")
public class WarnRecordController extends BaseController {

    @Autowired
    private WarnAnalysisService warnAnalysisService;

    @GetMapping("/getDeptWarnAnalysisList")
    public R<DepartmentWarnAnalysisVo> getDeptWarnAnalysisList(DeptWarnAnalysisBo request) {
        return R.ok(warnAnalysisService.getDeptWarnAnalysisList(request));
    }

    /**
     * 个人预警分析
     *
     * @param request
     * @return
     */
    @GetMapping("/getPersonslAnalysisList")
    public R<PersonalAnalysisVo> getPersonslAnalysisList(PersonalAnalysisBo request) {
        return R.ok(warnAnalysisService.getPersonslAnalysisList(request));
    }

    /**
     * 组内成员预警分析
     *
     * @param request
     * @return
     */
    @GetMapping("/getGroupAnalysisList")
    public R<GroupAnalysisVo> getGroupAnalysisList(WarnAnalysisBo request) {
        return R.ok(warnAnalysisService.getGroupAnalysisList(request));
    }


    @GetMapping("/getWarnPersonalPage")
    public R<Page<WarningAllVO>> getWarnPersonalPage(WarningBaseBO request) {
        LoginUser user = getLoginUser();
        return R.ok(warnAnalysisService.getWarnPersonalPage(request, user));
    }

    @GetMapping("/getWarnGroupsPage")
    public R<Page<WarningAllVO>> getWarnGroupsPage(WarningBaseBO request) {
        LoginUser user = getLoginUser();
        return R.ok(warnAnalysisService.getWarnGroupsPage(request, user));
    }

    @GetMapping("/getWarnAllPage")
    public R<Page<WarningAllVO>> getWarnAllPage(WarningBaseAllBO request) {
        return R.ok(warnAnalysisService.getWarnAllPage(request));
    }

    @GetMapping("/getWarnAllPageByUser")
    public R<Page<WarningAllVO>> getWarnAllPageByUser(@Validated(QueryGroup.class) WorkDetailBo workDetail) {
        return R.ok(warnAnalysisService.getWarnAllPageByUser(workDetail));
    }

    /**
     * 导出个人预警
     * @param workDetail
     * @param response
     */
    @PostMapping("/exportUserWarn")
    public void exportUserWarn(WorkDetailBo workDetail, HttpServletResponse response) {
        workDetail.setPageSize(Integer.MAX_VALUE);
        Page<WarningAllVO> page = warnAnalysisService.getWarnAllPageByUser(workDetail);
        List<WarningAllVO> records = page.getRecords();
        List<WarningAllExportVO> exportList = records.stream().map(warningAllVO -> {
            WarningAllExportVO exportVO = new WarningAllExportVO();
            BeanUtils.copyProperties(warningAllVO, exportVO);
            return exportVO;
        }).collect(Collectors.toList());
        ExcelUtil.exportCsv(exportList, "个人全部预警", WarningAllExportVO.class, response);
    }

    /**
     * 导出角色信息列表
     */
    @Log(title = "全部预警", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WarningBaseAllBO request, HttpServletResponse response) {
        // 导出全部预警
        request.setPageSize(Integer.MAX_VALUE);
        Page<WarningAllVO> list = warnAnalysisService.getWarnAllPage(request);
        List<WarningAllVO> exportList = list.getRecords();

        // WarningAllVO转化成WarningAllExportVO
        List<WarningAllExportVO> exportVOList = exportList.stream().map(warningAllVO -> {
            WarningAllExportVO exportVO = new WarningAllExportVO();
            BeanUtils.copyProperties(warningAllVO, exportVO);
            return exportVO;
        }).collect(Collectors.toList());

        ExcelUtil.exportExcel(exportVOList, "全部预警数据", WarningAllExportVO.class, response);
    }

    @GetMapping("/getWarnLevelSelects")
    public R<List<WarnSelectedVO>> getWarnLevelSelects() {
        return R.ok(WarnLevelEnum.getWarnLevels());
    }

    @GetMapping("/getWarnTypeSelets")
    public R<List<WarnSelectedVO>> getWarnTypeSelets() {
        return R.ok(WarnTypeEnum.getWarnTypes());
    }

    @GetMapping("/getWarnHandleSelets")
    public R<List<WarnSelectedVO>> getWarnHandleSelets() {
        return R.ok(WarnHandleStatusEnum.getWarnHandleStatus());
    }

    @GetMapping("/getWarnGroupSelets")
    public R<List<WarnSelectedGroupVO>> getWarnGroupSelets() {
        return R.ok(warnAnalysisService.getWarnGroupSelets());
    }

    @PostMapping("/updateHandleContent")
    public void updateHandleContent(WarnHandleContentBo contentBo) {
        warnAnalysisService.updateHandleContent(contentBo);
    }
}
