<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.ZtUserMapper">

    <resultMap type="com.qmqb.imp.system.domain.ZtUser" id="ZtUserResult">
        <result property="id" column="id"/>
        <result property="company" column="company"/>
        <result property="dept" column="dept"/>
        <result property="account" column="account"/>
        <result property="type" column="type"/>
        <result property="password" column="password"/>
        <result property="role" column="role"/>
        <result property="realname" column="realname"/>
        <result property="pinyin" column="pinyin"/>
        <result property="nickname" column="nickname"/>
        <result property="commiter" column="commiter"/>
        <result property="avatar" column="avatar"/>
        <result property="birthday" column="birthday"/>
        <result property="gender" column="gender"/>
        <result property="email" column="email"/>
        <result property="skype" column="skype"/>
        <result property="qq" column="qq"/>
        <result property="mobile" column="mobile"/>
        <result property="phone" column="phone"/>
        <result property="weixin" column="weixin"/>
        <result property="dingding" column="dingding"/>
        <result property="slack" column="slack"/>
        <result property="whatsapp" column="whatsapp"/>
        <result property="address" column="address"/>
        <result property="zipcode" column="zipcode"/>
        <result property="nature" column="nature"/>
        <result property="analysis" column="analysis"/>
        <result property="strategy" column="strategy"/>
        <result property="join" column="join"/>
        <result property="visits" column="visits"/>
        <result property="ip" column="ip"/>
        <result property="last" column="last"/>
        <result property="fails" column="fails"/>
        <result property="locked" column="locked"/>
        <result property="feedback" column="feedback"/>
        <result property="ranzhi" column="ranzhi"/>
        <result property="ldap" column="ldap"/>
        <result property="score" column="score"/>
        <result property="scoreLevel" column="scoreLevel"/>
        <result property="deleted" column="deleted"/>
        <result property="clientStatus" column="clientStatus"/>
        <result property="clientLang" column="clientLang"/>
    </resultMap>


</mapper>
