package com.qmqb.imp.system.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025-04-28 09:30
 */

@Data
public class TaskExportVO {

    @ExcelProperty(value = "ID")
    private Integer id;

    @ExcelProperty(value = "所属项目")
    private String pName;

    @ExcelProperty(value = "需求标题")
    private String title;

    @ExcelProperty(value = "主任务名称")
    private String name;

    @ExcelProperty(value = "子任务名称")
    private String sonName;

    @ExcelProperty(value = "当前状态")
    private String status;

    @ExcelProperty(value = "类型")
    private String type;

    @ExcelProperty(value = "任务启动人")
    private String taskStarter;

    @ExcelProperty(value = "任务启动时间")
    private Date realStarted;

    @ExcelProperty(value = "任务完成人")
    private String finishedby;

    @ExcelProperty(value = "任务完成时间")
    private Date finishedDate;

    @ExcelProperty(value = "消耗时间")
    private Float consumed;

    @ExcelProperty(value = "关闭人")
    private String closedby;

    @ExcelProperty(value = "关闭时间")
    private Date closeddate;

    @ExcelProperty(value = "关闭原因")
    private String closedreason;



}
