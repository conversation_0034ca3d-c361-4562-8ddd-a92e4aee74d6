package com.qmqb.imp.job.indicator.indicators.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.constant.Constants;
import com.qmqb.imp.common.enums.DingTalkProcessInstanceResultEnum;
import com.qmqb.imp.common.enums.DingTalkProcessInstanceStatusEnum;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.bo.UserLeaveBo;
import com.qmqb.imp.system.domain.bo.process.ProcessMaintenanceEnvironmentBo;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.domain.vo.UserLeaveVo;
import com.qmqb.imp.system.domain.vo.process.ProcessMaintenanceEnvironmentVo;
import com.qmqb.imp.system.service.IUserKqStatService;
import com.qmqb.imp.system.service.IUserLeaveService;
import com.qmqb.imp.system.service.process.IProcessMaintenanceEnvironmentService;
import lombok.Builder;
import lombok.Data;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 月计划性维护数指标等级计算
 *
 * <AUTHOR>
 */
@Service
public class MaintenanceIndicatorServiceImpl implements IndicatorLevelCalcService {
    @Resource
    private IProcessMaintenanceEnvironmentService processMaintenanceEnvironmentService;

    @Resource
    private IUserKqStatService iUserKqStatService;

    /**
     * S级标准：至少创建并执行10个
     */
    private static final int S_LEVEL_MAINTENANCE_COUNT = 10;

    /**
     * A级标准：至少创建并执行5个%
     */
    private static final int A_LEVEL_MAINTENANCE_COUNT = 5;

    /**
     * B级标准：至少创建并执行2个
     */
    private static final int B_LEVEL_MAINTENANCE_COUNT = 2;

    /**
     * C级标准：当月无维护
     */
    private static final int C_LEVEL_MAINTENANCE_COUNT = 0;

    @Override
    public String getIndicatorCode() {
        return PerformanceIndicatorEnum.PLAN_MAINTAIN_COUNT.getCode();
    }

    @Override
    public IndicatorCalcResult calcLevel(String nickName, Integer month, List<TrackWorkResultVO> trackWorkResults) {
        Optional<TrackWorkResultVO> userWorkResultOpt = trackWorkResults.stream()
            .filter(r -> nickName.equals(r.getWorkUsername()))
            .findFirst();

        if (!userWorkResultOpt.isPresent()) {
            String indicatorName = "未知指标";
            try {
                indicatorName = PerformanceIndicatorEnum.valueOf(getIndicatorCode().toUpperCase()).getName();
            } catch (IllegalArgumentException e) {
                // Ignore if enum not found
            }
            return new IndicatorCalcResult(ScoreLevelEnum.SCORE_C.getCode(),
                String.format("员工[%s]在%s月份的%s数据未找到", nickName, month, indicatorName));
        }

        TrackWorkResultVO userWorkResult = userWorkResultOpt.get();
        List<ProcessMaintenanceEnvironmentVo> data = new ArrayList<>();
        String level = calculateLevel(userWorkResult, data, nickName);
        String logContent = createLogContent(userWorkResult, nickName, level, data);

        return new IndicatorCalcResult(level, logContent);
    }

    public String calculateLevel(TrackWorkResultVO workResult,List<ProcessMaintenanceEnvironmentVo> data, String nickName) {
        // 获取年和月
        int year = workResult.getWorkYear();
        int month = workResult.getWorkMonth();
        Calendar cal = Calendar.getInstance();
        cal.clear();
        cal.set(Calendar.YEAR, year);
        // Calendar的月份从0开始
        cal.set(Calendar.MONTH, month - 1);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        // 获取当月时间
        Date currentMouth = cal.getTime();
        // 获取两个月的数据
        cal.add(Calendar.MONTH, -1);
        // 生成上月第一天0点
        Date startDate = cal.getTime();

        // 生成下月第一天0点，再减1毫秒得到本月最后一毫秒
        cal.set(Calendar.MONTH, month);
        Date endDate = new Date(cal.getTimeInMillis() - 1);
        // 是否请假超过5天
        Boolean userLeaveOverWeek = iUserKqStatService.userLeaveOverWeek(year, month, nickName);
        // 两个月的维护数
        List<ProcessMaintenanceEnvironmentVo> processMaintenanceEnvironmentVos = processMaintenanceEnvironmentService.queryList(ProcessMaintenanceEnvironmentBo.builder().originatorUser(nickName).originatorTimeRange(new Date[]{startDate, endDate}).result(DingTalkProcessInstanceResultEnum.AGREE.getSearchValue()).build());
        // 当月维护数
        List<ProcessMaintenanceEnvironmentVo> currentProcess = processMaintenanceEnvironmentVos.stream().filter(processMaintenanceEnvironmentVo -> !processMaintenanceEnvironmentVo.getOriginatorTime().before(currentMouth) && !processMaintenanceEnvironmentVo.getOriginatorTime().after(endDate)).collect(Collectors.toList());
        data.addAll(currentProcess);
        // 两个月为空
        if (CollectionUtil.isEmpty(processMaintenanceEnvironmentVos) ) {
            if (userLeaveOverWeek) {
                return ScoreLevelEnum.SCORE_B.getCode();
            }
            return ScoreLevelEnum.SCORE_D.getCode();
        }else if (CollectionUtil.isEmpty(currentProcess)) {
            if (userLeaveOverWeek) {
                return ScoreLevelEnum.SCORE_B.getCode();
            }
            return ScoreLevelEnum.SCORE_C.getCode();
        }else if (currentProcess.size() >= S_LEVEL_MAINTENANCE_COUNT) {
            return ScoreLevelEnum.SCORE_S.getCode();
        }else if (currentProcess.size() >= A_LEVEL_MAINTENANCE_COUNT) {
            return ScoreLevelEnum.SCORE_A.getCode();
        }else if (currentProcess.size() < B_LEVEL_MAINTENANCE_COUNT) {
            return ScoreLevelEnum.SCORE_C.getCode();
        }else {
            return ScoreLevelEnum.SCORE_B.getCode();
        }
    }

    @Override
    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults) {
        return "";
    }

    @Override
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level) {
        return "";
    }

    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level, List<ProcessMaintenanceEnvironmentVo> data) {
        Integer year = workResult.getWorkYear();
        Integer month = workResult.getWorkMonth();
        int size =  data == null? 0 :data.size();
        StringBuilder logContent = new StringBuilder();
        logContent.append(String.format("[%s]在%s年%s月份月计划性维护数: %s个",
            nickName, year, month, size));
        logContent.append(String.format("，评级：%s", level));
        String reason = getRatingReason(level, size);
        if (reason != null) {
            logContent.append(String.format("，原因：%s", reason));
        }
        return logContent.toString();
    }
    /**
     * 获取评级原因
     */
    private String getRatingReason(String level, int size) {
        switch (level) {
            case "S":
                return String.format("月计划性维护数(%d个)达到S级标准(≥%d个)", size, S_LEVEL_MAINTENANCE_COUNT);
            case "A":
                return String.format("月计划性维护数(%d个)达到A级标准(≥%d个)",size, A_LEVEL_MAINTENANCE_COUNT);
            case "B":
                return String.format("月计划性维护数(%d个)达到B级标准(≥%d个)",size, B_LEVEL_MAINTENANCE_COUNT);
            case "C":
                return "月计划性维护数达到C级标准(本月无维护)";
            case "D":
                return "月计划性维护数达到D级标准(两个月连续无维护)";
            default:
                return null;
        }
    }
}
