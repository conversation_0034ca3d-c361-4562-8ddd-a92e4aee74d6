package com.qmqb.imp.job.service;

import com.qmqb.imp.job.indicator.PerformanceGenerator;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

/**
 * 组长绩效指标计算定时任务
 * 专门负责计算组长的绩效指标，需要在组员绩效指标计算完成后执行
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ManagerPerformanceIndicatorTask {

    @Autowired
    private PerformanceGenerator performanceGenerator;

    private final String separator = ",";

    private final String dateSeparator = "-";

    /**
     * 计算组长绩效指标
     * <p>
     * 可通过任务参数指定年月和/或部门ID，支持以下格式：
     * - "2024-01" : 处理2024年1月所有部门的组长绩效指标
     * - "123" : 处理上个月部门ID为123的组长绩效指标
     * - "2024-01,123" : 处理2024年1月部门ID为123的组长绩效指标
     * - 空参数 : 处理上个月所有部门的组长绩效指标
     *
     * 注意：此任务必须在以下任务完成后执行：
     * 1. 组员绩效指标计算完成
     * 2. 组长绩效反馈生成完成
     */
    @XxlJob("calcManagerPerformanceIndicatorJobHandler")
    public ReturnT<String> calcManagerPerformanceIndicator(String param) {
        YearMonth yearMonth;
        Long deptId = null;

        if (StringUtils.isNotEmpty(param)) {
            try {
                // 解析参数：支持 yyyy-MM, 123, yyyy-MM,123 等格式
                if (param.contains(separator)) {
                    // 格式：yyyy-MM,deptId
                    String[] parts = param.split(separator);
                    String dateStr = parts[0];
                    yearMonth = YearMonth.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM"));

                    if (parts.length > 1 && StringUtils.isNotBlank(parts[1])) {
                        try {
                            deptId = Long.parseLong(parts[1]);
                            log.info("使用参数指定的年月和部门：{}-{}，部门ID：{}",
                                yearMonth.getYear(), yearMonth.getMonthValue(), deptId);
                        } catch (NumberFormatException e) {
                            log.error("部门ID参数格式错误：{}", parts[1]);
                            return new ReturnT<>(ReturnT.FAIL_CODE, "部门ID参数格式错误，请检查参数格式");
                        }
                    } else {
                        log.info("使用参数指定的年月：{}-{}，处理所有部门",
                            yearMonth.getYear(), yearMonth.getMonthValue());
                    }
                } else if (param.contains(dateSeparator)) {
                    // 格式：yyyy-MM（只指定年月）
                    yearMonth = YearMonth.parse(param, DateTimeFormatter.ofPattern("yyyy-MM"));
                    log.info("使用参数指定的年月：{}-{}，处理所有部门",
                        yearMonth.getYear(), yearMonth.getMonthValue());
                } else {
                    // 格式：123（只指定部门ID）
                    try {
                        deptId = Long.parseLong(param);
                        yearMonth = YearMonth.from(LocalDate.now().minusMonths(1));
                        log.info("使用默认年月（上个月）：{}-{}，参数指定部门ID：{}",
                            yearMonth.getYear(), yearMonth.getMonthValue(), deptId);
                    } catch (NumberFormatException e) {
                        log.error("参数格式错误，无法解析为部门ID：{}", param);
                        return new ReturnT<>(ReturnT.FAIL_CODE, "参数格式错误，请使用 'yyyy-MM'、'123' 或 'yyyy-MM,123' 格式");
                    }
                }
            } catch (Exception e) {
                log.error("参数格式错误，请使用 'yyyy-MM'、'123' 或 'yyyy-MM,123' 格式", e);
                return new ReturnT<>(ReturnT.FAIL_CODE, "参数格式错误，请使用 'yyyy-MM'、'123' 或 'yyyy-MM,123' 格式");
            }
        } else {
            yearMonth = YearMonth.from(LocalDate.now().minusMonths(1));
            log.info("使用默认年月（上个月）：{}-{}，处理所有部门",
                yearMonth.getYear(), yearMonth.getMonthValue());
        }

        int year = yearMonth.getYear();
        int month = yearMonth.getMonthValue();

        log.info("开始计算 {} 年 {} 月的组长绩效指标{}", year, month,
            deptId != null ? "，部门ID：" + deptId : "，所有部门");
        try {
            // 计算组长绩效指标（personType = 1 表示组长）
            // 注意：删除逻辑已经集成在performanceGenerator内部
            performanceGenerator.generatePerformanceFromFeedbackAndDept(year, month, 1, deptId);

            log.info("组长绩效指标计算完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("组长绩效指标计算失败", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, e.getMessage());
        }
    }
}
