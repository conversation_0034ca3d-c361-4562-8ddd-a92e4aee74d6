package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 禅道文档内容对象 zt_doccontent
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@Data
@TableName("zt_doccontent")
public class ZtDocContent {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 文档ID
     */
    private Integer doc;

    /**
     * 标题
     */
    private String title;

    /**
     * 摘要
     */
    private String digest;

    /**
     * 内容
     */
    private String content;

    /**
     * HTML内容
     */
    private String html;

    /**
     * 原始内容
     */
    @TableField("rawContent")
    private String rawContent;

    /**
     * 文件
     */
    private String files;

    /**
     * 类型
     */
    private String type;

    /**
     * 添加人
     */
    @TableField("addedBy")
    private String addedBy;

    /**
     * 添加时间
     */
    @TableField("addedDate")
    private Date addedDate;

    /**
     * 编辑人
     */
    @TableField("editedBy")
    private String editedBy;

    /**
     * 编辑时间
     */
    @TableField("editedDate")
    private Date editedDate;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 来源版本
     */
    @TableField("fromVersion")
    private Integer fromVersion;
} 