package com.qmqb.imp.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.SlowQueryLog;
import com.qmqb.imp.system.domain.SlowQueryStats;
import com.qmqb.imp.system.domain.vo.SlowQueryDetailVO;
import com.qmqb.imp.system.domain.vo.SlowQueryLogVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 慢sql信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@DS(DataSource.SLOWSQL)
public interface SlowQueryLogMapper extends BaseMapperPlus<SlowQueryLogMapper, SlowQueryLog, SlowQueryLogVo> {


    /**
     * 通过hash值查找sql文本
     *
     * @param sqlHashList
     * @return
     */
    List<SlowQueryStats> listSqlTextBySqlHash(@Param("sqlHashList") List<String> sqlHashList);

    /**
     * 分页获取慢sql详细信息
     *
     * @param build
     * @param sqlHash
     * @return
     */
    Page<SlowQueryDetailVO> slowSqlDetailPage(@Param("page") Page<Object> build, @Param("sqlHash") String sqlHash);

}
