@startuml

participant JOB模块
participant 钉钉OpenAPI

== 考勤异常预警定时任务 ==
JOB模块 -> JOB模块 : 1、由于调用钉钉OpenAPI最长的时间范围是7天，所以根据当前日期对时间范围\n     进行按周分割。\n2、获取考勤预警的配置，根据配置信息获取到要预警的用户列表。
JOB模块 -> 钉钉OpenAPI : 3、根据用户列表按照size为50进行分割，以每周为单位的时间范围遍历查询用户\n     的考勤信息。
钉钉OpenAPI -> JOB模块 : 4、返回所有用户的考勤信息。
JOB模块 -> JOB模块 : 5、根据用户的考勤信息筛选迟到的用户信息并组合成map。\n6、根据map获取迟到次数超过5、10、15次的用户集合。\n7、对用户进行去重操作，避免对不同预警级别的用户重复预警。\n8、调用封装好的公共预警类触发预警，钉钉触达到需要各用户。

== 进行中的任务没更新预警定时任务 ==
JOB模块 -> JOB模块 : 1、获取3、5、10个工作日之前的日期。\n2、获取进行中的任务没更新的预警配置。\n3、根据配置信息获取到要预警的用户列表。\n4、根据条件：用户列表查询、任务状态为doing、截止时间来查询要预警的进行中\n     的任务。\n5、对获取到的任务进行筛选，筛选条件是最后编辑人跟指派对象一致。\n6、根据任务的最后编辑人获取到要预警的用户列表。\n7、对各预警级别的用户列表进行去重操作，防止重复预警。\n8、调用封装好的公共预警类触发预警，钉钉触达到需要各用户。

== 连续几个工作日没进行任务预警定时任务 ==
JOB模块 -> JOB模块 : 1、获取3、5、10个工作日之前的日期。\n2、获取连续几个工作日没进行任务的预警配置。\n3、根据配置信息获取到要预警的用户列表。\n4、根据获取的用户列表，首先查询用户是否有进行中的任务，有进行中的任务则\n     不符合此预警逻辑，若无进行中的任务，查询用户指定时间内(如3、5、10个工\n     作日内)有没有完成或关闭的任务，若有则不发相应预警，若无则预警。\n5、对各预警级别的用户列表进行去重操作，防止重复预警。\n6、调用封装好的公共预警类触发预警，钉钉触达到需要各用户。

@enduml
