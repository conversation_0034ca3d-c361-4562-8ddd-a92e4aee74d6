package com.qmqb.imp.system.domain.vo;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 【请填写功能名称】视图对象 zt_user
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@ExcelIgnoreUnannotated
public class ZtUserVo {

    private static final long serialVersionUID = 1L;


    @ExcelProperty(value = "")
    private Integer id;


    @ExcelProperty(value = "")
    private Integer company;

    @ExcelProperty(value = "")
    private Integer dept;

    @ExcelProperty(value = "")
    private String account;

    @ExcelProperty(value = "")
    private String type;

    @ExcelProperty(value = "")
    private String password;

    @ExcelProperty(value = "")
    private String role;

    @ExcelProperty(value = "")
    private String realname;

    @ExcelProperty(value = "")
    private String pinyin;

    @ExcelProperty(value = "")
    private String nickname;

    @ExcelProperty(value = "")
    private String commiter;

    @ExcelProperty(value = "")
    private String avatar;

    @ExcelProperty(value = "")
    private Date birthday;

    @ExcelProperty(value = "")
    private String gender;

    @ExcelProperty(value = "")
    private String email;

    @ExcelProperty(value = "")
    private String skype;

    @ExcelProperty(value = "")
    private String qq;

    @ExcelProperty(value = "")
    private String mobile;

    @ExcelProperty(value = "")
    private String phone;

    @ExcelProperty(value = "")
    private String weixin;

    @ExcelProperty(value = "")
    private String dingding;

    @ExcelProperty(value = "")
    private String slack;

    @ExcelProperty(value = "")
    private String whatsapp;

    @ExcelProperty(value = "")
    private String address;

    @ExcelProperty(value = "")
    private String zipcode;

    @ExcelProperty(value = "")
    private String nature;

    @ExcelProperty(value = "")
    private String analysis;

    @ExcelProperty(value = "")
    private String strategy;

    @ExcelProperty(value = "")
    private Date join;

    @ExcelProperty(value = "")
    private Integer visits;

    @ExcelProperty(value = "")
    private String ip;

    @ExcelProperty(value = "")
    private Integer last;

    @ExcelProperty(value = "")
    private Integer fails;

    @ExcelProperty(value = "")
    private Date locked;

    @ExcelProperty(value = "")
    private String feedback;

    @ExcelProperty(value = "")
    private String ranzhi;

    @ExcelProperty(value = "")
    private String ldap;

    @ExcelProperty(value = "")
    private Long score;

    @ExcelProperty(value = "")
    private Long scoreLevel;

    @ExcelProperty(value = "")
    private String deleted;

    @ExcelProperty(value = "")
    private String clientStatus;

    @ExcelProperty(value = "")
    private String clientLang;


}
