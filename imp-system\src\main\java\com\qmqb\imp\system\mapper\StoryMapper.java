package com.qmqb.imp.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.common.core.domain.dto.StoryQueryDTO;
import com.qmqb.imp.system.domain.Story;
import com.qmqb.imp.system.domain.vo.StoryReleaseCountVO;
import com.qmqb.imp.system.domain.vo.StoryVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@Repository
@DS(DataSource.ZENTAO)
public interface StoryMapper extends BaseMapper<Story> {


    /**
     * 获取查询需求任务
     *
     * @param page
     * @param request
     * @return
     */
    Page<StoryVO> getLists(@Param("page") Page<StoryVO> page, @Param("request") StoryQueryDTO request);

    /**
     * 根据状态查询数量
     *
     * @param status
     * @param beginTime
     * @param endTime
     * @param usernameList
     * @return
     */
    Long selectStoryCountByStatus(@Param("status") String status, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime, List<String> usernameList);

    /**
     * 需求发布数
     *
     * @param storyList
     * @return
     */
    List<StoryReleaseCountVO> storyReleaseCount(@Param("list") List<Integer> storyList);

    /**
     * 没有案例的需求数量
     *
     * @param startTime
     * @param endTime
     * @return
     */
    Integer selectNotCaseStoryCount(@Param("startTime") LocalDate startTime, @Param("endTime") LocalDate endTime);

    /**
     * 根据最后更新时间查询需求
     * @return
     */
    List<StoryVO> getStoryListByDetail();
}
