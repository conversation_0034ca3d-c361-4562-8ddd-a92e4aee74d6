package com.qmqb.imp.system.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 分支详细信息对象 tb_branch
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
@Data
public class SlowQueryTopVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Schema(description = "id")
    private Long id;
    /**
     * 同类SQL的HASH值
     */
    @Schema(description = "同类SQL的HASH值")
    private String sqlHash;
    /**
     * query_times表示执行次数，sum_time表示总毫秒数，avg_time表示平均毫秒数
     */
    @Schema(description = "query_times表示执行次数，sum_time表示总毫秒数，avg_time表示平均毫秒数")
    private String type;
    /**
     * 次数
     */
    @Schema(description = "次数")
    private Long nums;


}
