package com.qmqb.imp.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qmqb.imp.system.domain.SlowQueryStats;
import com.qmqb.imp.system.domain.vo.SlowSqlStatVO;

import java.util.List;


/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10 14:29:51
 */
public interface ISlowQueryStatsService extends IService<SlowQueryStats> {


    /**
     * 获取各月慢sql数量统计
     *
     * @param year
     * @param status
     * @return
     */
    List<SlowSqlStatVO> slowSqlStat(Integer year,Integer status);
}
