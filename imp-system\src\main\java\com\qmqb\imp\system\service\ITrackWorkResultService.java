package com.qmqb.imp.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.bo.TrackWorkResultBO;
import com.qmqb.imp.system.domain.vo.PerformanceDataVO;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;

import java.util.List;

/**
 * 工作成果跟踪
 * <AUTHOR>
 * @since 2023-08-21
 */
public interface ITrackWorkResultService {

    /**
     * 工作成果跟踪列表
     * @param request
     * @return
     */
    TableDataInfo<TrackWorkResultVO> list(TrackWorkResultBO request);

    /**
     * 工作成果跟踪列表
     *
     * @param request
     * @param page
     * @param groupIdList
     * @param year
     * @param month
     * @return
     */
    TableDataInfo<TrackWorkResultVO> getTrackWorkResultVoTableDataInfo(TrackWorkResultBO request, Page<TrackWorkResultVO> page, List<Long> groupIdList, Integer year, Integer month);

    /**
     * 工作成果跟踪列表
     *
     * @param request
     * @return
     */
    PerformanceDataVO listOpenApi(TrackWorkResultBO request);
}
