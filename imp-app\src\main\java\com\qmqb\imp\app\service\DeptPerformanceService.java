package com.qmqb.imp.app.service;

import com.qmqb.imp.app.domain.bo.DateBO;
import com.qmqb.imp.app.domain.vo.MessageInfoVO;
import com.qmqb.imp.app.domain.vo.PerformancePublicListVO;
import com.qmqb.imp.app.domain.vo.PerformanceReportVO;
import com.qmqb.imp.common.core.domain.R;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
public interface DeptPerformanceService {
    /**
     * 消息处理情况
     *
     * @return
     */
    R<MessageInfoVO> messageInfo();

    /**
     * 部门绩效报告
     *
     * @param bo
     * @return
     */
    R<PerformanceReportVO> deptPerformanceReport(DateBO bo);

    /**
     * 最新绩效公示名单
     *
     * @return
     */
    R<PerformancePublicListVO> performancePublicList();
}
