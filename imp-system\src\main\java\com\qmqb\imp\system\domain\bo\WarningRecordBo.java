package com.qmqb.imp.system.domain.bo;

import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 【请填写功能名称】业务对象 tb_warning_record
 *
 * <AUTHOR>
 * @date 2025-08-06
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class WarningRecordBo extends BaseEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 预警类型
     */
    private String type;

    /**
     * 发送时间
     */
    private LocalDate sendTime;


}
