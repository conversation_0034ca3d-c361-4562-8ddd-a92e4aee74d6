package com.qmqb.imp.app.service.impl;

import com.hzed.structure.common.util.BeanUtil;
import com.qmqb.imp.app.domain.bo.WarnHandleBO;
import com.qmqb.imp.app.domain.bo.WarnListBO;
import com.qmqb.imp.app.domain.vo.WarnDetailVO;
import com.qmqb.imp.app.service.WarningPageService;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.common.utils.BeanCopyUtils;
import com.qmqb.imp.system.domain.bo.WarnListDTO;
import com.qmqb.imp.system.domain.bo.WarnRecordDetailBo;
import com.qmqb.imp.system.domain.dto.WarnDetailDTO;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.IWarnRecordDetailService;
import com.qmqb.imp.system.service.IWarnRecordService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WarningPageServiceImpl implements WarningPageService {

    private final IWarnRecordService warnRecordService;
    private final IWarnRecordDetailService warnRecordDetailService;
    private final ISysUserService sysUserService;

    /**
     * 个人预警列表
     *
     * @param bo
     * @return
     */
    @Override
    public R<List<WarnDetailVO>> list(WarnListBO bo) {
        Long userId = LoginHelper.getUserId();
        WarnListDTO warnListDTO = BeanUtil.copyProperties(bo, WarnListDTO.class);
        List<WarnDetailDTO> detailList = warnRecordService.listByUserIdList(Collections.singletonList(userId), warnListDTO);
        return R.ok(BeanCopyUtils.copyList(detailList, WarnDetailVO.class));
    }

    /**
     * 预警处理
     *
     * @param bo
     * @return
     */
    @Override
    public R<Void> handle(WarnHandleBO bo) {
        WarnRecordDetailBo detailBo = WarnRecordDetailBo.builder().handleContent(bo.getContent()).warnRecordId(bo.getId()).build();
        warnRecordDetailService.updateByWarnRecordId(detailBo);
        return R.ok();
    }

    /**
     * 全部预警列表
     *
     * @param bo
     * @return
     */
    @Override
    public R<List<WarnDetailVO>> all(WarnListBO bo) {
        List<SysUser> sysUsers;
        if (Objects.nonNull(bo.getGroup())) {
            sysUsers = sysUserService.selectUserByDeptId(bo.getGroup().longValue());
        } else {
            sysUsers = sysUserService.selectUserByDeptId(LoginHelper.getDeptId());
        }
        List<Long> userIds = sysUsers.stream().map(SysUser::getUserId).collect(Collectors.toList());
        WarnListDTO warnListDTO = BeanUtil.copyProperties(bo, WarnListDTO.class);
        List<WarnDetailDTO> detailDTOS = warnRecordService.listByUserIdList(userIds, warnListDTO);
        return R.ok(BeanCopyUtils.copyList(detailDTOS, WarnDetailVO.class));
    }
}
