package com.qmqb.imp.web.controller.business;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.system.domain.BugStatisticParamsBO;
import com.qmqb.imp.system.domain.bo.BugQueryParamsBO;
import com.qmqb.imp.system.domain.vo.BugPageVO;
import com.qmqb.imp.system.domain.vo.BugStatisticByProductVO;
import com.qmqb.imp.system.domain.vo.BugStatisticByProjectVO;
import com.qmqb.imp.system.domain.vo.BugStatisticByTypeVO;
import com.qmqb.imp.system.service.IBugService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @description BugController
 * @date 2025/2/21 18:04
 */
@RestController
@RequestMapping("/bug")
public class BugController {


    @Resource
    private IBugService iBugService;

    /**
     * bug分页查询
     *
     * @return
     */
    @GetMapping("/page")
    public R<IPage<BugPageVO>> page(BugQueryParamsBO paramsBO) {
        return R.ok(iBugService.page(paramsBO));
    }

    /**
     * 根据类型统计
     *
     * @return
     */
    @GetMapping("/statisticByType")
    public R<BugStatisticByTypeVO> statisticByType(BugStatisticParamsBO paramsBO) {
        return R.ok(iBugService.statisticByType(paramsBO));
    }


    /**
     * 根据类型统计
     *
     * @return
     */
    @GetMapping("/statisticByTypeAndOther")
    public R<List<BugStatisticByTypeVO.TypeBugStatisticItem>> statisticByTypeAndOther(BugStatisticParamsBO paramsBO) {
        return R.ok(iBugService.statisticItemByType(paramsBO));
    }


    /**
     * 根据产品统计
     *
     * @return
     */
    @GetMapping("/statisticByProduct")
    public R<BugStatisticByProductVO> statisticByProduct(BugStatisticParamsBO paramsBO) {
        return R.ok(iBugService.statisticByProduct(paramsBO));
    }

    /**
     * 根据项目统计
     *
     * @return
     */
    @GetMapping("/statisticByProject")
    public R<BugStatisticByProjectVO> statisticByProject(BugStatisticParamsBO paramsBO) {
        return R.ok(iBugService.statisticByProject(paramsBO));
    }

    /**
     * 根据类型统计导出
     *
     * @return
     */
    @PostMapping("/statisticByType/export")
    public void statisticByTypeExport(BugStatisticParamsBO paramsBO,HttpServletResponse response) {
        iBugService.statisticByTypeExport(paramsBO,response);
    }


    /**
     * 根据产品统计导出
     *
     * @return
     */
    @PostMapping("/statisticByProduct/export")
    public void statisticByProductExport(BugStatisticParamsBO paramsBO,HttpServletResponse response) {
        iBugService.statisticByProductExport(paramsBO,response);
    }

    /**
     * 根据项目统计导出
     *
     * @return
     */
    @PostMapping("/statisticByProject/export")
    public void statisticByProjectExport(BugStatisticParamsBO paramsBO, HttpServletResponse response) {
        iBugService.statisticByProjectExport(paramsBO,response);
    }



}
