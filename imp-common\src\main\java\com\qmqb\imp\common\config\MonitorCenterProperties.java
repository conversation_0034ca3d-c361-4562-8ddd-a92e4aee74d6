package com.qmqb.imp.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 监控中心配置类
 *
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "monitor-center")
public class MonitorCenterProperties {

    /**
     * 监控中心API根路径
     */
    private String apiUrl;

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }
}
