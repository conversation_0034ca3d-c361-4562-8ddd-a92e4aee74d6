package com.qmqb.imp.system.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.qmqb.imp.system.domain.ZtBuild;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.qmqb.imp.system.mapper.ZtBuildMapper;
import com.qmqb.imp.system.service.IZtBuildService;

import java.util.ArrayList;
import java.util.List;
/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@RequiredArgsConstructor
@Service
public class ZtBuildServiceImpl implements IZtBuildService {

    private final ZtBuildMapper baseMapper;


    @Override
    public List<ZtBuild> selectByStoryIds(List<Integer> storyIds) {
        if (CollectionUtils.isEmpty(storyIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ZtBuild> wrapper = new LambdaQueryWrapper<>();
        for (Integer storyId : storyIds) {
            wrapper.or(wq -> wq.apply("FIND_IN_SET({0}, stories)", storyId));
        }
        return baseMapper.selectList(wrapper);
    }
}
