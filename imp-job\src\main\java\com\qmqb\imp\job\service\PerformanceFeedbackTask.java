package com.qmqb.imp.job.service;

import com.qmqb.imp.job.indicator.PerformanceFeedbackManager;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

/**
 * 非组长绩效反馈生成定时任务
 * 专门负责生成绩效反馈记录，独立于指标计算任务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class PerformanceFeedbackTask {

    @Autowired
    private PerformanceFeedbackManager performanceFeedbackManager;

    private final String separator = ",";

    private final String dateSeparator = "-";

    /**
     * 生成绩效反馈记录
     * <p>
     * 可通过任务参数指定年月和/或部门ID，支持以下格式：
     * - "2024-01" : 处理2024年1月所有部门的非组长绩效反馈
     * - "123" : 处理上个月部门ID为123的非组长绩效反馈
     * - "2024-01,123" : 处理2024年1月部门ID为123的非组长绩效反馈
     * - 空参数 : 处理上个月所有部门的非组长绩效反馈
     */
    @XxlJob("performanceFeedbackGenerateHandler")
    public ReturnT<String> performanceFeedbackGenerateHandler(String param) {
        log.info("开始执行非组长绩效反馈生成任务，参数：{}", param);

        try {
            int evalYear;
            int evalMonth;
            Long deptId = null;

            if (StringUtils.isNotBlank(param)) {
                // 解析参数：支持 yyyy-MM, 123, yyyy-MM,123 等格式
                if (param.contains(separator)) {
                    // 格式：yyyy-MM,deptId
                    String[] parts = param.split(separator);
                    String dateStr = parts[0];
                    YearMonth yearMonth = YearMonth.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM"));
                    evalYear = yearMonth.getYear();
                    evalMonth = yearMonth.getMonthValue();

                    if (parts.length > 1 && StringUtils.isNotBlank(parts[1])) {
                        try {
                            deptId = Long.parseLong(parts[1]);
                            log.info("使用参数指定的年月和部门：{}-{}，部门ID：{}", evalYear, evalMonth, deptId);
                        } catch (NumberFormatException e) {
                            log.error("部门ID参数格式错误：{}，将处理所有部门", parts[1]);
                            return new ReturnT<>(ReturnT.FAIL_CODE, "部门ID参数格式错误，请检查参数格式");
                        }
                    } else {
                        log.info("使用参数指定的年月：{}-{}，处理所有部门", evalYear, evalMonth);
                    }
                } else if (param.contains(dateSeparator)) {
                    // 格式：yyyy-MM（只指定年月）
                    YearMonth yearMonth = YearMonth.parse(param, DateTimeFormatter.ofPattern("yyyy-MM"));
                    evalYear = yearMonth.getYear();
                    evalMonth = yearMonth.getMonthValue();
                    log.info("使用参数指定的年月：{}-{}，处理所有部门", evalYear, evalMonth);
                } else {
                    // 格式：123（只指定部门ID）
                    try {
                        deptId = Long.parseLong(param);
                        LocalDate lastMonth = LocalDate.now().minusMonths(1);
                        evalYear = lastMonth.getYear();
                        evalMonth = lastMonth.getMonthValue();
                        log.info("使用默认年月（上个月）：{}-{}，参数指定部门ID：{}", evalYear, evalMonth, deptId);
                    } catch (NumberFormatException e) {
                        log.error("参数格式错误，无法解析为部门ID：{}", param);
                        return new ReturnT<>(ReturnT.FAIL_CODE, "参数格式错误，请使用 'yyyy-MM'、'123' 或 'yyyy-MM,123' 格式");
                    }
                }
            } else {
                // 默认生成上一个月的绩效反馈
                LocalDate lastMonth = LocalDate.now().minusMonths(1);
                evalYear = lastMonth.getYear();
                evalMonth = lastMonth.getMonthValue();
                log.info("使用默认年月（上个月）：{}-{}，处理所有部门", evalYear, evalMonth);
            }

            // 执行绩效反馈生成（personType = 0 表示非组长）
            performanceFeedbackManager.generateFeedbackByYearMonthAndDept(evalYear, evalMonth, 0, deptId);

            log.info("非组长绩效反馈生成任务执行完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("非组长绩效反馈生成任务执行失败", e);
            return ReturnT.FAIL;
        }
    }
}
