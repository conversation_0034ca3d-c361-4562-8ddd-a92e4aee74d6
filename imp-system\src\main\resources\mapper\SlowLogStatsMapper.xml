<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.SlowLogStatsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.qmqb.imp.system.domain.SlowLogStats">
        <id column="id" property="id" />
        <result column="DBName" property="dbName" />
        <result column="DBNodeId" property="dbNodeId" />
        <result column="CreateTime" property="createTime" />
        <result column="MaxExecutionTime" property="maxExecutionTime" />
        <result column="MaxLockTime" property="maxLockTime" />
        <result column="ParseMaxRowCount" property="parseMaxRowCount" />
        <result column="ParseTotalRowCounts" property="parseTotalRowCounts" />
        <result column="ReturnMaxRowCount" property="returnMaxRowCount" />
        <result column="ReturnTotalRowCounts" property="returnTotalRowCounts" />
        <result column="SQLHash" property="sqlHash" />
        <result column="SQLText" property="sqlText" />
        <result column="TotalExecutionCounts" property="totalExecutionCounts" />
        <result column="TotalExecutionTimes" property="totalExecutionTimes" />
        <result column="TotalLockTimes" property="totalLockTimes" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, DBName, DBNodeId, CreateTime, MaxExecutionTime, MaxLockTime, ParseMaxRowCount, ParseTotalRowCounts, ReturnMaxRowCount, ReturnTotalRowCounts, SQLHash, SQLText, TotalExecutionCounts, TotalExecutionTimes, TotalLockTimes
    </sql>



</mapper>
