package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 【请填写功能名称】对象 zt_user
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@TableName("zt_user")
public class ZtUser {

    private static final long serialVersionUID=1L;


    @TableId(value = "id")
    private Integer id;

    private Integer company;

    private Integer dept;

    private String account;

    private String type;

    private String password;

    private String role;

    private String realname;

    private String pinyin;

    private String nickname;

    private String commiter;

    private String avatar;

    private Date birthday;

    private String gender;

    private String email;

    private String skype;

    private String qq;

    private String mobile;

    private String phone;

    private String weixin;

    private String dingding;

    private String slack;

    private String whatsapp;

    private String address;

    private String zipcode;

    private String nature;

    private String analysis;

    private String strategy;

    @TableField("`join`")
    private Date join;

    private Integer visits;

    private String ip;

    private Integer last;

    private Integer fails;

    private Date locked;

    private String feedback;

    private String ranzhi;

    private String ldap;

    private Long score;

    @TableField("`scoreLevel`")
    private Long scoreLevel;

    private String deleted;

    @TableField("`clientStatus`")
    private String clientStatus;

    @TableField("`clientLang`")
    private String clientLang;

}
