package com.qmqb.imp.system.service.indicator;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackMainBo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackMainQueryBo;
import com.qmqb.imp.system.domain.performance.PerformanceFeedbackMain;
import com.qmqb.imp.system.domain.vo.performance.PerformanceFeedbackMainVo;

import java.util.Collection;
import java.util.List;

/**
 * 绩效反馈主表Service接口
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IPerformanceFeedbackMainService extends IService<PerformanceFeedbackMain> {

    /**
     * 查询绩效反馈主表
     *
     * @param id 绩效反馈主表ID
     * @return 绩效反馈主表信息
     */
    PerformanceFeedbackMainVo queryById(Long id);

    /**
     * 查询绩效反馈主表列表（分页）
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 绩效反馈主表分页列表
     */
    TableDataInfo<PerformanceFeedbackMainVo> queryPageList(PerformanceFeedbackMainQueryBo bo, PageQuery pageQuery);

    /**
     * 查询绩效反馈主表列表
     *
     * @param bo 查询条件
     * @return 绩效反馈主表列表
     */
    List<PerformanceFeedbackMainVo> queryList(PerformanceFeedbackMainQueryBo bo);

    /**
     * 新增绩效反馈主表
     *
     * @param bo 新增对象
     * @return 是否成功
     */
    Boolean insertByBo(PerformanceFeedbackMainBo bo);

    /**
     * 修改绩效反馈主表
     *
     * @param bo 修改对象
     * @return 是否成功
     */
    Boolean updateByBo(PerformanceFeedbackMainBo bo);

    /**
     * 校验并批量删除绩效反馈主表信息
     *
     * @param ids     待删除ID集合
     * @param isValid 是否校验
     * @return 是否成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据员工昵称、年份、月份查询系统生成的绩效反馈记录列表
     *
     * @param nickName 员工昵称
     * @param year     年份
     * @param month    月份
     * @return 绩效反馈记录列表
     */
    List<String> getIdByNickNameAndYearMonth(String nickName, Integer year, Integer month);

    /**
     * 批量项目经理审核
     * @param ids     绩效反馈主表ID集合
     * @param status  审核状态
     * @param auditor 审核人
     * @param remark  审核备注
     * @return 是否成功
     */
    Boolean batchProjectManagerAudit(Collection<Long> ids, String status, String auditor, String remark);

    /**
     * 批量提交
     *
     * @param ids       绩效反馈主表ID集合
     * @param submitter 提交人
     * @return 是否成功
     */
    Boolean batchSubmit(Collection<Long> ids, String submitter);

    /**
     * 批量最终审核
     *
     * @param ids     绩效反馈主表ID集合
     * @param status  审核状态
     * @param auditor 审核人
     * @param remark  审核备注
     * @return 是否成功
     */
    Boolean batchFinalAudit(Collection<Long> ids, String status, String auditor, String remark);

    /**
     * 根据二类指标查询绩效反馈记录
     *
     * @param secondaryIndicator 二类指标
     * @param year 年份
     * @param month 月份
     * @param nickNames 员工昵称列表
     * @return 绩效反馈记录列表
     */
    List<com.qmqb.imp.system.domain.performance.PerformanceFeedback> getBySecondaryIndicator(String secondaryIndicator, Integer year, Integer month, List<String> nickNames);
}
