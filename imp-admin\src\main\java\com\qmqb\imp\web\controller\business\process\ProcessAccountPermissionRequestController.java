package com.qmqb.imp.web.controller.business.process;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.system.domain.bo.process.ProcessAccountPermissionRequestBo;
import com.qmqb.imp.system.domain.vo.process.ProcessAccountPermissionRequestVo;
import com.qmqb.imp.system.service.process.IProcessAccountPermissionRequestService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 信息系统账号及权限申请
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/processAccountPermissionRequest")
public class ProcessAccountPermissionRequestController extends BaseController {

    private final IProcessAccountPermissionRequestService iProcessAccountPermissionRequestService;

    /**
     * 查询信息系统账号及权限申请列表
     */
    @SaCheckPermission("system:processAccountPermissionRequest:list")
    @GetMapping("/list")
    public TableDataInfo<ProcessAccountPermissionRequestVo> list(ProcessAccountPermissionRequestBo bo, PageQuery pageQuery) {
        return iProcessAccountPermissionRequestService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取信息系统账号及权限申请详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:processAccountPermissionRequest:query")
    @GetMapping("/{id}")
    public R<ProcessAccountPermissionRequestVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iProcessAccountPermissionRequestService.queryById(id));
    }

    /**
     * 新增信息系统账号及权限申请
     */
    @SaCheckPermission("system:processAccountPermissionRequest:add")
    @Log(title = "信息系统账号及权限申请", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProcessAccountPermissionRequestBo bo) {
        return toAjax(iProcessAccountPermissionRequestService.insertByBo(bo));
    }

    /**
     * 修改信息系统账号及权限申请
     */
    @SaCheckPermission("system:processAccountPermissionRequest:edit")
    @Log(title = "信息系统账号及权限申请", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProcessAccountPermissionRequestBo bo) {
        return toAjax(iProcessAccountPermissionRequestService.updateByBo(bo));
    }

    /**
     * 删除信息系统账号及权限申请
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:processAccountPermissionRequest:remove")
    @Log(title = "信息系统账号及权限申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iProcessAccountPermissionRequestService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
