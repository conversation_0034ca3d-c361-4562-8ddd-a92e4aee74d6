package com.qmqb.imp.web.controller.business;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.system.domain.bo.ScanProjectFileAssignBo;
import com.qmqb.imp.system.domain.bo.ScanProjectFileBo;
import com.qmqb.imp.system.domain.bo.ScanProjectFileProcessBo;
import com.qmqb.imp.system.domain.vo.ScanProjectFileVo;
import com.qmqb.imp.system.service.IScanProjectFileService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 扫描项目文件记录
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/scanProjectFile")
public class ScanProjectFileController extends BaseController {

    private final IScanProjectFileService iScanProjectFileService;

    /**
     * 按照扫描文件分组查询扫描记录概况
     */
    @SaCheckPermission("system:scanProjectFile:list")
    @GetMapping("/pageGroupingFile")
    public TableDataInfo<ScanProjectFileVo> pageGroupingFile(ScanProjectFileBo bo, PageQuery pageQuery) {
        return iScanProjectFileService.queryPageListWithPermission(bo, pageQuery);
    }

    /**
     * 执行文件操作（指派、更换指派）
     */
    @SaCheckPermission("system:scanProjectFile:assign")
    @Log(title = "扫描项目文件操作", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/assign")
    public R<Void> executeFileAssign(@Validated @RequestBody ScanProjectFileAssignBo operationBo) {
        boolean result = iScanProjectFileService.executeFileAssign(operationBo);
        return result ? R.ok() : R.fail();
    }

    /**
     * 执行文件操作（处理）
     */
    @SaCheckPermission("system:scanProjectFile:process")
    @Log(title = "扫描项目文件操作", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/process")
    public R<Void> executeFileProcess(@Validated @RequestBody ScanProjectFileProcessBo operationBo) {
        boolean result = iScanProjectFileService.executeFileProcess(operationBo);
        return result ? R.ok() : R.fail();
    }
}
