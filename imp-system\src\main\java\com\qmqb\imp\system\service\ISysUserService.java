package com.qmqb.imp.system.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.vo.SysUserExportVo;

import java.time.LocalDate;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 用户 业务层
 *
 * <AUTHOR>
 */
@DS(DataSource.GITLAB)
public interface ISysUserService {


    /**
     * 查询分页
     * @param user
     * @param pageQuery
     * @return
     */
    TableDataInfo<SysUser> selectPageUserList(SysUser user, PageQuery pageQuery);

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    List<SysUser> selectUserList(SysUser user);

    /**
     * 查询所有的用户列表
     *
     * @return
     */
    List<SysUser> selectAllUser();

    /**
     * 查询所有的用户列表
     *
     * @return
     */
    List<SysUser> selectAllUser2();

    /**
     * 根据条件分页查询已分配用户角色列表
     * @param user
     * @param pageQuery
     * @return
     */
    TableDataInfo<SysUser> selectAllocatedList(SysUser user, PageQuery pageQuery);

    /**
     * 用户信息集合信息
     * @param user
     * @param pageQuery
     * @return
     */
    TableDataInfo<SysUser> selectUnallocatedList(SysUser user, PageQuery pageQuery);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    SysUser selectUserByUserName(String userName);

    /**
     * 通过昵称查询用户
     * @param nickName
     * @return
     */
    SysUser selectUserByNickName(String nickName);

    /**
     * 通过手机号查询用户
     *
     * @param phonenumber 手机号
     * @return 用户对象信息
     */
    SysUser selectUserByPhonenumber(String phonenumber);

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    SysUser selectUserById(Long userId);

    /**
     * 根据用户ID查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    String selectUserRoleGroup(String userName);

    /**
     * 根据用户ID查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    String selectUserPostGroup(String userName);

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    String checkUserNameUnique(SysUser user);

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    String checkPhoneUnique(SysUser user);

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    String checkEmailUnique(SysUser user);

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    void checkUserAllowed(SysUser user);

    /**
     * 校验用户是否允许更换部门
     *
     * @param user 用户信息
     */
    void checkUserChangeDept(SysUser user);

    /**
     * 校验是否允许加入新员工组
     * @param user
     */
    void checkNewMemDeptAllowed(SysUser user);

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    void checkUserDataScope(Long userId);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int insertUser(SysUser user);

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    boolean registerUser(SysUser user);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int updateUser(SysUser user);

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    void insertUserAuth(Long userId, Long[] roleIds);

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    int updateUserStatus(SysUser user);

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int updateUserProfile(SysUser user);

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    boolean updateUserAvatar(String userName, String avatar);

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    int resetPwd(SysUser user);

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    int resetUserPwd(String userName, String password);

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    int deleteUserById(Long userId);

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    int deleteUserByIds(Long[] userIds);

    /**
     * 根据部门编码和角色编码查找用户
     *
     * @param deptCodes
     * @param roleKeys
     * @return
     */
    List<SysUserExportVo> listExportVoByDeptCodesAndRoleKeys(List<String> deptCodes, List<String> roleKeys);

    /**
     * 通过昵称查询用户
     *
     * @param nickNames
     * @return
     */
    List<SysUser> selectUserByNickNames(Collection<String> nickNames);

    /**
     * 通过id列表查询用户
     *
     * @param ids
     * @return
     */
    List<SysUser> selectUserByIds(Collection<String> ids);

    /**
     * 通过部门ID查询用户
     *
     * @param deptId 部门ID
     * @return
     */
    List<SysUser> selectUserByDeptId(Long deptId);

    /**
     * 根据姓名找出离职员工
     * @param userNames
     * @return
     */
    List<String> listDepartByUserNames(List<String> userNames);


    /**
     * 根据角色名称和月份查询用户数
     * @param roleName
     * @param monthEndTime
     * @return
     */
    Integer selectUserCountByRoleName(String roleName, Date monthEndTime);

    /**
     * 根据禅道用户名查询用户
     * @param ztUsers
     * @return
     */
    List<SysUserExportVo> listExportVoByztUserNames(List<String> ztUsers);

    /**
     * 根据昵称查询用户
     * @param leadNames
     * @return
     */
    List<SysUserExportVo> listExportVoByNickNames(Set<String> leadNames);


    /**
     * 获取时间范围内的员工(新员工查询)
     * @param startTime
     * @param endTime
     * @return
     */
    List<SysUser> listNewEmployee(LocalDate startTime, LocalDate endTime);

    /**
     * 查询当前用户所在组的所有用户
     * @return
     */
    List<SysUser> listAllUserInGroup();

    /**
     * 根据角色id查询用户
     * @param roleId
     * @return
     */
    List<SysUser> listByRoleId(Long roleId);

    /**
     * 根据禅道名称查询用户信息
     *
     * @param devUserNames
     * @return
     */
    List<SysUser> selectByZtUserNames(List<String> devUserNames);

}
