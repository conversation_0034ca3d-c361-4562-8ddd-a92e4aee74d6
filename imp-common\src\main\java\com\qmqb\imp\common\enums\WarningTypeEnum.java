package com.qmqb.imp.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description 预警类型
 * @date 2025/8/6 11:50
 */
@Getter
public enum WarningTypeEnum {

    /**
     * 任务状态预警
     */
    TASK_STATUS("task_status", "任务状态预警"),
    /**
     * 需求用例预警
     */
    STORY_CASE("story_case", "需求用例预警"),
    ;
    /**
     * 编码
     */
    private final String code;
    /**
     * 描述
     */
    private final String desc;

    WarningTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
