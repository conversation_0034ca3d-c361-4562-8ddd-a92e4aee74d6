package com.qmqb.imp.system.service;

import cn.hutool.core.date.DateTime;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.dto.TaskInfoQueryDTO;
import com.qmqb.imp.common.core.domain.dto.TaskQueryDTO;
import com.qmqb.imp.common.core.domain.dto.TaskStatisticsDTO;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.ZtTaskStatusEnum;
import com.qmqb.imp.system.domain.ZtTask;
import com.qmqb.imp.system.domain.bo.WorkDetailBo;
import com.qmqb.imp.system.domain.bo.ZtTaskBo;
import com.qmqb.imp.system.domain.vo.*;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 禅道任务Service接口
 *
 * <AUTHOR>
 * @date 2022-12-08
 */
@DS(DataSource.ZENTAO)
public interface IZtTaskService {

    /**
     * 查询禅道任务
     * @param id
     * @return
     */
    ZtTaskVo queryById(Integer id);

    /**
     * 查询禅道任务列表
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<ZtTaskVo> queryPageList(ZtTaskBo bo, PageQuery pageQuery);

    /**
     * 查询禅道任务列表
     * @param bo
     * @return
     */
    List<ZtTaskVo> queryList(ZtTaskBo bo);

    /**
     * 新增禅道任务
     * @param bo
     * @return
     */
    Boolean insertByBo(ZtTaskBo bo);

    /**
     * 修改禅道任务
     * @param bo
     * @return
     */
    Boolean updateByBo(ZtTaskBo bo);

    /**
     * 校验并批量删除禅道任务信息
     * @param ids
     * @param isValid
     * @return
     */
    Boolean deleteWithValidByIds(Collection<Integer> ids, Boolean isValid);

    /**
     * 获取禅道任务查询分页
     * @param request
     * @return
     */
    Page<TaskVO> page(TaskQueryDTO request);

    /**
     * 根据完成时间查询查询已完成任务
     *
     * @param fromDate
     * @param toDate
     * @return
     */
    List<ZtTask> listDoneByFinishedDate(DateTime fromDate, DateTime toDate);

    /**
     * 根据开始时间查询查询进行中任务
     *
     * @param fromDate
     * @param toDate
     * @return
     */
    List<ZtTask> listDoingByRealStarted(DateTime fromDate, DateTime toDate);

    /**
     * 根据状态和开启时间任务
     *
     * @param status
     * @param fromDate
     * @param toDate
     * @return
     */
    List<ZtTask> listByStatusAndOpenedDate(List<String> status, DateTime fromDate, DateTime toDate);

    /**
     * 根据用户拼音名小写列表以及开始和结束时间获取用户的任务个数列表
     *
     * @param userNameList
     * @param beginTime
     * @param endTime
     * @return
     */
    List<ZtTaskCountVO> getTaskListByTimeAndUser(List<String> userNameList, Date beginTime, Date endTime);

    /**
     * 根据月份和组别查询任务列表
     *
     * @param request
     * @return
     */
    TableDataInfo<TaskInfoVO> getTaskListByGroupAndMonth(TaskInfoQueryDTO request);

    /**
     * 处理连续几个工作日没进行任务场景并返回要预警的对象
     *
     * @param sysUserExportVoList 用户列表
     * @param beginTime           开始时间
     * @param endTime             结束时间
     * @return
     */
    List<SysUserExportVo> dealNoTaskWarnObject(List<SysUserExportVo> sysUserExportVoList, Date beginTime, Date endTime);

    /**
     * 处理进行中的任务连续几个工作日没更新场景
     *
     * @param sysUserExportVoList 用户列表
     * @param endTime             截止时间
     * @return
     */
    List<SysUserExportVo> dealDoingTaskWarnObject(List<SysUserExportVo> sysUserExportVoList, Date endTime);


    /**
     * 任务统计
     *
     * @param request
     * @return
     */
    TableDataInfo<TaskStatisticsVO> statisticsNoDoing(TaskStatisticsDTO request);


    /**
     * 根据状态查询任务数量
     * @param monthStartTime
     * @param monthEndTime
     * @param ztTaskStatusEnum
     * @param usernameList
     * @return
     */
    Long selectTaskCountByStatus(Date monthStartTime, Date monthEndTime, ZtTaskStatusEnum ztTaskStatusEnum, List<String> usernameList);


    /**
     * 个人任务明细
     * @param workDetail
     * @return
     */
    Page<TaskVO> queryUserTask(WorkDetailBo workDetail);

    /**
     * 查询进行中的任务超过10天没有完成的成员
     * @return
     */
    List<TaskVO> doingTaskTimeout();

    /**
     * 查询所有进行中的任务，不按时间过滤
     *
     * @return 所有进行中的任务列表
     */
    List<ZtTask> listAllDoing();

    /**
     * 查询所有暂停中的任务，不按时间过滤
     *
     * @return 所有暂停中的任务列表
     */
    List<ZtTask> listAllPaused();

    /**
     * 查询任务的负责人
     * @param storyIdList
     * @param parent
     * @param type
     * @return
     */
    List<String> selectTaskPrincipal(List<Long> storyIdList, Integer parent, String type);
}
