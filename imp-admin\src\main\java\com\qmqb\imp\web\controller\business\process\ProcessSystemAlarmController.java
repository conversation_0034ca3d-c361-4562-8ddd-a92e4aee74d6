package com.qmqb.imp.web.controller.business.process;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.system.domain.bo.process.ProcessSystemAlarmBo;
import com.qmqb.imp.system.domain.vo.process.ProcessSystemAlarmVo;
import com.qmqb.imp.system.service.process.IProcessSystemAlarmService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 系统功能异常报障处理
 *
 * <AUTHOR>
 * @date 2024-12-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/processSystemAlarm")
public class ProcessSystemAlarmController extends BaseController {

    private final IProcessSystemAlarmService iProcessSystemAlarmService;

    /**
     * 查询系统功能异常报障处理列表
     */
    @SaCheckPermission("system:processSystemAlarm:list")
    @GetMapping("/list")
    public TableDataInfo<ProcessSystemAlarmVo> list(ProcessSystemAlarmBo bo, PageQuery pageQuery) {
        return iProcessSystemAlarmService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取系统功能异常报障处理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:processSystemAlarm:query")
    @GetMapping("/{id}")
    public R<ProcessSystemAlarmVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iProcessSystemAlarmService.queryById(id));
    }

    /**
     * 新增系统功能异常报障处理
     */
    @SaCheckPermission("system:processSystemAlarm:add")
    @Log(title = "系统功能异常报障处理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProcessSystemAlarmBo bo) {
        return toAjax(iProcessSystemAlarmService.insertByBo(bo));
    }

    /**
     * 修改系统功能异常报障处理
     */
    @SaCheckPermission("system:processSystemAlarm:edit")
    @Log(title = "系统功能异常报障处理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProcessSystemAlarmBo bo) {
        return toAjax(iProcessSystemAlarmService.updateByBo(bo));
    }

    /**
     * 删除系统功能异常报障处理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:processSystemAlarm:remove")
    @Log(title = "系统功能异常报障处理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iProcessSystemAlarmService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
