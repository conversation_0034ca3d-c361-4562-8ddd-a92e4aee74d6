package com.qmqb.imp.app.service;

import com.qmqb.imp.app.domain.bo.PerformanceEvaluateBizBO;
import com.qmqb.imp.app.domain.bo.SubmitEvaluateBO;
import com.qmqb.imp.app.domain.vo.KeyValueVO;
import com.qmqb.imp.app.domain.vo.PerformanceEvaluateBizVO;
import com.qmqb.imp.app.domain.vo.PerformanceReportVO;
import com.qmqb.imp.common.core.domain.R;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
public interface PerformanceEvaluateBizService {

    /**
     * 人员列表
     *
     * @return
     */
    R<List<KeyValueVO>> peopleSelect();

    /**
     * 个人绩效报告
     *
     * @param bo
     * @return
     */
    R<PerformanceReportVO> personalPerformanceReport(PerformanceEvaluateBizBO bo);

    /**
     * 点评详情
     *
     * @return
     */
    R<PerformanceEvaluateBizVO> detail(PerformanceEvaluateBizBO bo);

    /**
     * 提交点评
     *
     * @param bo
     * @return
     */
    R<Void> submit(SubmitEvaluateBO bo);
}
