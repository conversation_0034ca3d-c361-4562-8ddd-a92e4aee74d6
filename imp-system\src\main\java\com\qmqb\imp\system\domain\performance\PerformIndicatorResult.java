package com.qmqb.imp.system.domain.performance;

import com.baomidou.mybatisplus.annotation.*;
import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 绩效指标原因对象 tb_perform_indicator_result
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_performance_indicator_result")
public class PerformIndicatorResult extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 一类指标
     */
    private String firstIndecator;
    /**
     * 二类指标
     */
    private String secondIndecator;
    /**
     * 绩效级别(S/A/B/C/D)
     */
    private String level;
    /**
     * 推荐原因
     */
    private String result;
    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private Integer delFlag;

}
