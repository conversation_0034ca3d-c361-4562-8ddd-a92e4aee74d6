package com.qmqb.imp.system.domain.bo.performance;

import com.qmqb.imp.common.core.domain.BaseEntity;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;

import java.time.LocalDate;
import java.util.Date;

/**
 * 绩效辅导业务对象 tb_performance_tutoring
 *
 * <AUTHOR>
 * @date 2025-07-01
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class PerformanceTutoringBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class,AddGroup.class })
    private Long id;

    /**
     * 绩效反馈表id(tb_performance_feedback)
     */
    @NotNull(message = "绩效反馈表id(tb_performance_feedback)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long feedbackId;

    /**
     * 辅导概要
     */
    @NotBlank(message = "辅导概要不能为空", groups = { EditGroup.class })
    private String tutoringSummary;

    /**
     * 辅导结果
     */
    @NotBlank(message = "辅导结果不能为空", groups = { EditGroup.class })
    private String tutoringResult;

    /**
     * 辅导人
     */
    @NotBlank(message = "辅导人不能为空", groups = { EditGroup.class })
    private String tutor;

    /**
     * 辅导时间
     */
    @NotNull(message = "辅导时间不能为空", groups = {  EditGroup.class })
    private LocalDate tutoringTime;

    /**
     * 辅导附件
     */
    private String tutoringAttachment;

    /**
     * 总监建议
     */
    private String directorSuggest;

    /**
     * 建议时间
     */
    private Date suggestTime;


}
