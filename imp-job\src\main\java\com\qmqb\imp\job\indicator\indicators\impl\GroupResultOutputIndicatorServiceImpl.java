package com.qmqb.imp.job.indicator.indicators.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.core.domain.entity.SysRole;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.PersonTypeEnum;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.common.utils.ScoreLevelUtil;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.performance.Performance;
import com.qmqb.imp.system.domain.performance.PerformanceIndicator;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.indicator.IPerformanceIndicatorService;
import com.qmqb.imp.system.service.indicator.IPerformanceService;
import com.qmqb.imp.system.service.IUserKqStatService;
import com.qmqb.imp.system.domain.UserKqStat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 月结果产出指标等级计算
 * <p>
 * 针对技术经理角色，根据团队成员在已计算的专业指标等级基础上进行计算：
 * - 开发成员：查询其"代码行数"指标等级
 * - 测试成员：查询其"用例执行数"和"BUG创建数"指标等级并合并
 * - 运维成员：查询其"计划性维护数"指标等级
 * - 其他角色成员：忽略
 *
 * 算法规则：
 * - 大团队(>=4人，含组长)：要求半数以上达到对应等级（计算时包含组长）
 * - 小团队(<4人，含组长)：要求全部组员达到对应等级（计算时排除组长）
 * - D级一票否决：任何成员为D级，技术经理直接为D级
 * - C级占半数以上：技术经理为C级
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class GroupResultOutputIndicatorServiceImpl implements IndicatorLevelCalcService {

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IPerformanceIndicatorService performanceIndicatorService;

    @Autowired
    private IPerformanceService performanceService;

    @Autowired
    private IUserKqStatService userKqStatService;

    @Override
    public String getIndicatorCode() {
        return PerformanceIndicatorEnum.GROUP_RESULT_OUTPUT.getCode();
    }

    @Override
    public IndicatorCalcResult calcLevel(String nickName, Integer month, List<TrackWorkResultVO> trackWorkResults) {
        Optional<TrackWorkResultVO> userWorkResultOpt = trackWorkResults.stream()
            .filter(r -> nickName.equals(r.getWorkUsername()))
            .findFirst();

        if (!userWorkResultOpt.isPresent()) {
            String indicatorName = PerformanceIndicatorEnum.GROUP_RESULT_OUTPUT.getName();
            return new IndicatorCalcResult(ScoreLevelEnum.SCORE_C.getCode(),
                String.format("员工[%s]在%s月份的%s数据未找到", nickName, month, indicatorName));
        }

        TrackWorkResultVO userWorkResult = userWorkResultOpt.get();

        try {
            // 构建团队计算数据
            TeamCalculationData teamData = buildTeamData(userWorkResult, nickName);

            // 计算成员等级
            calculateMemberLevels(teamData);

            // 计算最终等级
            String finalLevel = calculateFinalLevel(teamData);

            // 生成日志
            String logContent = generateLogContent(teamData, finalLevel);

            return new IndicatorCalcResult(finalLevel, logContent);

        } catch (Exception e) {
            log.error("计算技术经理 {} 月结果产出指标失败", nickName, e);
            throw new ServiceException(String.format("技术经理[%s]月结果产出指标计算异常：%s", nickName, e.getMessage()), e);
        }
    }

    @Override
    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults) {
        return "";
    }

    /**
     * 构建团队计算数据
     */
    private TeamCalculationData buildTeamData(TrackWorkResultVO workResult, String nickName) {
        Integer year = workResult.getWorkYear();
        Integer month = workResult.getWorkMonth();

        TeamCalculationData data = new TeamCalculationData();
        data.managerName = nickName;
        data.year = year;
        data.month = month;

        // 获取技术经理信息
        data.manager = sysUserService.selectUserByNickName(nickName);
        if (data.manager == null) {
            throw new ServiceException(String.format("未找到技术经理信息: %s", nickName));
        }

        // 获取所有团队成员
        data.allMembers = sysUserService.selectUserByDeptId(data.manager.getDeptId());
        if (data.allMembers.isEmpty()) {
            throw new ServiceException(String.format("技术经理 %s 的团队无成员", nickName));
        }

        // 过滤请假成员
        filterAbsentMembers(data);

        // 获取团队绩效记录
        data.performanceIds = getTeamPerformanceIds(data);

        // 建立绩效记录到员工的映射
        buildPerformanceMapping(data);

        return data;
    }

    /**
     * 过滤请假成员（出勤天数为0）
     */
    private void filterAbsentMembers(TeamCalculationData data) {
        List<String> allMemberNames = data.allMembers.stream()
            .map(SysUser::getNickName)
            .collect(Collectors.toList());

        // 获取考勤数据
        List<UserKqStat> attendanceStats = userKqStatService.listByUserNamesAndKqYearAndKqMonth(
            allMemberNames, String.valueOf(data.year), String.valueOf(data.month));

        // 找出请假成员
        Set<String> absentMemberNames = attendanceStats.stream()
            .filter(stat -> stat.getKqAttendanceDays() != null && stat.getKqAttendanceDays().intValue() == 0)
            .map(UserKqStat::getKqUserName)
            .collect(Collectors.toSet());

        // 分离有效成员和请假成员
        data.activeMembers = data.allMembers.stream()
            .filter(member -> !absentMemberNames.contains(member.getNickName()))
            .collect(Collectors.toList());

        data.absentMembers = data.allMembers.stream()
            .filter(member -> absentMemberNames.contains(member.getNickName()))
            .collect(Collectors.toList());

        log.info("技术经理 {} 团队成员过滤：总人数{}，请假人数{}，有效人数{}",
            data.managerName, data.allMembers.size(), data.absentMembers.size(), data.activeMembers.size());
    }

    /**
     * 获取团队绩效记录ID
     */
    private List<Long> getTeamPerformanceIds(TeamCalculationData data) {
        List<Long> performanceIds = performanceService.list(new LambdaQueryWrapper<Performance>()
                .eq(Performance::getYear, data.year)
                .eq(Performance::getMonth, data.month)
                .eq(Performance::getGroupId, data.manager.getDeptId()))
                .stream()
                .map(Performance::getId)
                .collect(Collectors.toList());

        if (performanceIds.isEmpty()) {
            throw new ServiceException(String.format("技术经理 %s 的团队无绩效记录", data.managerName));
        }
        return performanceIds;
    }

    /**
     * 建立绩效记录到员工的映射
     */
    private void buildPerformanceMapping(TeamCalculationData data) {
        List<Performance> allPerformances = performanceService.list(
            new LambdaQueryWrapper<Performance>()
                .in(Performance::getId, data.performanceIds));

        data.performanceIdToNickName = allPerformances.stream()
            .collect(Collectors.toMap(Performance::getId, Performance::getNickName));
    }

    /**
     * 计算所有成员的专业指标等级
     */
    private void calculateMemberLevels(TeamCalculationData data) {
        data.memberLevels = new ArrayList<>();
        data.memberLevelMap = new HashMap<>(16);
        data.memberRoleMap = new HashMap<>(16);

        for (SysUser member : data.activeMembers) {
            String memberName = member.getNickName();
            String memberRole = getMemberRoleType(member);

            // 跳过其他角色
            if ("其他角色".equals(memberRole)) {
                log.debug("成员 {} 为其他角色，跳过绩效计算", memberName);
                continue;
            }

            // 计算成员等级
            String memberLevel = calculateSingleMemberLevel(member, data);

            if (memberLevel != null) {
                data.memberLevels.add(memberLevel);
                data.memberLevelMap.put(memberName, memberLevel);
                data.memberRoleMap.put(memberName, memberRole);
                log.debug("成员 {} ({}) 的专业指标等级: {}", memberName, memberRole, memberLevel);
            } else {
                log.debug("成员 {} ({}) 无绩效记录，跳过", memberName, memberRole);
            }
        }
    }

    /**
     * 计算单个成员的专业指标等级
     */
    private String calculateSingleMemberLevel(SysUser member, TeamCalculationData data) {
        List<String> indicatorsForMember = getIndicatorsForMember(member);
        if (indicatorsForMember.isEmpty()) {
            return null;
        }

        // 查找该成员的绩效记录ID
        Long memberPerformanceId = null;
        for (Map.Entry<Long, String> entry : data.performanceIdToNickName.entrySet()) {
            if (member.getNickName().equals(entry.getValue())) {
                memberPerformanceId = entry.getKey();
                break;
            }
        }

        if (memberPerformanceId == null) {
            return null;
        }

        // 查询指标等级记录
        List<PerformanceIndicator> memberIndicators = new ArrayList<>();
        for (String indicatorCode : indicatorsForMember) {
            List<PerformanceIndicator> indicators = performanceIndicatorService.list(
                new LambdaQueryWrapper<PerformanceIndicator>()
                    .eq(PerformanceIndicator::getPerformanceId, memberPerformanceId)
                    .eq(PerformanceIndicator::getIndicatorCode, indicatorCode));
            memberIndicators.addAll(indicators);
        }

        // 处理指标等级
        if (memberIndicators.isEmpty()) {
            // 有绩效记录但没有指标记录，视为B级（因为B级绩效不会记录在指标表中）
            log.debug("成员 {} 有绩效记录但无专业指标记录，视为B级", member.getNickName());
            return ScoreLevelEnum.SCORE_B.getCode();
        }

        // 获取等级列表
        List<String> levels = memberIndicators.stream()
            .map(PerformanceIndicator::getScoreLevel)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        if (levels.isEmpty()) {
            return ScoreLevelEnum.SCORE_B.getCode();
        }
        //B等级数据库没有，自动补全
        if(levels.size() <indicatorsForMember.size()){
            for (int i=0;i<indicatorsForMember.size()-levels.size();i++){
                levels.add(ScoreLevelEnum.SCORE_B.getCode());
            }
        }

        // 对于测试人员（有多个指标），需要合并等级
        if (levels.size() > 1) {
            return mergeMultipleLevels(levels);
        } else {
            return levels.get(0);
        }
    }

    /**
     * 合并多个指标等级（主要用于测试人员）
     */
    private String mergeMultipleLevels(List<String> levels) {
        double totalA = 0.0;
        int cCount = 0;
        for (String level : levels) {
            totalA += ScoreLevelUtil.convertLevelToAvalue(level);
            if (ScoreLevelEnum.SCORE_C.getCode().equals(level)) {
                cCount++;
            }
        }
        return ScoreLevelUtil.determineCategoryLevel(totalA, cCount,levels);
    }

    /**
     * 计算技术经理的最终等级
     */
    private String calculateFinalLevel(TeamCalculationData data) {
        if (data.memberLevels.isEmpty()) {
            log.warn("技术经理 {} 的团队无有效专业成员数据", data.managerName);
            return ScoreLevelEnum.SCORE_B.getCode();
        }

        // 统计各等级人数
        Map<String, Long> levelCounts = data.memberLevels.stream()
            .collect(Collectors.groupingBy(level -> level, Collectors.counting()));

        int sCount = levelCounts.getOrDefault(ScoreLevelEnum.SCORE_S.getCode(), 0L).intValue();
        int aCount = levelCounts.getOrDefault(ScoreLevelEnum.SCORE_A.getCode(), 0L).intValue();
        int bCount = levelCounts.getOrDefault(ScoreLevelEnum.SCORE_B.getCode(), 0L).intValue();
        int cCount = levelCounts.getOrDefault(ScoreLevelEnum.SCORE_C.getCode(), 0L).intValue();
        int dCount = levelCounts.getOrDefault(ScoreLevelEnum.SCORE_D.getCode(), 0L).intValue();

        // D级一票否决
        if (dCount > 0) {
            return ScoreLevelEnum.SCORE_D.getCode();
        }

        // C级占半数以上
        int totalMembers = data.memberLevels.size();
        if (cCount >= (totalMembers + 1) / CommConstants.CommonVal.TWO) {
            return ScoreLevelEnum.SCORE_C.getCode();
        }

        // 根据团队规模应用不同算法
        // 包含组长的总人数
        int totalTeamSize = data.activeMembers.size();

        if (totalTeamSize >= CommConstants.CommonVal.FOUR) {
            return calculateLevelForLargeTeam(totalTeamSize, sCount, aCount, bCount);
        } else {
            return calculateLevelForSmallTeam(totalTeamSize, sCount, aCount, bCount);
        }
    }

    /**
     * 计算大团队（>=4人，含组长）的等级
     * 算法：要求半数以上达到对应等级（计算时包含组长）
     */
    private String calculateLevelForLargeTeam(int totalTeamSize, int sCount, int aCount, int bCount) {
        // 半数以上的计算：(总人数 + 1) / 2，向上取整
        int halfOrMore = (totalTeamSize + 1) / 2;

        // S级：要求半数以上为S
        if (sCount >= halfOrMore) {
            return ScoreLevelEnum.SCORE_S.getCode();
        }

        // A级：要求半数以上为A或S
        if ((sCount + aCount) >= halfOrMore) {
            return ScoreLevelEnum.SCORE_A.getCode();
        }

        // B级：要求半数以上为B以上
        if ((sCount + aCount + bCount) >= halfOrMore) {
            return ScoreLevelEnum.SCORE_B.getCode();
        }

        return ScoreLevelEnum.SCORE_B.getCode();
    }

    /**
     * 计算小团队（<4人，含组长）的等级
     * 算法：要求全部组员达到对应等级（计算时排除组长）
     */
    private String calculateLevelForSmallTeam(int totalTeamSize, int sCount, int aCount, int bCount) {
        // 只看组员数量，排除组长
        int memberCount = totalTeamSize - 1;

        if (memberCount <= 0) {
            return ScoreLevelEnum.SCORE_B.getCode();
        }

        // S级：要求全部组员为S
        if (sCount == memberCount) {
            return ScoreLevelEnum.SCORE_S.getCode();
        }

        // A级：要求全部组员为A或S
        if ((sCount + aCount) == memberCount) {
            return ScoreLevelEnum.SCORE_A.getCode();
        }

        // B级：要求全部组员为B以上
        if ((sCount + aCount + bCount) == memberCount) {
            return ScoreLevelEnum.SCORE_B.getCode();
        }

        return ScoreLevelEnum.SCORE_B.getCode();
    }

    /**
     * 根据成员角色获取对应的指标列表
     */
    private List<String> getIndicatorsForMember(SysUser member) {
        List<String> indicators = new ArrayList<>();

        if (member.getRoles() != null && !member.getRoles().isEmpty()) {
            for (SysRole role : member.getRoles()) {
                Long roleId = role.getRoleId();
                if (PersonTypeEnum.DEVELOPER.getRoleId().equals(roleId)) {
                    indicators.add(PerformanceIndicatorEnum.CODE_LINES.getCode());
                    break;
                } else if (PersonTypeEnum.TESTER.getRoleId().equals(roleId)) {
                    indicators.add(PerformanceIndicatorEnum.CASE_EXECUTE_COUNT.getCode());
                    indicators.add(PerformanceIndicatorEnum.BUG_COUNT.getCode());
                    break;
                } else if (PersonTypeEnum.OPERATOR.getRoleId().equals(roleId)) {
                    indicators.add(PerformanceIndicatorEnum.PLAN_MAINTAIN_COUNT.getCode());
                    break;
                }
            }
        }

        return indicators;
    }

    /**
     * 获取成员的角色类型描述
     */
    private String getMemberRoleType(SysUser member) {
        if (member.getRoles() != null && !member.getRoles().isEmpty()) {
            for (SysRole role : member.getRoles()) {
                Long roleId = role.getRoleId();
                if (PersonTypeEnum.DEVELOPER.getRoleId().equals(roleId)) {
                    return "开发人员";
                } else if (PersonTypeEnum.TESTER.getRoleId().equals(roleId)) {
                    return "测试人员";
                } else if (PersonTypeEnum.OPERATOR.getRoleId().equals(roleId)) {
                    return "运维人员";
                }
            }
        }
        return "其他角色";
    }

    @Override
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level) {
        return "";
    }

    /**
     * 生成详细日志内容
     */
    private String generateLogContent(TeamCalculationData data, String level) {
        StringBuilder logContent = new StringBuilder();

        logContent.append(String.format("[%s]在%s年%s月份的月结果产出指标",
            data.managerName, data.year, data.month));

        // 基本统计信息
        logContent.append(String.format("，团队总人数：%d人", data.allMembers.size()));
        logContent.append(String.format("，有效成员数(排除请假)：%d人", data.activeMembers.size()));
        logContent.append(String.format("，专业成员数(排除请假并且有指标)：%d人", data.memberLevels.size()));
        logContent.append("，评级：").append(level);

        // 成员角色分布
        appendRoleDistribution(logContent, data);

        // 成员等级分布
        appendLevelDistribution(logContent, data);

        // 详细成员信息
        appendMemberDetails(logContent, data);

        // 请假成员信息
        if (!data.absentMembers.isEmpty()) {
            logContent.append("\n  请假成员（已排除）：");
            for (SysUser absentMember : data.absentMembers) {
                logContent.append(String.format("\n    %s：当月请假未上班", absentMember.getNickName()));
            }
        }

        // 评级原因
        String reason = getRatingReason(level, data);
        if (reason != null) {
            logContent.append("\n  计算原因：").append(reason);
        }

        return logContent.toString();
    }

    /**
     * 添加成员角色分布统计
     */
    private void appendRoleDistribution(StringBuilder logContent, TeamCalculationData data) {
        if (!data.memberRoleMap.isEmpty()) {
            Map<String, Long> roleDistribution = data.memberRoleMap.values().stream()
                .collect(Collectors.groupingBy(role -> role, Collectors.counting()));

            List<String> roleStats = new ArrayList<>();
            roleDistribution.forEach((roleType, count) ->
                roleStats.add(String.format("%s%d人", roleType, count)));

            logContent.append("\n  成员角色分布：").append(String.join("，", roleStats));
        }
    }

    /**
     * 添加成员等级分布统计
     */
    private void appendLevelDistribution(StringBuilder logContent, TeamCalculationData data) {
        if (!data.memberLevels.isEmpty()) {
            Map<String, Long> levelDistribution = data.memberLevels.stream()
                .collect(Collectors.groupingBy(level -> level, Collectors.counting()));

            List<String> levelStats = new ArrayList<>();
            for (String levelCode : Arrays.asList(ScoreLevelEnum.SCORE_S.getCode(), ScoreLevelEnum.SCORE_A.getCode(),
                ScoreLevelEnum.SCORE_B.getCode(), ScoreLevelEnum.SCORE_C.getCode(),
                ScoreLevelEnum.SCORE_D.getCode())) {
                Long count = levelDistribution.get(levelCode);
                if (count != null && count > 0) {
                    levelStats.add(String.format("%s级%d人", levelCode, count));
                }
            }
            if (!levelStats.isEmpty()) {
                logContent.append("\n  成员等级分布：").append(String.join("，", levelStats));
            }
        }
    }

    /**
     * 添加详细成员信息
     */
    private void appendMemberDetails(StringBuilder logContent, TeamCalculationData data) {
        if (!data.memberLevelMap.isEmpty()) {
            logContent.append("\n  成员指标等级详情：");
            for (Map.Entry<String, String> entry : data.memberLevelMap.entrySet()) {
                String memberName = entry.getKey();
                String memberLevel = entry.getValue();
                String roleType = data.memberRoleMap.get(memberName);

                logContent.append(String.format("\n    %s(%s)：%s级", memberName, roleType, memberLevel));
            }
        }
    }

    /**
     * 获取评级原因
     */
    private String getRatingReason(String level, TeamCalculationData data) {
        int totalMembers = data.memberLevels.size();
        int totalTeamSize = data.activeMembers.size();

        if (totalMembers == 0) {
            return "团队无有效专业成员数据";
        }

        Map<String, Long> levelCounts = data.memberLevels.stream()
            .collect(Collectors.groupingBy(l -> l, Collectors.counting()));

        int sCount = levelCounts.getOrDefault("S", 0L).intValue();
        int aCount = levelCounts.getOrDefault("A", 0L).intValue();
        int bCount = levelCounts.getOrDefault("B", 0L).intValue();
        int cCount = levelCounts.getOrDefault("C", 0L).intValue();
        int dCount = levelCounts.getOrDefault("D", 0L).intValue();

        switch (level) {
            case "S":
                if (totalTeamSize >= CommConstants.CommonVal.FOUR) {
                    int required = (totalTeamSize + 1) / 2;
                    return String.format("大团队(%d人)中%d人达到S级，满足半数以上要求(%d人)", totalTeamSize, sCount, required);
                } else {
                    return String.format("小团队(%d人)中%d名组员全部达到S级", totalTeamSize, totalTeamSize - 1);
                }
            case "A":
                if (totalTeamSize >= CommConstants.CommonVal.FOUR) {
                    int required = (totalTeamSize + 1) / 2;
                    return String.format("大团队(%d人)中%d人达到A级以上，满足半数以上要求(%d人)", totalTeamSize, sCount + aCount, required);
                } else {
                    return String.format("小团队(%d人)中%d名组员全部达到A级以上", totalTeamSize, totalTeamSize - 1);
                }
            case "B":
                if (totalTeamSize >= CommConstants.CommonVal.FOUR) {
                    int required = (totalTeamSize + 1) / 2;
                    return String.format("大团队(%d人)中%d人达到B级以上，满足半数以上要求(%d人)", totalTeamSize, sCount + aCount + bCount, required);
                } else {
                    return String.format("小团队(%d人)中%d名组员全部达到B级以上", totalTeamSize, totalTeamSize - 1);
                }
            case "C":
                if (cCount >= (totalMembers + 1) / CommConstants.CommonVal.TWO) {
                    return String.format("团队%d人中%d人为C级，占一半或以上", totalMembers, cCount);
                } else {
                    return String.format("团队%d人整体表现未达到更高等级要求", totalMembers);
                }
            case "D":
                return String.format("团队%d人中有%d人为D级", totalMembers, dCount);
            default:
                return null;
        }
    }

    /**
     * 团队计算数据类
     */
    private static class TeamCalculationData {
        String managerName;
        Integer year;
        Integer month;
        SysUser manager;
        List<SysUser> allMembers;
        List<SysUser> activeMembers;
        List<SysUser> absentMembers;
        List<Long> performanceIds;
        Map<Long, String> performanceIdToNickName;

        List<String> memberLevels;
        Map<String, String> memberLevelMap;
        Map<String, String> memberRoleMap;
    }
}
