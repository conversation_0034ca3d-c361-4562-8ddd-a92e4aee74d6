package com.qmqb.imp.web.controller.business;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.system.domain.bo.DataBasePermissionCreateBo;
import com.qmqb.imp.system.domain.bo.DataBasePermissionQueryBo;
import com.qmqb.imp.system.domain.bo.DataBasePermissionUpdateBo;
import com.qmqb.imp.system.domain.vo.DataBasePermissionVo;
import com.qmqb.imp.system.domain.vo.DbDeptVO;
import com.qmqb.imp.system.service.IDataBasePermissionService;
import com.qmqb.imp.system.service.ISysDeptService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据库权限
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/dataBasePermission")
public class DataBasePermissionController extends BaseController {

    private final IDataBasePermissionService iDataBasePermissionService;

    private final ISysDeptService sysDeptService;

    /**
     * 查询数据库权限列表
     */
    @SaCheckPermission("system:dataBasePermission:list")
    @GetMapping("/list")
    public TableDataInfo<DataBasePermissionVo> list(DataBasePermissionQueryBo bo, PageQuery pageQuery) {
        return iDataBasePermissionService.queryPageList(bo, pageQuery);
    }

    /**
     * 新增数据库权限
     */
    @SaCheckPermission("system:dataBasePermission:add")
    @Log(title = "数据库权限", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody DataBasePermissionCreateBo bo) {
        return toAjax(iDataBasePermissionService.insertByBo(bo));
    }

    /**
     * 修改数据库权限
     */
    @SaCheckPermission("system:dataBasePermission:edit")
    @Log(title = "数据库权限", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody DataBasePermissionUpdateBo bo) {
        return toAjax(iDataBasePermissionService.updateByBo(bo));
    }

    /**
     * 查询小组和人员
     */
    @GetMapping("/deptAndUser")
    public List<DbDeptVO> deptAndUser() {
        return sysDeptService.deptAndUser();
    }


}
