package com.qmqb.imp.system.service.impl.performance;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qmqb.imp.system.domain.performance.PerformanceFeedbackMainIndicatorResult;
import com.qmqb.imp.system.mapper.performance.PerformanceFeedbackMainIndicatorResultMapper;
import com.qmqb.imp.system.service.indicator.IPerformanceFeedbackMainIndicatorResultService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 绩效反馈主表与绩效指标结果中间表ServiceImpl
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Service
public class PerformanceFeedbackMainIndicatorResultServiceImpl extends ServiceImpl<PerformanceFeedbackMainIndicatorResultMapper, PerformanceFeedbackMainIndicatorResult>
        implements IPerformanceFeedbackMainIndicatorResultService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchByMainId(Long mainId, List<Long> indicatorResultIds) {
        if (mainId == null || indicatorResultIds == null || indicatorResultIds.isEmpty()) {
            return false;
        }
        List<PerformanceFeedbackMainIndicatorResult> list = new ArrayList<>();
        for (Long indicatorResultId : indicatorResultIds) {
            PerformanceFeedbackMainIndicatorResult entity = new PerformanceFeedbackMainIndicatorResult();
            entity.setMainId(mainId);
            entity.setIndicatorResultId(indicatorResultId);
            list.add(entity);
        }
        return this.saveBatch(list);
    }

    /**
     * 根据主表ID查询所有指标结果ID
     * @param mainId 主表ID
     * @return 指标结果ID列表
     */
    @Override
    public List<Long> getIndicatorResultIdsByMainId(Long mainId) {
        return this.baseMapper.selectIndicatorResultIdsByMainId(mainId);
    }
}
