package com.qmqb.imp.web.controller.business;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.dto.CodeQueryDTO;
import com.qmqb.imp.common.core.domain.vo.UserTeamSelectVO;
import com.qmqb.imp.common.core.validate.QueryGroup;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.bo.TimePeriodStatisticsInfoBO;
import com.qmqb.imp.system.domain.bo.WorkDetailBo;
import com.qmqb.imp.system.domain.vo.CodeStatisticsVO;
import com.qmqb.imp.system.domain.vo.GitStatisticsGroupInfoVO;
import com.qmqb.imp.system.domain.vo.GitStatisticsInfoExportVO;
import com.qmqb.imp.system.domain.vo.GitStatisticsInfoVO;
import com.qmqb.imp.system.domain.vo.TimePeriodStatisticsInfoVO;
import com.qmqb.imp.system.service.IGitStatisticsInfoService;
import com.qmqb.imp.system.service.GitStatisticsService;
import lombok.RequiredArgsConstructor;

import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 代码统计控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@RestController
@RequiredArgsConstructor
    @RequestMapping("/code/query")
public class GitLabCodeController extends BaseController {

    private final IGitStatisticsInfoService gitStatisticsInfoService;
    private final GitStatisticsService gitStatisticsService;

    /**
     * 分页查询代码统计信息
     * @param request
     * @return
     */
    @GetMapping("/page")
    public R<Page<GitStatisticsInfoVO>> page(CodeQueryDTO request) {
        return R.ok(gitStatisticsInfoService.page(request));
    }

    /**
     * 个人提交明细
     * @param workDetail
     * @return
     */
    @GetMapping("/userCommitList")
    public R<Page<GitStatisticsInfoVO>> getUserCommitList(@Validated(QueryGroup.class) WorkDetailBo workDetail) {
        return R.ok(gitStatisticsInfoService.getUserCommitList(workDetail));
    }

    /**
     * 导出个人提交明细
     * @param workDetail
     * @param response
     */
    @PostMapping("/userCommitList/export")
    public void exportUserCommitList(WorkDetailBo workDetail, HttpServletResponse response) {
        workDetail.setPageSize(Integer.MAX_VALUE);
        Page<GitStatisticsInfoVO> page = gitStatisticsInfoService.getUserCommitList(workDetail);
        List<GitStatisticsInfoVO> list = page.getRecords();
        List<GitStatisticsInfoExportVO> exportList = list.stream().map(item -> {
            GitStatisticsInfoExportVO exportVO = new GitStatisticsInfoExportVO();
            BeanUtils.copyProperties(item, exportVO);
            return exportVO;
        }).collect(Collectors.toList());
        ExcelUtil.exportCsv(exportList, "代码提交明细", GitStatisticsInfoExportVO.class, response);
    }

    @GetMapping("/teamSelectList")
    public R<List<UserTeamSelectVO>> getUserTeamSelectList(){
        return R.ok(gitStatisticsInfoService.getDept());
    }


    /**
     * 年度工作量曲线
     *
     * @param year         年份
     * @param deptId       开发组
     * @param isNotInclude 不包含技术经理：false-否，true-是
     * @return
     */
    @GetMapping("/statistics")
    public R<List<CodeStatisticsVO>> statistics(@RequestParam Integer year,
                                                @RequestParam Long deptId,
                                                @RequestParam Boolean isNotInclude) {
        return gitStatisticsService.statistics(year, deptId, isNotInclude);
    }

    @GetMapping("/getTimePeriodLeftStatisticsInfoPage")
    public R<Page<GitStatisticsGroupInfoVO>> getTimePeriodLeftStatisticsInfoPage(TimePeriodStatisticsInfoBO request) {
        return R.ok(gitStatisticsInfoService.getTimePeriodStatisticsInfo(request));
    }

    @GetMapping("/getTimePeriodRightStatisticsInfoPage")
    public R<Page<GitStatisticsGroupInfoVO>> getTimePeriodRightStatisticsInfoPage(TimePeriodStatisticsInfoBO request) {
        return R.ok(gitStatisticsInfoService.getTimePeriodStatisticsInfo(request));
    }



    @GetMapping("/getTimePeriodStatisticsHistogram")
    public R<TimePeriodStatisticsInfoVO> getTimePeriodStatisticsHistogram(TimePeriodStatisticsInfoBO request){
        return R.ok(gitStatisticsInfoService.getTimePeriodStatisticsHistogram(request));
    }
}
