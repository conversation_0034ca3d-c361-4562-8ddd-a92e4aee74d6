<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.DataBasePermissionMapper">

    <resultMap type="com.qmqb.imp.system.domain.DataBasePermission" id="DataBasePermissionResult">
        <result property="id" column="id"/>
        <result property="dbName" column="db_name"/>
        <result property="dbRemark" column="db_remark"/>
        <result property="groupIds" column="group_ids"/>
        <result property="memberIds" column="member_ids"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>


</mapper>
