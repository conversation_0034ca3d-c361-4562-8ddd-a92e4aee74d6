CREATE TABLE `tb_process_operation_users`
(
    `id`                  bigint(20) NOT NULL COMMENT '主键',
    `process_code`        varchar(100) NOT NULL COMMENT '审批流的唯一码',
    `process_instance_id` varchar(255) NOT NULL COMMENT '审批实例业务编号',
    `originator_user_id`  varchar(50)  NOT NULL COMMENT '发起人的dinguserId。',
    `operation_user_ids`  text         NOT NULL COMMENT '流程操作人dinguserid',
    `originator_time`     datetime     NOT NULL COMMENT '创建时间',
    `create_time`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
    `update_time`         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据表修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uidx_pid` (`process_instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='流程操作人记录';


-- 新增流程统计菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`,
                                   `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (1900814109581635586, '流程统计', 1868939801902202881, 0, 'processStat', 'process/processStat/index', NULL, 1, 0, 'C', '0', '0',
        'system:processStat:query', 'cascader', 'admin', '2025-03-15 15:39:34', 'admin', '2025-03-17 10:29:00', '');

