package com.qmqb.imp.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025-07-28 13:22
 * 请假类型枚举
 */
@Getter
public enum UserLeaveTypeEnum {

    /**
     * 请假类型
     */
    ANNUAL_LEAVE("年假", "1"),
    PERSONAL_LEAVE("事假", "2"),
    SICK_LEAVE("病假", "3"),
    COMPENSATORY_LEAVE("调休", "4"),
    MATERNITY_LEAVE("产假", "5"),
    PATERNITY_LEAVE("陪产假", "6"),
    MARRIAGE_LEAVE("婚假", "7"),
    PRENATAL_CHECKUP_LEAVE("产检假", "8"),
    BEREAVEMENT_LEAVE("丧假", "9"),
    PARENTING_LEAVE("育儿假", "10"),
    CARE_LEAVE("护理假", "11");

    private final String description;
    private final String code;

    UserLeaveTypeEnum(String description, String code) {
        this.description = description;
        this.code = code;
    }

    /**
     * 根据描述模糊获取枚举
     */
    public static UserLeaveTypeEnum getByDescription(String description) {
        for (UserLeaveTypeEnum type : values()) {
            if (description.contains(type.getDescription())) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据code获取枚举
     */
    public static UserLeaveTypeEnum getByCode(String code) {
        for (UserLeaveTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

}
