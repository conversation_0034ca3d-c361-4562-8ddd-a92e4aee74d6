
#注意：1、上线前要把部门编码的数据设置好；2、核对一下是不是每个用户都有对应的角色

CREATE TABLE `tb_warn_record` (
                                  `id` bigint(20) NOT NULL COMMENT '主键',
                                  `warn_code` varchar(22) NOT NULL COMMENT '预警编号',
                                  `warn_id` bigint(20) NOT NULL COMMENT '预警配置id',
                                  `warn_type` tinyint(2) DEFAULT '1' COMMENT '预警类型(1-考勤预警；2-任务预警；3-代码预警；4-用例预警；5-项目预警；6-同步预警；7-文档预警；8-管理预警)',
                                  `user_id` bigint(20) NOT NULL COMMENT '用户id',
                                  `nick_name` varchar(30) DEFAULT '' COMMENT '用户姓名',
                                  `warn_level` tinyint(2) DEFAULT '0' COMMENT '预警级别(0=P0; 1=P1; 2=P3; 3=P3; 4=P4;)',
                                  `warn_content` varchar(50) NOT NULL COMMENT '预警内容',
                                  `batch_no` bigint(20) NOT NULL COMMENT '批次号',
                                  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                                  `version` int(11) DEFAULT '0' COMMENT '版本号',
                                  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                  `create_time` datetime NOT NULL COMMENT '创建时间',
                                  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                  `update_time` datetime NOT NULL COMMENT '更新时间',
                                  PRIMARY KEY (`id`),
                                  KEY `idx_user_id` (`user_id`),
                                  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='预警记录';

CREATE TABLE `tb_warn_record_detail` (
                                         `id` bigint(20) NOT NULL COMMENT '主键',
                                         `warn_record_id` bigint(20) NOT NULL COMMENT '预警记录id',
                                         `warn_way` tinyint(2) DEFAULT '0' COMMENT '预警方式(1-钉钉；2-邮件；4-短信；8-电话)',
                                         `warn_status` tinyint(2) DEFAULT '0' COMMENT '预警状态(0=未通知; 1=通知失败; 2=已通知)',
                                         `handle_status` tinyint(2) DEFAULT '0' COMMENT '处理状态(0=待处理; 2=已处理;)',
                                         `handle_content` varchar(50) DEFAULT NULL COMMENT '处理内容',
                                         `warn_nums` tinyint(2) DEFAULT '1' COMMENT '预警次数',
                                         `warn_time` datetime NOT NULL COMMENT '预警时间',
                                         `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                                         `version` int(11) DEFAULT '0' COMMENT '版本号',
                                         `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                         `create_time` datetime NOT NULL COMMENT '创建时间',
                                         `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                         `update_time` datetime NOT NULL COMMENT '更新时间',
                                         PRIMARY KEY (`id`),
                                         KEY `idx_warn_record_id` (`warn_record_id`),
                                         KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='预警记录明细';

CREATE TABLE `tb_warn_config` (
                                  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                  `warn_type` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '预警类型：1-考勤预警；2-任务预警；3-代码预警；4-用例预警；5-项目预警；6-同步预警；7-文档预警；8-管理预警',
                                  `warn_object` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '预警对象：1-全员（总监例外）；2-全员；3-组长；4-组员；5-测试组长；6-开发组长；7-测试组员；8-开发组员；9-运维组长；10-运维组；11-数据；12-数据组长；13-BI组长；14-BI组员；15-项目经理；16-项目负责人（禅道项目的负责人）；17-总监',
                                  `warn_level` tinyint(2) unsigned NOT NULL DEFAULT '3' COMMENT '预警级别：0-P0；1-P1；1-P2',
                                  `warn_code` varchar(50) NOT NULL DEFAULT '' COMMENT '预警编码',
                                  `trigger_rule_id` bigint(20) NOT NULL COMMENT '触发规则ID',
                                  `trigger_method` int(11) unsigned NOT NULL DEFAULT '1' COMMENT '触发方式：通过位运算表示状态，可多选值；1-钉钉；2-邮件；4-短信；8-电话',
                                  `warn_content` varchar(256) DEFAULT '' COMMENT '预警内容：参数：{月}、{组内预警人数}、{月-1}、{月+1}',
                                  `statistics_scope` tinyint(2) NOT NULL DEFAULT '1' COMMENT '统计范围：1-本月；2-本周；3-上周；4-上月；5-全年；6-上年',
                                  `is_used` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '预警开关：0-关，1-开',
                                  `remark` varchar(256) DEFAULT NULL COMMENT '备注',
                                  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
                                  `version` int(11) DEFAULT '0' COMMENT '版本号',
                                  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                  `create_time` datetime NOT NULL COMMENT '创建时间',
                                  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                  `update_time` datetime NOT NULL COMMENT '更新时间',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COMMENT='预警配置表';

CREATE TABLE `tb_warn_trigger_rule` (
                                        `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                        `warn_type` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '预警类型：1-考勤预警；2-任务预警；3-代码预警；4-用例预警；5-项目预警；6-同步预警；7-文档预警；8-管理预警',
                                        `rule_name` varchar(256) NOT NULL DEFAULT '' COMMENT '规则名称',
                                        `rule_type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '规则类型：例如：1-考勤预警：1-本月迟到N次进行预警；2-任务预警：1-连续N个工作日没进行任务，2-进行中的任务连续N个工作日没进行更新',
                                        `compare_type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '比对类型: 1-平均值, 2-求和值, 3-最大值, 4-最小值, 5-次数, 6-最后一次的值',
                                        `compare_symbol` tinyint(2) NOT NULL DEFAULT '1' COMMENT '比对符号: 1->=, 2-> , 3-<=, 4-< , 5-=, 6-!=',
                                        `compare_value` decimal(18,2) NOT NULL COMMENT '比对值',
                                        `remark` varchar(256) DEFAULT NULL COMMENT '备注',
                                        `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                        `create_time` datetime NOT NULL COMMENT '创建时间',
                                        `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                        `update_time` datetime NOT NULL COMMENT '更新时间',
                                        PRIMARY KEY (`id`),
                                        UNIQUE KEY `uk_warn_type_rule_name` (`warn_type`,`rule_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='预警配置触发规则表';


ALTER table sys_dept add COLUMN dept_code varchar(20) not null COMMENT '部门编码' AFTER dept_id;

ALTER TABLE sys_dept add UNIQUE KEY uk_dept_code(dept_code);

