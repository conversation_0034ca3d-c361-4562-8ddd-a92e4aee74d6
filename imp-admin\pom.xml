<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>imp-parent</artifactId>
        <groupId>com.qmqb.imp</groupId>
        <version>4.4.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>imp-admin</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>

        <!-- spring-boot-devtools -->
<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-devtools</artifactId>-->
<!--            <optional>true</optional> &lt;!&ndash; 表示依赖不会传递 &ndash;&gt;-->
<!--        </dependency>-->

        <!-- Mysql驱动包 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <!--        &lt;!&ndash; Oracle &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>com.oracle.database.jdbc</groupId>-->
        <!--            <artifactId>ojdbc8</artifactId>-->
        <!--        </dependency>-->
        <!--        &lt;!&ndash; PostgreSql &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>org.postgresql</groupId>-->
        <!--            <artifactId>postgresql</artifactId>-->
        <!--        </dependency>-->
        <!--        &lt;!&ndash; SqlServer &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>com.microsoft.sqlserver</groupId>-->
        <!--            <artifactId>mssql-jdbc</artifactId>-->
        <!--        </dependency>-->

        <!-- 核心模块-->
        <dependency>
            <groupId>com.qmqb.imp</groupId>
            <artifactId>imp-framework</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qmqb.imp</groupId>
            <artifactId>imp-system</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qmqb.imp</groupId>
            <artifactId>imp-job</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qmqb.imp</groupId>
            <artifactId>imp-oss</artifactId>
        </dependency>

        <!-- 代码生成-->
        <dependency>
            <groupId>com.qmqb.imp</groupId>
            <artifactId>imp-generator</artifactId>
        </dependency>

        <!--  demo模块  -->
        <dependency>
            <groupId>com.qmqb.imp</groupId>
            <artifactId>imp-demo</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 钉钉旧版服务端API -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
        </dependency>

        <!-- skywalking 整合 logback -->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.skywalking</groupId>-->
        <!--            <artifactId>apm-toolkit-logback-1.x</artifactId>-->
        <!--            <version>${与你的agent探针版本保持一致}</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.apache.skywalking</groupId>-->
        <!--            <artifactId>apm-toolkit-trace</artifactId>-->
<!--            <version>${与你的agent探针版本保持一致}</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.qmqb.structure</groupId>
            <artifactId>spring-boot-block-sql-starter</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
