package com.qmqb.imp.system.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-05-27 18:15
 */
@Data
public class SlowSqlStatVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "数据库名")
    private String dbName;

    @Schema(description = "各月慢SQL数量")
    private List<Integer> monthOfSlowSqlCount;

}
