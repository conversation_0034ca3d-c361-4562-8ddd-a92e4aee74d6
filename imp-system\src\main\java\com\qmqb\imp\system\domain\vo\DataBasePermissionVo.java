package com.qmqb.imp.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.qmqb.imp.common.annotation.ExcelDictFormat;
import com.qmqb.imp.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 数据库权限视图对象 tb_data_base_permission
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@ExcelIgnoreUnannotated
public class DataBasePermissionVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */

    private Long id;

    /**
     * 数据库名
     */

    private String dbName;

    /**
     * 数据库备注
     */

    private String dbRemark;

    /**
     * 组id列表（逗号隔开）
     */

    private String groupIds;

    /**
     * 成员id列表（逗号隔开）
     */

    private String memberIds;

    /**
     * 组名称列表（逗号隔开）
     */
    private String groupNames;

    /**
     * 成员名称列表（逗号隔开）
     */
    private String memberNames;


}
