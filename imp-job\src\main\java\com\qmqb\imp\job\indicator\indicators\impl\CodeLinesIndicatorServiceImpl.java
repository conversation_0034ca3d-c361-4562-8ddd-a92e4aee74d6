package com.qmqb.imp.job.indicator.indicators.impl;

import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 月新增代码行指标等级计算
 *
 * <AUTHOR>
 */
@Service
public class CodeLinesIndicatorServiceImpl implements IndicatorLevelCalcService {

    /**
     * S级标准：至少4000行且部门排名前10%
     */
    private static final int S_LEVEL_CODE_LINES = 4000;
    private static final double S_LEVEL_PERCENTILE = 0.1;

    /**
     * A级标准：至少2000行且部门排名前20%
     */
    private static final int A_LEVEL_CODE_LINES = 2000;
    private static final double A_LEVEL_PERCENTILE = 0.2;

    /**
     * B级标准：至少1000行
     */
    private static final int B_LEVEL_CODE_LINES = 1000;

    /**
     * C级标准：至少500行
     */
    private static final int C_LEVEL_CODE_LINES = 500;

    @Override
    public String getIndicatorCode() {
        return PerformanceIndicatorEnum.CODE_LINES.getCode();
    }

    @Override
    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults) {
        int codeLines = Optional.ofNullable(workResult.getPushCodeLines()).orElse(0);
        if (codeLines < C_LEVEL_CODE_LINES) {
            return ScoreLevelEnum.SCORE_D.getCode();
        }
        if (codeLines < B_LEVEL_CODE_LINES) {
            return ScoreLevelEnum.SCORE_C.getCode();
        }
        if (codeLines < A_LEVEL_CODE_LINES) {
            return ScoreLevelEnum.SCORE_B.getCode();
        }
        // 代码行数大于等于2000，直接走部门排名逻辑
        List<Integer> departmentCodeLines = getDepartmentCodeLines(trackWorkResults);
        if (departmentCodeLines.isEmpty()) {
            if (codeLines >= S_LEVEL_CODE_LINES) {
                return ScoreLevelEnum.SCORE_S.getCode();
            } else {
                return ScoreLevelEnum.SCORE_A.getCode();
            }
        }
        double percentile = calculatePercentile(codeLines, departmentCodeLines);
        if (codeLines >= S_LEVEL_CODE_LINES && percentile <= S_LEVEL_PERCENTILE) {
            return ScoreLevelEnum.SCORE_S.getCode();
        } else if (percentile <= A_LEVEL_PERCENTILE) {
            return ScoreLevelEnum.SCORE_A.getCode();
        } else {
            return ScoreLevelEnum.SCORE_B.getCode();
        }
    }

    @Override
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level) {
        int codeLines = Optional.ofNullable(workResult.getPushCodeLines()).orElse(0);
        Integer year = workResult.getWorkYear();
        Integer month = workResult.getWorkMonth();
        StringBuilder logContent = new StringBuilder();
        logContent.append(String.format("[%s]在%s年%s月份新增代码行数：%d行",
            nickName, year, month, codeLines));
        logContent.append(String.format("，评级：%s", level));
        String reason = getRatingReason(codeLines, level, workResult, nickName);
        if (reason != null) {
            logContent.append(String.format("，原因：%s", reason));
        }
        return logContent.toString();
    }

    /**
     * 获取同部门所有员工的代码行数列表（包括当前员工）
     */
    private List<Integer> getDepartmentCodeLines(List<TrackWorkResultVO> trackWorkResults) {
        return trackWorkResults.stream()
            .map(result -> Optional.ofNullable(result.getPushCodeLines()).orElse(0))
            .sorted(Comparator.reverseOrder())
            .collect(Collectors.toList());
    }

    /**
     * 计算指定值在列表中的百分位数
     *
     * @param value      要计算的值
     * @param sortedList 已排序的列表（降序）
     * @return 百分位数（0-1之间，越小表示排名越靠前）
     */
    private double calculatePercentile(int value, List<Integer> sortedList) {
        if (sortedList.isEmpty()) {
            return 0.0;
        }
        int position = 0;
        for (int i = 0; i < sortedList.size(); i++) {
            if (value >= sortedList.get(i)) {
                position = i + 1 ;
                break;
            }
            position = i + 1;
        }
        return (double) position / sortedList.size();
    }

    /**
     * 获取评级原因
     */
    private String getRatingReason(int codeLines, String level, TrackWorkResultVO workResult, String nickName) {
        switch (level) {
            case "S":
                return String.format("代码行数(%d)达到S级标准(≥%d行)且部门排名前10%%", codeLines, S_LEVEL_CODE_LINES);
            case "A":
                return String.format("代码行数(%d)达到A级标准(≥%d行)且部门排名前20%%", codeLines, A_LEVEL_CODE_LINES);
            case "B":
                return String.format("代码行数(%d)达到B级标准(≥%d行)", codeLines, B_LEVEL_CODE_LINES);
            case "C":
                return String.format("代码行数(%d)达到C级标准(≥%d行)", codeLines, C_LEVEL_CODE_LINES);
            case "D":
                return String.format("代码行数(%d)未达到C级标准(<%d行)", codeLines, C_LEVEL_CODE_LINES);
            default:
                return null;
        }
    }
}
