package com.qmqb.imp.system.domain.performance;

import com.baomidou.mybatisplus.annotation.*;
import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.Date;

/**
 * 绩效辅导对象 tb_performance_tutoring
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_performance_tutoring")
@Builder
public class PerformanceTutoring extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 绩效反馈表id(tb_performance_feedback)
     */
    private Long feedbackId;
    /**
     * 辅导概要
     */
    private String tutoringSummary;
    /**
     * 辅导结果
     */
    private String tutoringResult;
    /**
     * 辅导人
     */
    private String tutor;
    /**
     * 辅导时间
     */
    private LocalDate tutoringTime;
    /**
     * 辅导附件
     */
    private String tutoringAttachment;
    /**
     * 总监建议
     */
    private String directorSuggest;
    /**
     * 建议时间
     */
    private Date suggestTime;

}
