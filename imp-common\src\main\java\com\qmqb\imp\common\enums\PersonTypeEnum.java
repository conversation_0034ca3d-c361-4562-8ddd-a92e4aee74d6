package com.qmqb.imp.common.enums;

import com.qmqb.imp.common.constant.CommConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 * @Date 2022/12/6
 */
@Getter
@AllArgsConstructor
public enum PersonTypeEnum {

    /**
     * 人员类型
     */
    ALL(1, "全部", null),
    TECHNICAL_MANAGER(2, "技术经理", 2L),
    DEVELOPER(3, "开发人员", 3L),
    TESTER(4, "测试人员", 4L),
    NON_GROUP_LEADER(5, "非组长", null),
    PROJECT_MANAGER(6, "项目经理", 6L),
    OPERATOR(7, "运维人员", 7L)
    ;

    private final Integer type;
    private final String desc;
    private final Long roleId;

    public static List<Long> getRoleIdListFromType(Integer type) {
        if (type == 1) {
            return Arrays.asList(TECHNICAL_MANAGER.getRoleId(), DEVELOPER.getRoleId(), TESTER.getRoleId(), PROJECT_MANAGER.getRoleId(), OPERATOR.getRoleId());
        } else if (type == CommConstants.CommonVal.TWO) {
            //技术经理
            return Collections.singletonList(TECHNICAL_MANAGER.getRoleId());
        } else if (type == CommConstants.CommonVal.THREE) {
            //开发(包括运维)
            return Collections.singletonList(DEVELOPER.getRoleId());
        } else if (type == CommConstants.CommonVal.FOUR) {
            //测试
            return Collections.singletonList(TESTER.getRoleId());
        } else if (type == CommConstants.CommonVal.FIVE) {
            //非组长
            return Arrays.asList(DEVELOPER.getRoleId(), TESTER.getRoleId(), OPERATOR.getRoleId());
        } else if (type == CommConstants.CommonVal.SIX) {
            //项管
            return Collections.singletonList(PROJECT_MANAGER.getRoleId());
        } else if (type == CommConstants.CommonVal.SEVEN) {
            //运维
            return Collections.singletonList(OPERATOR.getRoleId());
        }
        else {
            return null;
        }
    }


    /**
     * 根据岗位类型获取人员类型
     * @param postType
     * @return
     */
    public static List<Long> getRoleIdListFromPostType(Integer postType) {
        if (postType==null || postType == 1) {
            return Arrays.asList(TECHNICAL_MANAGER.getRoleId(), DEVELOPER.getRoleId(), TESTER.getRoleId(), PROJECT_MANAGER.getRoleId(), OPERATOR.getRoleId());
        } else if (postType == CommConstants.CommonVal.TWO) {
            //技术经理
            return Collections.singletonList(TECHNICAL_MANAGER.getRoleId());
        } else if (postType == CommConstants.CommonVal.THREE) {
            //开发(包括运维)
            return Arrays.asList(DEVELOPER.getRoleId());
        } else if (postType == CommConstants.CommonVal.FOUR) {
            //测试
            return Collections.singletonList(TESTER.getRoleId());
        }  else if (postType == CommConstants.CommonVal.FIVE) {
            //项管
            return Collections.singletonList(PROJECT_MANAGER.getRoleId());
        } else if (postType == CommConstants.CommonVal.SIX) {
            //运维
            return Collections.singletonList(OPERATOR.getRoleId());
        } else {
            return null;
        }
    }

    /**
     * 根据 type 获取对应的枚举实例
     *
     * @param type 人员类型编码
     * @return 对应的 PersonTypeEnum 实例，如果未找到则返回 null
     */
    public static PersonTypeEnum fromType(Integer type) {
        for (PersonTypeEnum personType : values()) {
            if (personType.getType().equals(type)) {
                return personType;
            }
        }
        return null;
    }

}
