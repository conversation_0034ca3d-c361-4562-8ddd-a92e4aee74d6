package com.qmqb.imp.common.utils;

import com.qmqb.imp.common.enums.ScoreLevelEnum;

import java.util.List;
import java.util.Objects;

/**
 * 绩效等级判定工具类
 *
 * <AUTHOR>
 */
public class ScoreLevelUtil {

    /**
     * 分类等级阈值常量：用于根据总A级数量确定分类等级
     * S级：4个A及以上
     * A级：2-3个A
     * B级：0-1个A或C<1
     * C级：C>1
     */
    private static final double S_LEVEL_THRESHOLD = 4.0;
    private static final double A_LEVEL_THRESHOLD = 2.0;
    private static final double B_LEVEL_THRESHOLD = 0.0;
    private static final int C_COUNT_THRESHOLD = 1;

    /**
     * A级转换常量：用于将不同等级转换为对应的A级数量值
     * S级 = 2个A, A级 = 1个A, B级 = 0个A,
     * C级 = -0.5个A（抵消2个A）, D级 = -1个A
     */
    private static final double S_LEVEL_A_VALUE = 2.0;
    private static final double A_LEVEL_A_VALUE = 1.0;
    private static final double B_LEVEL_A_VALUE = 0.0;
    private static final double C_LEVEL_A_VALUE = -2;
    private static final double D_LEVEL_A_VALUE = -4;

    /**
     * 根据A级数量和C级数量确定分类等级
     */
    public static String determineCategoryLevel(double totalA, int cCount,List<String> levels) {
        // totalA：为负数（抵消不完）取最低绩效
        if (totalA < 0) {
            return getMinScoreLevel(levels);
        }
        // totalA：为正数（抵消完）按规则转换
        if (totalA >= S_LEVEL_THRESHOLD) {
            return ScoreLevelEnum.SCORE_S.getCode();
        } else if (totalA >= A_LEVEL_THRESHOLD) {
            return ScoreLevelEnum.SCORE_A.getCode();
        }  else if (totalA >= B_LEVEL_THRESHOLD) {
            return ScoreLevelEnum.SCORE_B.getCode();
        } else {
            return ScoreLevelEnum.SCORE_C.getCode();
        }
    }

    /**
     * 根据A级数量和C级数量确定分类等级
     * 反馈-> 二类
     * 换算时，1个S可换成2个A，1个C可以抵消2个A，最终对比A的数量得到该项的绩效评分
     * 4个A：S    2-3个A：A    0-1个A或C≤1：B      C>1：C    只要有一个D：D
     *
     * C的数量使用抵消过后剩下的C数量
     *
     */
    public static String determineFeedBackCategoryLevel(double totalA, int cCount, List<String> levels) {
        // 使用getUnbalancedLevels计算cCount
        int unbalancedccount = 0;
        List<String> unbalancedLevels = getUnbalancedLevels(levels);
        for (String level : unbalancedLevels) {
            if (ScoreLevelEnum.SCORE_C.getCode().equals(level)) {
                unbalancedccount++;
            }
        }
        if (totalA >= S_LEVEL_THRESHOLD) {
            return ScoreLevelEnum.SCORE_S.getCode();
        } else if (totalA >= A_LEVEL_THRESHOLD) {
            return ScoreLevelEnum.SCORE_A.getCode();
        } else if (totalA >= B_LEVEL_THRESHOLD || unbalancedccount <= C_COUNT_THRESHOLD) {
            return ScoreLevelEnum.SCORE_B.getCode();
        } else {
            return ScoreLevelEnum.SCORE_C.getCode();
        }
    }

    /**
     * 获取一组绩效等级中的最低等级
     *
     * @param levels 绩效等级列表（如 S、A、B、C、D）
     * @return 最低绩效等级（如有D则返回D，依次类推）
     */
    public static String getMinScoreLevel(List<String> levels) {
        if (levels == null || levels.isEmpty()) {
            // 默认返回B级
            return ScoreLevelEnum.SCORE_B.getCode();
        }
        // 定义等级优先级，数值越大等级越低
        int minRank = Integer.MIN_VALUE;
        String minLevel = ScoreLevelEnum.SCORE_B.getCode();
        for (String level : levels) {
            ScoreLevelEnum levelEnum = ScoreLevelEnum.getByCode(level);
            if (levelEnum == null) {
                continue;
            }
            int rank = getLevelRank(levelEnum);
            if (rank > minRank) {
                minRank = rank;
                minLevel = levelEnum.getCode();
            }
        }
        return minLevel;
    }

    /**
     * 获取绩效等级的优先级，数值越大等级越低
     *
     * @param levelEnum 绩效等级枚举
     * @return 优先级
     */
    private static int getLevelRank(ScoreLevelEnum levelEnum) {
        switch (levelEnum) {
            case SCORE_S:
                return 1;
            case SCORE_A:
                return 2;
            case SCORE_C:
                return 4;
            case SCORE_D:
                return 5;
            default:
                return 3;
        }
    }

    /**
     * 将等级转换为A级数量
     * S级 = 2个A, A级 = 1个A, B级 = 0个A, C级 = -0.5个A, D级 = -1个A
     *
     * @param level 等级
     * @return A级数量
     */
    public static double convertLevelToAvalue(String level) {
        ScoreLevelEnum levelEnum = ScoreLevelEnum.getByCode(level);
        if (levelEnum == null) {
            // 非法等级默认按B级处理
            return B_LEVEL_A_VALUE;
        }

        switch (levelEnum) {
            case SCORE_S:
                return S_LEVEL_A_VALUE;
            case SCORE_A:
                return A_LEVEL_A_VALUE;
            case SCORE_C:
                return C_LEVEL_A_VALUE;
            case SCORE_D:
                return D_LEVEL_A_VALUE;
            default:
                return B_LEVEL_A_VALUE;
        }
    }

    /**
     * 按照S、A、B、C、D顺序对绩效等级列表排序
     *
     * @param levels 原始绩效等级列表
     * @return 排序后的等级列表
     */
    public static List<String> sortLevels(List<String> levels) {
        if (levels == null || levels.isEmpty()) {
            return new java.util.ArrayList<>();
        }
        java.util.List<String> sorted = new java.util.ArrayList<>(levels);
        sorted.sort((l1, l2) -> {
            int r1 = getLevelRank(Objects.requireNonNull(ScoreLevelEnum.getByCode(l1)));
            int r2 = getLevelRank(Objects.requireNonNull(ScoreLevelEnum.getByCode(l2)));
            return Integer.compare(r1, r2);
        });
        return sorted;
    }

    /**
     * 获取抵消不完的绩效等级列表
     * <p>
     * 按照S=2A，A=1A，C=-2A，D=-4A，B=0A的规则，将levels换算成A的总数，
     * 累加过程中一旦A总数为负，则返回当前及后续未被抵消的等级列表。
     *
     * @param levels 原始绩效等级列表
     * @return 抵消不完的等级列表
     */
    public static List<String> getUnbalancedLevels(List<String> levels) {
        List<String> sortedLevels = sortLevels(levels);
        List<String> unbalanced = new java.util.ArrayList<>();
        if (sortedLevels.isEmpty()) {
            return unbalanced;
        }
        double aSum = 0;
        for (int i = 0; i < sortedLevels.size(); i++) {
            aSum += convertLevelToAvalue(sortedLevels.get(i));
            if (aSum < 0) {
                unbalanced.addAll(sortedLevels.subList(i, sortedLevels.size()));
                break;
            }
        }
        return unbalanced;
    }
}
