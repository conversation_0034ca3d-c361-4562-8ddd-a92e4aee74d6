package com.qmqb.imp.web.controller.business.process;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.system.domain.bo.process.ProcessStatBo;
import com.qmqb.imp.system.domain.vo.process.ProcessStatListVo;
import com.qmqb.imp.system.domain.vo.process.ProcessStatVo;
import com.qmqb.imp.system.service.process.IProcessStatService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @className: ProcessStatController
 * @author: yang<PERSON><PERSON>an
 * @description:
 * @date: 2025/3/15 16:55
 * @version: 1.0
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/processStat")
public class ProcessStatController {

    private final IProcessStatService ipStatService;

    /**
     * 获取风控规则开发需求详细信息
     *
     * @param statBo 主键
     */
    @SaCheckPermission("system:processStat:query")
    @GetMapping("/list")
    public R<ProcessStatVo> getProcessStatList(ProcessStatBo statBo) {
        return R.ok(ipStatService.getProcessStatList(statBo));
    }
}
