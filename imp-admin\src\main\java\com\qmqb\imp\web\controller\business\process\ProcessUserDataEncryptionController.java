package com.qmqb.imp.web.controller.business.process;



import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.system.domain.vo.process.ProcessUserDataEncryptionVo;
import com.qmqb.imp.system.domain.bo.process.ProcessUserDataEncryptionBo;
import com.qmqb.imp.system.service.process.IProcessUserDataEncryptionService;
import com.qmqb.imp.common.core.page.TableDataInfo;

/**
 * 敏感用户数据加解密
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/processUserDataEncryption")
public class ProcessUserDataEncryptionController extends BaseController {

    private final IProcessUserDataEncryptionService iProcessUserDataEncryptionService;

    /**
     * 查询敏感用户数据加解密列表
     */
    @SaCheckPermission("system:processUserDataEncryption:list")
    @GetMapping("/list")
    public TableDataInfo<ProcessUserDataEncryptionVo> list(ProcessUserDataEncryptionBo bo, PageQuery pageQuery) {
        return iProcessUserDataEncryptionService.queryPageList(bo, pageQuery);
    }


}
