package com.qmqb.imp.app.controller;

import com.qmqb.imp.app.domain.bo.WarnHandleBO;
import com.qmqb.imp.app.domain.bo.WarnListBO;
import com.qmqb.imp.app.domain.vo.WarnDetailVO;
import com.qmqb.imp.app.service.WarningPageService;
import com.qmqb.imp.common.core.domain.R;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 预警页
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/warning-page")
public class WarningPageController {

    private final WarningPageService warningPageService;

    /**
     * 个人预警列表
     *
     * @return 结果
     */
    @GetMapping("/list")
    public R<List<WarnDetailVO>> list(@Valid WarnListBO bo) {
        return warningPageService.list(bo);
    }

    /**
     * 预警处理
     *
     * @return 结果
     */
    @PostMapping("/handle")
    public R<Void> handle(@Validated @RequestBody WarnHandleBO bo) {
        return warningPageService.handle(bo);
    }


    /**
     * 全部预警列表
     *
     * @return 结果
     */
    @GetMapping("/all")
    public R<List<WarnDetailVO>> all(@Valid WarnListBO bo) {
        return warningPageService.all(bo);
    }
}
