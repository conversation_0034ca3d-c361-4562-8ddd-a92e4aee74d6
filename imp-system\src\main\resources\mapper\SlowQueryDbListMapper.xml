<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.SlowQueryDbListMapper">

    <resultMap type="com.qmqb.imp.system.domain.SlowQueryInfo" id="SlowQueryInfoResult">
        <result property="id" column="id"/>
        <result column="DBName" property="dbName" jdbcType="VARCHAR"/>
        <result column="IP" property="ip" jdbcType="VARCHAR"/>
        <result column="User" property="user" jdbcType="VARCHAR"/>
        <result column="last_executionStartTime" property="lastExecutionStartTime" jdbcType="TIMESTAMP"/>
        <result column="last_parseRowCounts" property="lastParseRowCounts" jdbcType="VARCHAR"/>
        <result column="last_returnRowCounts" property="lastReturnRowCounts" jdbcType="VARCHAR"/>
        <result column="last_queryTimeMS" property="lastQueryTimeMs" jdbcType="BIGINT"/>
        <result column="SQLHash" property="sqlHash" jdbcType="VARCHAR"/>
        <result column="total_query_times" property="totalQueryTimes" jdbcType="BIGINT"/>
        <result column="total_sum_time" property="totalSumTime" jdbcType="BIGINT"/>
        <result column="avg_query_time" property="avgQueryTime" jdbcType="BIGINT"/>
        <result column="avg_returnRowCounts" property="avgReturnRowCounts" jdbcType="BIGINT"/>
        <result column="avg_parseRowCounts" property="avgParseRowCounts" jdbcType="BIGINT"/>
        <result column="slow_rule" property="slowRule" jdbcType="VARCHAR"/>
        <result column="warn_level" property="warnLevel" jdbcType="VARCHAR"/>
    </resultMap>




    <select id="slowSqlTop" resultType="com.qmqb.imp.system.domain.vo.SlowQueryTopVO">
        select * from ${topName} where SQLHash in
        <foreach collection="sqlHashList" separator="," item="sqlHash" open="(" close=")">
            #{sqlHash}
        </foreach>
    </select>




</mapper>

