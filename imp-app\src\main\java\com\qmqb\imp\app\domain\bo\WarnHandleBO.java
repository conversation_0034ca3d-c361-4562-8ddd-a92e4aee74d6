package com.qmqb.imp.app.domain.bo;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 预警处理业务对象
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Data
public class WarnHandleBO {

    /**
     * 要处理的预警id
     */
    @NotNull(message = "预警id不能为空")
    private Long id;

    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空")
    @Length(max = 100, message = "回复内容限制100字内")
    private String content;

}
