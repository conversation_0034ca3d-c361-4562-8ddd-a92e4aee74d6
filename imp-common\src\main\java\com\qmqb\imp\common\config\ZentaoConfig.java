package com.qmqb.imp.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 禅道API配置
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
@Component
@ConfigurationProperties(prefix = "zentao.api")
public class ZentaoConfig {

    /**
     * 禅道API基础URL
     */
    private String url;

    /**
     * 禅道登录用户名
     */
    private String username;

    /**
     * 禅道登录密码
     */
    private String password;

    /**
     * 执行ID（用于创建任务）
     */
    private Long executionId;
} 