package com.qmqb.imp.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.qmqb.imp.common.core.domain.dto.StoryStatQueryDTO;
import com.qmqb.imp.common.enums.ZtStoryStatusEnum;
import com.qmqb.imp.system.domain.Story;
import com.qmqb.imp.system.domain.ZtProduct;
import com.qmqb.imp.system.domain.vo.StoryStatVO;
import com.qmqb.imp.system.service.IStoryService;
import com.qmqb.imp.system.service.IZtProductService;
import com.qmqb.imp.system.service.StoryStatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 工作成果跟踪
 *
 * <AUTHOR>
 * @since 2024-11-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class StoryStatServiceImpl implements StoryStatService {

    @Autowired
    private IStoryService storyService;

    @Autowired
    private IZtProductService ztProductService;

    /**
     * 获取需求统计数据
     *
     * @param dto
     * @return
     */
    @Override
    @DS("zentao")
    public List<StoryStatVO> getStoryStat(StoryStatQueryDTO dto) {
        // 组装数据
        dto.setBeginDate(StoryStatQueryDTO.beginToDate(dto.getBeginDate()));
        dto.setEndDate(StoryStatQueryDTO.endToDate(dto.getEndDate()));
        List<Story> storyList = storyService.list(Wrappers.lambdaQuery(Story.class)
            .select(Story::getProduct, Story::getStatus)
            .ge(Objects.nonNull(dto.getBeginDate()), Story::getOpeneddate, dto.getBeginDate())
            .le(Objects.nonNull(dto.getEndDate()), Story::getOpeneddate, dto.getEndDate())
            .eq(Story::getDeleted, "0"));
        if (CollUtil.isEmpty(storyList)) {
            return Collections.emptyList();
        }
        Set<Integer> productIds = storyList.stream().map(Story::getProduct).collect(Collectors.toSet());
        List<ZtProduct> ztProducts = ztProductService.productNameListByIds(productIds);
        // 按产品名称和状态分组
        Map<Integer, Map<String, Long>> groupedStories = storyList.stream()
            .collect(Collectors.groupingBy(Story::getProduct,
                Collectors.groupingBy(Story::getStatus, Collectors.counting())));

        List<StoryStatVO> vos = ztProducts.stream()
            .map(ztProduct -> {
                StoryStatVO vo = StoryStatVO.builder()
                    .productId(ztProduct.getId())
                    .product(ztProduct.getName()).activeCount(0).closedCount(0).draftCount(0).changeCount(0).build();
                Map<String, Long> statusCounts = groupedStories.get(ztProduct.getId());
                if (Objects.nonNull(statusCounts)) {
                    vo.setDraftCount(Math.toIntExact(statusCounts.getOrDefault(ZtStoryStatusEnum.DRAFT.getValue(), 0L)));
                    vo.setActiveCount(Math.toIntExact(statusCounts.getOrDefault(ZtStoryStatusEnum.ACTIVE.getValue(), 0L)));
                    vo.setChangeCount(Math.toIntExact(statusCounts.getOrDefault(ZtStoryStatusEnum.CHANGED.getValue(), 0L)));
                    vo.setClosedCount(Math.toIntExact(statusCounts.getOrDefault(ZtStoryStatusEnum.CLOSED.getValue(), 0L)));
                }
                return vo;
            })
            // 添加排序逻辑，默认按活跃数从大到小排序
            .sorted(Comparator.comparingInt(StoryStatVO::getActiveCount).reversed())
            .collect(Collectors.toList());
        return vos;
    }
}
