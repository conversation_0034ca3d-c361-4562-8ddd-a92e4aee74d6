package com.qmqb.imp.system.domain.performance;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 绩效反馈对象 tb_performance_feedback
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_performance_feedback")
public class PerformanceFeedback extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 主反馈表ID（关联主反馈表）
     */
    private Long mainFeedbackId;

    /**
     * 所属组ID
     */
    private Long groupId;

    /**
     * 所属组
     */
    private String groupName;
    /**
     * 一类指标
     */
    private String primaryIndicator;

    /**
     * 二类指标
     */
    private String secondaryIndicator;

    /**
     * 事件标题
     */
    private String eventTitle;

    /**
     * 事件明细
     */
    private String eventDetail;

    /**
     * 推荐绩效级别
     */
    private String recommendedLevel;

    /**
     * 推荐原因
     */
    private String recommendedReason;

    /**
     * 员工昵称
     */
    private String nickName;

    /**
     * 绩效年份
     */
    private Integer year;

    /**
     * 绩效月份
     */
    private Integer month;

    /**
     * 角色类型
     */
    private String personType;

    /**
     * 是否已取消（0 否，1 是）
     */
    private Integer isCanceled;
}
