package com.qmqb.imp.test;

import com.qmqb.imp.app.domain.bo.DeptKeyIndicatorBO;
import com.qmqb.imp.app.domain.vo.KeyIndicatorVO;
import com.qmqb.imp.app.service.HomepageService;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
@Slf4j
public class KeyIndicatorTest {

    @Resource
    private HomepageService homepageService;

    @Test
    public void projectCountTest() {
        //项目总数
        DeptKeyIndicatorBO bo = new DeptKeyIndicatorBO();
        bo.setDeptId(117L);
        bo.setType(6);
        R<KeyIndicatorVO> res = homepageService.keyIndicator(bo, bo.getDeptId());
        log.info(JsonUtils.toJsonString(res));
    }

    @Test
    public void releaseCountTest() {
        //上线数
        DeptKeyIndicatorBO bo = new DeptKeyIndicatorBO();
        bo.setDeptId(117L);
        bo.setType(7);
        R<KeyIndicatorVO> res = homepageService.keyIndicator(bo, bo.getDeptId());
        log.info(JsonUtils.toJsonString(res));
    }

    @Test
    public void prodFaultCountTest() {
        //生产故障数
        DeptKeyIndicatorBO bo = new DeptKeyIndicatorBO();
        bo.setDeptId(117L);
        bo.setType(8);
        R<KeyIndicatorVO> res = homepageService.keyIndicator(bo, bo.getDeptId());
        log.info(JsonUtils.toJsonString(res));
    }

    @BeforeEach
    public void beforeEach() {
        //登录
        LoginUser loginUser = new LoginUser();
        loginUser.setUserId(1602235239812698114L);
        loginUser.setUserType("app_user");
        LoginHelper.login(loginUser);
    }

}
