package com.qmqb.imp.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzed.structure.tool.enums.BoolFlagEnum;
import com.qmqb.imp.app.domain.bo.DateBO;
import com.qmqb.imp.app.domain.bo.KeyIndicatorBO;
import com.qmqb.imp.app.domain.enums.IndicatorType;
import com.qmqb.imp.app.domain.vo.KeyIndicatorVO;
import com.qmqb.imp.app.domain.vo.MessageInfoVO;
import com.qmqb.imp.app.domain.vo.PerformanceEvaluateBizVO;
import com.qmqb.imp.app.domain.vo.PerformanceReportVO;
import com.qmqb.imp.app.service.HomepageService;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.enums.WarnLevelEnum;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.domain.*;
import com.qmqb.imp.system.domain.vo.DingTalkProcessInstanceTaskVO;
import com.qmqb.imp.system.domain.vo.DingTalkProcessInstanceVO;
import com.qmqb.imp.system.domain.vo.GitStatisticsInfoVO;
import com.qmqb.imp.system.mapper.DingTalkProcessInstanceMapper;
import com.qmqb.imp.system.mapper.DingTalkProcessInstanceTaskMapper;
import com.qmqb.imp.system.mapper.GitStatisticsInfoMapper;
import com.qmqb.imp.system.mapper.WarnRecordMapper;
import com.qmqb.imp.system.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HomepageServiceImpl implements HomepageService {

    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final WarnRecordMapper warnRecordMapper;
    private final ISysUserService sysUserService;
    private final IUserKqStatService userKqStatService;
    private final IWorkStatService workStatService;
    private final IZtDocService ztDocService;
    private final GitStatisticsInfoMapper gitStatisticsInfoMapper;
    private final IPerformanceEvaluateService performanceEvaluateService;
    private final DingTalkProcessInstanceTaskMapper dingTalkProcessInstanceTaskMapper;
    private final DingTalkProcessInstanceMapper dingTalkProcessInstanceMapper;

    /**
     * 指标值排序器，默认正序
     */
    private final Comparator<KeyIndicatorVO.Indicator> valueComparator = Comparator.comparing(KeyIndicatorVO.Indicator::getValue);

    /**
     * 消息处理情况
     *
     * @return
     */
    @Override
    public R<MessageInfoVO> messageInfo() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        Long userId = loginUser.getUserId();
        SysUser sysUser = sysUserService.selectUserById(userId);
        List<SysUser> sysUsers;
        if (sysUser.isAdmin() || sysUser.isJszxAdmin()) {
            sysUsers = sysUserService.selectAllUser();
        } else {
            sysUsers = sysUserService.selectUserByDeptId(loginUser.getDeptId());
        }
        QueryWrapper<WarnRecord> wrapper = Wrappers.query();
        wrapper.in("t1.`user_id`", sysUsers.stream().map(SysUser::getUserId).collect(Collectors.toList()));
        wrapper.eq("t2.`handle_status`", BoolFlagEnum.NO.getStatus());
        List<WarnRecord> warnRecords = warnRecordMapper.selectWarnRecordList(wrapper);
        long personalCount = warnRecords.stream().filter(warnRecord -> Objects.equals(userId, warnRecord.getUserId())).count();
        long personalP0Count = warnRecords.stream().filter(warnRecord -> Objects.equals(userId, warnRecord.getUserId()) && Objects.equals(WarnLevelEnum.P0.getValue(), warnRecord.getWarnLevel())).count();
        long personalP1Count = warnRecords.stream().filter(warnRecord -> Objects.equals(userId, warnRecord.getUserId()) && Objects.equals(WarnLevelEnum.P1.getValue(), warnRecord.getWarnLevel())).count();
        long personalP2Count = warnRecords.stream().filter(warnRecord -> Objects.equals(userId, warnRecord.getUserId()) && Objects.equals(WarnLevelEnum.P2.getValue(), warnRecord.getWarnLevel())).count();

        long groupCount = warnRecords.size();
        long groupP0Count = warnRecords.stream().filter(warnRecord -> Objects.equals(WarnLevelEnum.P0.getValue(), warnRecord.getWarnLevel())).count();
        long groupP1Count = warnRecords.stream().filter(warnRecord -> Objects.equals(WarnLevelEnum.P1.getValue(), warnRecord.getWarnLevel())).count();
        long groupP2Count = warnRecords.stream().filter(warnRecord -> Objects.equals(WarnLevelEnum.P2.getValue(), warnRecord.getWarnLevel())).count();

        MessageInfoVO vo = MessageInfoVO.builder()
            .personal(personalCount)
            .group(groupCount)
            .p0(MessageInfoVO.Count.builder().self(personalP0Count).group(groupP0Count).build())
            .p1(MessageInfoVO.Count.builder().self(personalP1Count).group(groupP1Count).build())
            .p2(MessageInfoVO.Count.builder().self(personalP2Count).group(groupP2Count).build())
            .build();

        return R.ok(vo);
    }

    /**
     * 个人绩效报告
     *
     * @param bo
     * @return
     */
    @Override
    public R<PerformanceReportVO> personalPerformanceReport(DateBO bo, Long userId) {
        int year = DateUtil.year(bo.getDate());
        int month = DateUtil.month(bo.getDate()) + 1;
        SysUser sysUser = sysUserService.selectUserById(userId);
        PerformanceReportVO vo = PerformanceReportVO.builder().name(sysUser.getNickName()).build();

        DateTime beginOfMonth = DateUtil.beginOfMonth(bo.getDate());
        DateTime endOfMonth = DateUtil.endOfMonth(bo.getDate());

        // 异步处理各模块数据
        CompletableFuture<Void> attendanceFuture = handlePersonalAttendanceData(vo, year, month, sysUser);
        CompletableFuture<Void> docFuture = handlePersonalDocumentData(vo, sysUser, beginOfMonth, endOfMonth);
        CompletableFuture<Void> taskFuture = handlePersonalTaskData(vo, year, month, sysUser);
        CompletableFuture<Void> codeFuture = handlePersonalCodeData(vo, sysUser, beginOfMonth, endOfMonth);
        CompletableFuture<Void> warnFuture = handlePersonalWarningData(vo, userId, beginOfMonth, endOfMonth);

        try {
            CompletableFuture.allOf(attendanceFuture, docFuture, taskFuture, codeFuture, warnFuture).get();
            return R.ok(vo);
        } catch (InterruptedException | ExecutionException e) {
            log.error("获取数据异常", e);
            return R.fail("获取数据异常");
        }
    }

    /**
     * 处理个人考勤数据
     */
    private CompletableFuture<Void> handlePersonalAttendanceData(PerformanceReportVO vo, int year, int month, SysUser sysUser) {
        return CompletableFuture.supplyAsync(() -> {
            UserKqStat stat = userKqStatService.queryByUserNameAndKqYearAndKqMonth(
                sysUser.getNickName(), String.valueOf(year), String.valueOf(month));

            if (Objects.isNull(stat)) {
                PerformanceReportVO.ManHour manHour = PerformanceReportVO.ManHour.builder()
                    .day(BigDecimal.ZERO)
                    .total(BigDecimal.ZERO)
                    .build();
                vo.setManHour(manHour);
                vo.setAttendanceDay(BigDecimal.ZERO);
            } else {
                if (Objects.nonNull(stat.getKqAttendanceWorkTime()) && Objects.nonNull(stat.getKqAttendanceDays())) {
                    BigDecimal day = stat.getKqAttendanceDays() == 0L ? BigDecimal.ZERO :
                        NumberUtil.div(stat.getKqAttendanceWorkTime(), stat.getKqAttendanceDays())
                            .divide(BigDecimal.valueOf(60), 2, RoundingMode.UP);
                    BigDecimal total = NumberUtil.div(stat.getKqAttendanceWorkTime(), BigDecimal.valueOf(60), 2);

                    PerformanceReportVO.ManHour manHour = PerformanceReportVO.ManHour.builder()
                        .day(day)
                        .total(total)
                        .build();
                    vo.setManHour(manHour);
                    vo.setAttendanceDay(BigDecimal.valueOf(stat.getKqAttendanceDays()));
                } else {
                    PerformanceReportVO.ManHour manHour = PerformanceReportVO.ManHour.builder()
                        .day(BigDecimal.ZERO)
                        .total(BigDecimal.ZERO)
                        .build();
                    vo.setManHour(manHour);
                    vo.setAttendanceDay(BigDecimal.valueOf(stat.getKqAttendanceDays()));
                }
            }
            return stat;
        }, threadPoolTaskExecutor).thenApply(result -> null);
    }

    /**
     * 处理个人文档数据
     */
    private CompletableFuture<Void> handlePersonalDocumentData(PerformanceReportVO vo, SysUser sysUser,
                                                              DateTime beginOfMonth, DateTime endOfMonth) {
        return CompletableFuture.supplyAsync(() -> {
            Long count = ztDocService.countByAddedByAndAddedDate(
                Collections.singleton(sysUser.getZtUserName()), beginOfMonth, endOfMonth);
            vo.setDocs(Objects.isNull(count) ? BigDecimal.ZERO : BigDecimal.valueOf(count));
            return count;
        }, threadPoolTaskExecutor).thenApply(result -> null);
    }

    /**
     * 处理个人任务数据
     */
    private CompletableFuture<Void> handlePersonalTaskData(PerformanceReportVO vo, int year, int month, SysUser sysUser) {
        return CompletableFuture.supplyAsync(() -> {
            WorkStat workStat = workStatService.queryByUserNameAndYearAndMonth(
                sysUser.getDept().getDeptId(), sysUser.getNickName(), year, month);

            if (Objects.isNull(workStat)) {
                vo.setCompletedTasks(BigDecimal.ZERO);
                vo.setCases(BigDecimal.ZERO);
                vo.setCreatedBugs(BigDecimal.ZERO);
                vo.setResolvedBugs(BigDecimal.ZERO);
            } else {
                vo.setCompletedTasks(BigDecimal.valueOf(workStat.getWorkDoneClosedTaskCount()));
                vo.setCases(BigDecimal.valueOf(workStat.getWorkCaseCount()));
                vo.setCreatedBugs(BigDecimal.valueOf(workStat.getCreateBugCount()));
                vo.setResolvedBugs(BigDecimal.valueOf(workStat.getWorkResolveBugCount()));
            }
            return workStat;
        }, threadPoolTaskExecutor).thenApply(result -> null);
    }

    /**
     * 处理个人代码数据
     */
    private CompletableFuture<Void> handlePersonalCodeData(PerformanceReportVO vo, SysUser sysUser,
                                                          DateTime beginOfMonth, DateTime endOfMonth) {
        return CompletableFuture.supplyAsync(() -> {
            List<GitStatisticsInfo> gitStatisticsInfos = gitStatisticsInfoMapper.selectList(
                Wrappers.lambdaQuery(GitStatisticsInfo.class)
                    .in(GitStatisticsInfo::getCommitter, sysUser.getGitAuthorName(), sysUser.getGitCommitterName())
                    .le(GitStatisticsInfo::getAdditionsLine, 1000)
                    .between(GitStatisticsInfo::getCommitDate, beginOfMonth, endOfMonth)
                    .apply("message NOT LIKE '%Merge%'")
                    .apply("message NOT LIKE '%merge%'")
                    .apply("message NOT LIKE '%合并分支%'")
                    .apply("message NOT LIKE '%Conflict%'"));

            if (CollUtil.isEmpty(gitStatisticsInfos)) {
                vo.setCodes(BigDecimal.ZERO);
            } else {
                BigDecimal codes = gitStatisticsInfos.stream()
                    .map(e -> BigDecimal.valueOf(e.getTotalLine()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setCodes(codes);
            }
            return gitStatisticsInfos;
        }, threadPoolTaskExecutor).thenApply(result -> null);
    }

    /**
     * 处理个人预警数据
     */
    private CompletableFuture<Void> handlePersonalWarningData(PerformanceReportVO vo, Long userId,
                                                             DateTime beginOfMonth, DateTime endOfMonth) {
        return CompletableFuture.supplyAsync(() -> {
            QueryWrapper<WarnRecord> wrapper = Wrappers.query();
            wrapper.eq("t1.`user_id`", userId);
            wrapper.between("t1.`create_time`", beginOfMonth, endOfMonth);
            List<WarnRecord> warnRecords = warnRecordMapper.selectWarnRecordList(wrapper);

            if (CollUtil.isEmpty(warnRecords)) {
                vo.setWarning(PerformanceReportVO.Warning.builder()
                    .total(BigDecimal.ZERO)
                    .unprocessed(BigDecimal.ZERO)
                    .build());
            } else {
                vo.setWarning(PerformanceReportVO.Warning.builder()
                    .total(BigDecimal.valueOf(warnRecords.size()))
                    .unprocessed(BigDecimal.valueOf(warnRecords.stream()
                        .filter(warnRecord -> Objects.equals(
                            warnRecord.getDetail().getHandleStatus(),
                            BoolFlagEnum.NO.getStatus()))
                        .count()))
                    .build());
            }
            return warnRecords;
        }, threadPoolTaskExecutor).thenApply(result -> null);
    }

    /**
     * 组绩效报告
     */
    @Override
    public R<PerformanceReportVO> teamPerformanceReport(DateBO bo) {
        // 初始化基础数据
        int year = DateUtil.year(bo.getDate());
        int month = DateUtil.month(bo.getDate()) + 1;
        LoginUser loginUser = LoginHelper.getLoginUser();
        SysUser sysUser = sysUserService.selectUserById(loginUser.getUserId());
        List<SysUser> sysUsers = getUserList(sysUser);

        PerformanceReportVO vo = PerformanceReportVO.builder().name(sysUser.getDept().getDeptName()).build();
        int peopleNumber = sysUsers.size();
        DateTime beginOfMonth = DateUtil.beginOfMonth(bo.getDate());
        DateTime endOfMonth = DateUtil.endOfMonth(bo.getDate());

        // 异步处理各模块数据
        CompletableFuture<List<UserKqStat>> attendanceFuture = handleAttendanceData(vo, year, month, sysUsers, peopleNumber);
        CompletableFuture<Long> docFuture = handleDocumentData(vo, sysUsers, beginOfMonth, endOfMonth, peopleNumber);
        CompletableFuture<List<WorkStat>> taskFuture = handleTaskData(vo, year, month, sysUsers, peopleNumber);
        CompletableFuture<List<GitStatisticsInfo>> codeFuture = handleCodeData(vo, sysUsers, beginOfMonth, endOfMonth, peopleNumber);
        CompletableFuture<List<WarnRecord>> warnFuture = handleWarningData(vo, sysUsers, beginOfMonth, endOfMonth);

        try {
            CompletableFuture.allOf(attendanceFuture, docFuture, taskFuture, codeFuture, warnFuture).get();
        } catch (InterruptedException | ExecutionException e) {
            log.error("获取数据异常", e);
            return R.fail("获取数据异常");
        }
        return R.ok(vo);
    }

    /**
     * 获取用户列表
     */
    private List<SysUser> getUserList(SysUser sysUser) {
        if (sysUser.isAdmin() || sysUser.isJszxAdmin()) {
            return sysUserService.selectAllUser();
        } else {
            return sysUserService.selectUserByDeptId(sysUser.getDeptId());
        }
    }

    /**
     * 处理考勤数据
     */
    private CompletableFuture<List<UserKqStat>> handleAttendanceData(PerformanceReportVO vo, int year, int month,
            List<SysUser> sysUsers, int peopleNumber) {
        List<String> nickNames = sysUsers.stream().map(SysUser::getNickName).collect(Collectors.toList());
        return CompletableFuture.supplyAsync(() -> {
            List<UserKqStat> stats = userKqStatService.listByUserNamesAndKqYearAndKqMonth(nickNames, String.valueOf(year), String.valueOf(month));
            if (CollUtil.isEmpty(stats)) {
                PerformanceReportVO.ManHour manHour = PerformanceReportVO.ManHour.builder().day(BigDecimal.ZERO).total(BigDecimal.ZERO).build();
                vo.setManHour(manHour);
                vo.setAttendanceDay(BigDecimal.ZERO);
            } else {
                // 平均每日工时=工作时长(分)/出勤天数
                BigDecimal totalPeopleDailyTime = stats.stream().map(e -> {
                    // 每人每日平均工时
                    BigDecimal peopleDailyTime = BigDecimal.ZERO;
                    if (!NumberUtil.equals(e.getKqAttendanceDays(), CommConstants.CommonVal.ZERO)) {
                        peopleDailyTime = NumberUtil.div(BigDecimal.valueOf(e.getKqAttendanceWorkTime()), BigDecimal.valueOf(e.getKqAttendanceDays()), 2);
                    }
                    // 转换成小时
                    return NumberUtil.div(peopleDailyTime, 60, 2);
                }).reduce(BigDecimal.ZERO, BigDecimal::add);
                PerformanceReportVO.ManHour manHour = PerformanceReportVO.ManHour.builder()
                    .day(NumberUtil.div(totalPeopleDailyTime, peopleNumber, 2))
                    .build();
                vo.setManHour(manHour);
                BigDecimal totalDay = stats.stream().map(e -> BigDecimal.valueOf(e.getKqAttendanceDays())).reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setAttendanceDay(NumberUtil.div(totalDay, peopleNumber, 2));
            }
            return stats;
        }, threadPoolTaskExecutor);
    }

    /**
     * 处理文档数据
     */
    private CompletableFuture<Long> handleDocumentData(PerformanceReportVO vo, List<SysUser> sysUsers,
            DateTime beginOfMonth, DateTime endOfMonth, int peopleNumber) {
        List<String> ztUserNames = sysUsers.stream().map(SysUser::getZtUserName).collect(Collectors.toList());
        return CompletableFuture.supplyAsync(() -> {
            Long count = ztDocService.countByAddedByAndAddedDate(ztUserNames, beginOfMonth, endOfMonth);
            vo.setDocs(NumberUtil.div(BigDecimal.valueOf(count), peopleNumber, 2));
            return count;
        }, threadPoolTaskExecutor);
    }

    /**
     * 处理任务数据
     */
    private CompletableFuture<List<WorkStat>> handleTaskData(PerformanceReportVO vo, int year, int month,
            List<SysUser> sysUsers, int peopleNumber) {
        List<String> nickNames = sysUsers.stream().map(SysUser::getNickName).collect(Collectors.toList());
        return CompletableFuture.supplyAsync(() -> {
            List<WorkStat> workStats = workStatService.listByUserNameAndYearAndMonth(nickNames, year, month);
            if (CollUtil.isEmpty(workStats)) {
                vo.setCompletedTasks(BigDecimal.ZERO);
                vo.setCases(BigDecimal.ZERO);
                vo.setCreatedBugs(BigDecimal.ZERO);
                vo.setResolvedBugs(BigDecimal.ZERO);
            } else {
                vo.setCompletedTasks(NumberUtil.div(workStats.stream()
                    .map(workStat -> BigDecimal.valueOf(workStat.getWorkDoneClosedTaskCount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add), peopleNumber, 2));
                vo.setCases(NumberUtil.div(workStats.stream()
                    .map(workStat -> BigDecimal.valueOf(workStat.getWorkCaseCount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add), peopleNumber, 2));
                vo.setCreatedBugs(NumberUtil.div(workStats.stream()
                    .map(workStat -> BigDecimal.valueOf(workStat.getCreateBugCount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add), peopleNumber, 2));
                vo.setResolvedBugs(NumberUtil.div(workStats.stream()
                    .map(workStat -> BigDecimal.valueOf(workStat.getWorkResolveBugCount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add), peopleNumber, 2));
            }
            return workStats;
        }, threadPoolTaskExecutor);
    }

    /**
     * 处理代码数据
     */
    private CompletableFuture<List<GitStatisticsInfo>> handleCodeData(PerformanceReportVO vo, List<SysUser> sysUsers,
            DateTime beginOfMonth, DateTime endOfMonth, int peopleNumber) {
        return CompletableFuture.supplyAsync(() -> {
            List<String> gitAuthorNames = sysUsers.stream().map(SysUser::getGitAuthorName).collect(Collectors.toList());
            List<String> gitCommitterNames = sysUsers.stream().map(SysUser::getGitCommitterName).collect(Collectors.toList());
            Set<String> committers = new HashSet<>();
            committers.addAll(gitAuthorNames);
            committers.addAll(gitCommitterNames);
            List<GitStatisticsInfo> gitStatisticsInfos = gitStatisticsInfoMapper.selectList(Wrappers.lambdaQuery(GitStatisticsInfo.class)
                .in(GitStatisticsInfo::getCommitter, committers)
                .le(GitStatisticsInfo::getAdditionsLine, 1000)
                .between(GitStatisticsInfo::getCommitDate, beginOfMonth, endOfMonth)
                .apply("message NOT LIKE '%Merge%'")
                .apply("message NOT LIKE '%merge%'")
                .apply("message NOT LIKE '%合并分支%'")
                .apply("message NOT LIKE '%Conflict%'"));
            if (CollUtil.isEmpty(gitStatisticsInfos)) {
                vo.setCodes(BigDecimal.ZERO);
            } else {
                BigDecimal codes = gitStatisticsInfos.stream().map(e -> BigDecimal.valueOf(e.getAdditionsLine())).reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setCodes(NumberUtil.div(codes, peopleNumber, 2));
            }
            return gitStatisticsInfos;
        }, threadPoolTaskExecutor);
    }

    /**
     * 处理预警数据
     */
    private CompletableFuture<List<WarnRecord>> handleWarningData(PerformanceReportVO vo, List<SysUser> sysUsers,
            DateTime beginOfMonth, DateTime endOfMonth) {
        return CompletableFuture.supplyAsync(() -> {
            QueryWrapper<WarnRecord> wrapper = Wrappers.query();
            wrapper.in("t1.`user_id`", sysUsers.stream().map(SysUser::getUserId).collect(Collectors.toList()));
            wrapper.between("t1.`create_time`", beginOfMonth, endOfMonth);
            List<WarnRecord> warnRecords = warnRecordMapper.selectWarnRecordList(wrapper);
            if (CollUtil.isEmpty(warnRecords)) {
                vo.setWarning(PerformanceReportVO.Warning.builder().total(BigDecimal.ZERO).unprocessed(BigDecimal.ZERO).build());
            } else {
                vo.setWarning(PerformanceReportVO.Warning.builder()
                    .total(BigDecimal.valueOf(warnRecords.size()))
                    .unprocessed(BigDecimal.valueOf(warnRecords.stream()
                        .filter(warnRecord -> Objects.equals(warnRecord.getDetail().getHandleStatus(), BoolFlagEnum.NO.getStatus()))
                        .count()))
                    .build());
            }
            return warnRecords;
        }, threadPoolTaskExecutor);
    }

    /**
     * 最新绩效点评
     *
     * @return
     */
    @Override
    public R<PerformanceEvaluateBizVO> performanceEvaluate() {
        Long userId = LoginHelper.getUserId();
        PerformanceEvaluate evaluate = performanceEvaluateService.queryLatestByUserId(userId);
        if (Objects.isNull(evaluate)) {
            return R.ok();
        }
        PerformanceEvaluateBizVO vo = BeanUtil.copyProperties(evaluate, PerformanceEvaluateBizVO.class);
        vo.setEvaluateTime(DateUtil.format(evaluate.getEvaluateTime(), DatePattern.CHINESE_DATE_PATTERN));
        return R.ok(vo);
    }

    /**
     * 关键指标
     *
     * @param bo
     * @return
     */
    @Override
    public R<KeyIndicatorVO> keyIndicator(KeyIndicatorBO bo, Long deptId) {
        IndicatorType type = IndicatorType.indexOf(bo.getType());
        boolean isDetail = Objects.nonNull(deptId);
        LoginUser loginUser = LoginHelper.getLoginUser();
        SysUser sysUser = sysUserService.selectUserById(loginUser.getUserId());
        String deptName = "";
        List<SysUser> sysUsers;
        // 部门关键指标页
        if (isDetail) {
            if (Objects.equals(-1L, deptId)) {
                sysUsers = sysUserService.selectAllUser();
                deptName = "技术中心";
            } else {
                sysUsers = sysUserService.selectUserByDeptId(deptId);
                Optional<SysUser> any = sysUsers.stream().findAny();
                if (any.isPresent()) {
                    deptName = any.get().getDept().getDeptName();
                }
            }
        } else {
            // 首页关键指标
            if (sysUser.isAdmin() || sysUser.isJszxAdmin()) {
                sysUsers = sysUserService.selectAllUser();
                deptName = "技术中心";
            } else {
                sysUsers = sysUserService.selectUserByDeptId(loginUser.getDeptId());
                Optional<SysUser> any = sysUsers.stream().findAny();
                if (any.isPresent()) {
                    deptName = any.get().getDept().getDeptName();
                }
            }
        }
        switch (type) {
            case TASK_COUNT:
                // 任务总数
                return R.ok(handleTaskCount(sysUsers, deptName, isDetail));
            case WORK_TIME:
                // 工作耗时
                return R.ok(handleWorkTime(sysUsers, deptName, isDetail));
            case DOC_COUNT:
                // 文档数
                return R.ok(handleDocCount(sysUsers, deptName, isDetail));
            case CODE_COUNT:
                // 代码总数
                return R.ok(handleCodeCount(sysUsers, deptName, isDetail));
            case CASE_COUNT:
                // 用例总数
                return R.ok(handleCaseCount(sysUsers, deptName, isDetail));
            case PROJECT_COUNT:
                // 项目总数
                return R.ok(handleProjectCount(sysUsers, deptName, isDetail));
            case RELEASE_COUNT:
                // 上线数
                return R.ok(handleReleaseCount(sysUsers, deptName, isDetail));
            case PROD_FAULT_COUNT:
                // 生产故障数
                return R.ok(handleProdFaultCount(sysUsers, deptName, isDetail));
            default:
                return R.ok();
        }
    }


    private KeyIndicatorVO handleTaskCount(List<SysUser> sysUsers, String deptName, boolean isDetail) {
        DateTime now = DateTime.now();
        int year = DateUtil.year(now);
        List<String> nickNames = sysUsers.stream().map(SysUser::getNickName).collect(Collectors.toList());
        List<WorkStat> taskCountWorkStats = workStatService.listByUserNameAndYearAndMonth(nickNames, year, null);
        KeyIndicatorVO vo = KeyIndicatorVO.builder().build();
        // 汇总
        Map<Integer, List<WorkStat>> monthGroup = taskCountWorkStats.stream().collect(Collectors.groupingBy(WorkStat::getWorkMonth));
        // 添加组指标集合
        List<KeyIndicatorVO.Indicator> group = new ArrayList<>();
        for (int month = 1; month <= CommConstants.CommonVal.TWELVE; month++) {
            List<WorkStat> workStats = monthGroup.get(month);
            // 遍历月份列表，设置组指标集合的组名、年、月、任务总数
            KeyIndicatorVO.Indicator groupIndicator = KeyIndicatorVO.Indicator.builder()
                .name(deptName)
                .year(year)
                .month(month)
                .value(BigDecimal.ZERO)
                .build();
            if (CollUtil.isNotEmpty(workStats)) {
                groupIndicator.setValue(workStats.stream()
                    .map(e -> BigDecimal.valueOf(e.getWorkDoneClosedTaskCount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            if (isDetail) {
                // 添加个人指标集合
                List<KeyIndicatorVO.Indicator> personals = new ArrayList<>();
                for (SysUser sysUser : sysUsers) {
                    // 遍历用户列表，设置个人指标集合的用户名、年、月、任务总数
                    KeyIndicatorVO.Indicator personal = KeyIndicatorVO.Indicator.builder()
                        .name(sysUser.getNickName())
                        .year(year)
                        .month(month)
                        .value(BigDecimal.ZERO)
                        .build();
                    if (CollUtil.isNotEmpty(workStats)) {
                        // 根据用户名称汇总任务总数
                        BigDecimal reduce = workStats.stream()
                            .filter(e -> StrUtil.equals(e.getWorkUsername(), sysUser.getNickName()))
                            .map(e -> BigDecimal.valueOf(e.getWorkDoneClosedTaskCount()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        personal.setValue(reduce);
                    }
                    personals.add(personal);
                }
                // 根据任务总数降序
                Collections.sort(personals, valueComparator.reversed());
                groupIndicator.setPersonal(personals);
            }
            group.add(groupIndicator);
        }
        vo.setGroup(group);
        return vo;
    }


    private KeyIndicatorVO handleWorkTime(List<SysUser> sysUsers, String deptName, boolean isDetail) {
        DateTime now = DateTime.now();
        int year = DateUtil.year(now);
        List<String> nickNames = sysUsers.stream().map(SysUser::getNickName).collect(Collectors.toList());
        List<UserKqStat> userKqStats = userKqStatService.listByUserNamesAndKqYearAndKqMonth(nickNames, String.valueOf(year), null);
        KeyIndicatorVO vo = KeyIndicatorVO.builder().build();
        // 汇总
        Map<String, List<UserKqStat>> monthGroup = userKqStats.stream().collect(Collectors.groupingBy(UserKqStat::getKqMonth));
        // 添加组指标集合
        List<KeyIndicatorVO.Indicator> group = new ArrayList<>();
        for (int month = 1; month <= CommConstants.CommonVal.TWELVE; month++) {
            List<UserKqStat> workStats = monthGroup.get(String.valueOf(month));
            // 遍历月份列表，设置组指标集合的组名、年、月、工作耗时
            KeyIndicatorVO.Indicator groupIndicator = KeyIndicatorVO.Indicator.builder()
                .name(deptName)
                .year(year)
                .month(month)
                .value(BigDecimal.ZERO)
                .build();
            if (CollUtil.isNotEmpty(workStats)) {
                groupIndicator.setValue(workStats.stream()
                    .map(e -> BigDecimal.valueOf(e.getKqAttendanceWorkTime()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP));
            }
            if (isDetail) {
                // 添加个人指标集合
                List<KeyIndicatorVO.Indicator> personals = new ArrayList<>();
                for (SysUser sysUser : sysUsers) {
                    // 遍历用户列表，设置个人指标集合的用户名、年、月、工作耗时
                    KeyIndicatorVO.Indicator personal = KeyIndicatorVO.Indicator.builder()
                        .name(sysUser.getNickName())
                        .year(year)
                        .month(month)
                        .value(BigDecimal.ZERO)
                        .build();
                    if (CollUtil.isNotEmpty(workStats)) {
                        // 根据用户名称汇总工作耗时
                        BigDecimal divide = workStats.stream().filter(e -> StrUtil.equals(e.getKqUserName(), sysUser.getNickName()))
                            .map(e -> BigDecimal.valueOf(e.getKqAttendanceWorkTime()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add)
                            .divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);
                        personal.setValue(divide);
                    }
                    personals.add(personal);
                }
                // 根据工作耗时降序
                Collections.sort(personals, valueComparator.reversed());
                groupIndicator.setPersonal(personals);
            }
            group.add(groupIndicator);
        }
        vo.setGroup(group);
        return vo;
    }

    private KeyIndicatorVO handleDocCount(List<SysUser> sysUsers, String deptName, boolean isDetail) {
        DateTime now = DateTime.now();
        int year = DateUtil.year(now);
        DateTime beginOfYear = DateUtil.beginOfYear(now);
        DateTime endOfYear = DateUtil.endOfYear(now);
        List<String> ztUserNames = sysUsers.stream().map(SysUser::getZtUserName).collect(Collectors.toList());
        List<ZtDoc> ztDocs = ztDocService.listByAddedByAndAddedDate(ztUserNames, beginOfYear, endOfYear);

        KeyIndicatorVO vo = KeyIndicatorVO.builder().build();
        // 汇总
        Map<Integer, List<ZtDoc>> monthGroup = ztDocs.stream().collect(Collectors.groupingBy(e -> DateUtil.month(e.getAddedDate()) + 1));
        // 添加组指标集合
        List<KeyIndicatorVO.Indicator> group = new ArrayList<>();
        for (int month = 1; month <= CommConstants.CommonVal.TWELVE; month++) {
            List<ZtDoc> workStats = monthGroup.get(month);
            // 遍历月份列表，设置组指标集合的组名、年、月、文档数
            KeyIndicatorVO.Indicator groupIndicator = KeyIndicatorVO.Indicator.builder()
                .name(deptName)
                .year(year)
                .month(month)
                .value(BigDecimal.ZERO)
                .build();
            if (CollUtil.isNotEmpty(workStats)) {
                groupIndicator.setValue(BigDecimal.valueOf(workStats.size()));
            }
            if (isDetail) {
                // 添加个人指标集合
                List<KeyIndicatorVO.Indicator> personals = new ArrayList<>();
                for (SysUser sysUser : sysUsers) {
                    // 遍历用户列表，设置个人指标集合的用户名、年、月、文档数
                    KeyIndicatorVO.Indicator personal = KeyIndicatorVO.Indicator.builder()
                        .name(sysUser.getNickName())
                        .year(year)
                        .month(month)
                        .value(BigDecimal.ZERO)
                        .build();
                    if (CollUtil.isNotEmpty(workStats)) {
                        // 根据禅道用户名称汇总文档数
                        long count = workStats.stream().filter(e -> StrUtil.equals(e.getAddedBy(), sysUser.getZtUserName())).count();
                        personal.setValue(BigDecimal.valueOf(count));
                    }
                    personals.add(personal);
                }
                // 根据文档数降序
                Collections.sort(personals, valueComparator.reversed());
                groupIndicator.setPersonal(personals);
            }
            group.add(groupIndicator);
        }
        vo.setGroup(group);
        return vo;
    }

    private KeyIndicatorVO handleCodeCount(List<SysUser> sysUsers, String deptName, boolean isDetail) {
        DateTime now = DateTime.now();
        int year = DateUtil.year(now);
        DateTime beginOfYear = DateUtil.beginOfYear(now);
        DateTime endOfYear = DateUtil.endOfYear(now);
        List<String> gitAuthorNames = sysUsers.stream().map(SysUser::getGitAuthorName).collect(Collectors.toList());
        List<String> gitCommitterNames = sysUsers.stream().map(SysUser::getGitCommitterName).collect(Collectors.toList());
        Set<String> committers = new HashSet<>();
        committers.addAll(gitAuthorNames);
        committers.addAll(gitCommitterNames);
        List<GitStatisticsInfo> gitStatisticsInfos = gitStatisticsInfoMapper.selectList(Wrappers.lambdaQuery(GitStatisticsInfo.class)
            .in(GitStatisticsInfo::getCommitter, committers)
            .le(GitStatisticsInfo::getAdditionsLine, 1000)
            .between(GitStatisticsInfo::getCommitDate, beginOfYear, endOfYear)
            .apply("message NOT LIKE '%Merge%'")
            .apply("message NOT LIKE '%merge%'")
            .apply("message NOT LIKE '%合并分支%'")
            .apply("message NOT LIKE '%Conflict%'"));

        KeyIndicatorVO vo = KeyIndicatorVO.builder().build();
        // 汇总
        Map<Integer, List<GitStatisticsInfo>> monthGroup = gitStatisticsInfos.stream().collect(Collectors.groupingBy(e -> DateUtil.month(e.getCommitDate()) + 1));
        // 添加组指标集合
        List<KeyIndicatorVO.Indicator> group = new ArrayList<>();
        for (int month = 1; month <= CommConstants.CommonVal.TWELVE; month++) {
            List<GitStatisticsInfo> workStats = monthGroup.get(month);
            // 遍历月份列表，设置组指标集合的组名、年、月、代码总数
            KeyIndicatorVO.Indicator groupIndicator = KeyIndicatorVO.Indicator.builder()
                .name(deptName)
                .year(year)
                .month(month)
                .value(BigDecimal.ZERO)
                .build();
            if (CollUtil.isNotEmpty(workStats)) {
                groupIndicator.setValue(workStats.stream().map(e -> BigDecimal.valueOf(e.getTotalLine())).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            if (isDetail) {
                // 添加个人指标集合
                List<KeyIndicatorVO.Indicator> personal = new ArrayList<>();
                for (SysUser sysUser : sysUsers) {
                    // 遍历用户列表，设置个人指标集合的用户名、年、月、代码总数
                    KeyIndicatorVO.Indicator personalIndicator = KeyIndicatorVO.Indicator.builder()
                        .name(sysUser.getNickName())
                        .year(year)
                        .month(month)
                        .value(BigDecimal.ZERO)
                        .build();
                    if (CollUtil.isNotEmpty(workStats)) {
                        // 根据用户Git名称汇总代码总数
                        BigDecimal reduce = workStats.stream()
                            .filter(o -> StringUtils.equals(sysUser.getGitAuthorName(), o.getCommitter())
                                || StringUtils.equals(sysUser.getGitCommitterName(), o.getCommitter()))
                            .map(e -> BigDecimal.valueOf(e.getTotalLine())).reduce(BigDecimal.ZERO, BigDecimal::add);
                        personalIndicator.setValue(reduce);
                    }
                    personal.add(personalIndicator);
                }
                // 根据代码总数降序
                Collections.sort(personal, valueComparator.reversed());
                groupIndicator.setPersonal(personal);
            }
            group.add(groupIndicator);
        }
        vo.setGroup(group);
        return vo;
    }


    private KeyIndicatorVO handleCaseCount(List<SysUser> sysUsers, String deptName, boolean isDetail) {
        DateTime now = DateTime.now();
        int year = DateUtil.year(now);
        List<String> nickNames = sysUsers.stream().map(SysUser::getNickName).collect(Collectors.toList());
        List<WorkStat> caseCountWorkStats = workStatService.listByUserNameAndYearAndMonth(nickNames, year, null);
        KeyIndicatorVO vo = KeyIndicatorVO.builder().build();
        // 汇总
        Map<Integer, List<WorkStat>> monthGroup = caseCountWorkStats.stream().collect(Collectors.groupingBy(WorkStat::getWorkMonth));
        // 添加组指标集合
        List<KeyIndicatorVO.Indicator> group = new ArrayList<>();
        for (int month = 1; month <= CommConstants.CommonVal.TWELVE; month++) {
            List<WorkStat> workStats = monthGroup.get(month);
            // 遍历月份列表，设置组指标集合的组名、年、月、用例总数
            KeyIndicatorVO.Indicator groupIndicator = KeyIndicatorVO.Indicator.builder()
                .name(deptName)
                .year(year)
                .month(month)
                .value(BigDecimal.ZERO)
                .build();
            if (CollUtil.isNotEmpty(workStats)) {
                groupIndicator.setValue(workStats.stream()
                    .map(e -> BigDecimal.valueOf(e.getWorkCaseCount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            if (isDetail) {
                // 添加个人指标集合
                List<KeyIndicatorVO.Indicator> personals = new ArrayList<>();
                for (SysUser sysUser : sysUsers) {
                    // 遍历用户列表，设置个人指标集合的用户名、年、月、用例总数
                    KeyIndicatorVO.Indicator personal = KeyIndicatorVO.Indicator.builder()
                        .name(sysUser.getNickName())
                        .year(year)
                        .month(month)
                        .value(BigDecimal.ZERO)
                        .build();
                    if (CollUtil.isNotEmpty(workStats)) {
                        // 根据用户名称汇总用例总数
                        BigDecimal reduce = workStats.stream()
                            .filter(e -> StrUtil.equals(e.getWorkUsername(), sysUser.getNickName()))
                            .map(e -> BigDecimal.valueOf(e.getWorkCaseCount()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        personal.setValue(reduce);
                    }
                    personals.add(personal);
                }
                // 根据用例总数降序
                Collections.sort(personals, valueComparator.reversed());
                groupIndicator.setPersonal(personals);
            }
            group.add(groupIndicator);
        }
        vo.setGroup(group);
        return vo;
    }


    private KeyIndicatorVO handleProjectCount(List<SysUser> sysUsers, String deptName, boolean isDetail) {
        DateTime now = DateTime.now();
        int year = DateUtil.year(now);
        DateTime beginOfYear = DateUtil.beginOfYear(now);
        DateTime endOfYear = DateUtil.endOfYear(now);
        Set<String> committerNames = getCommitterNameBySysUsers(sysUsers);
        List<GitStatisticsInfoVO> gitStatisticsInfos = gitStatisticsInfoMapper.queryByCommitterAndCommitDate(committerNames, beginOfYear.toString(), endOfYear.toString());
        KeyIndicatorVO vo = KeyIndicatorVO.builder().build();
        // 汇总
        Map<Integer, List<GitStatisticsInfoVO>> monthGroup = gitStatisticsInfos.stream().collect(Collectors.groupingBy(e -> DateUtil.month(e.getCommitDate()) + 1));
        // 添加组指标集合
        List<KeyIndicatorVO.Indicator> group = new ArrayList<>();
        for (int month = 1; month <= CommConstants.CommonVal.TWELVE; month++) {
            List<GitStatisticsInfoVO> workStats = monthGroup.get(month);
            // 遍历月份列表，设置组指标集合的组名、年、月、项目总数
            KeyIndicatorVO.Indicator groupIndicator = KeyIndicatorVO.Indicator.builder()
                .name(deptName)
                .year(year)
                .month(month)
                .value(BigDecimal.ZERO)
                .build();
            if (CollUtil.isNotEmpty(workStats)) {
                groupIndicator.setValue(BigDecimal.valueOf(workStats.size()));
            }
            if (isDetail) {
                // 添加个人指标集合
                List<KeyIndicatorVO.Indicator> personals = new ArrayList<>();
                for (SysUser sysUser : sysUsers) {
                    // 遍历用户列表，设置个人指标集合的用户名、年、月、项目总数
                    KeyIndicatorVO.Indicator personal = KeyIndicatorVO.Indicator.builder()
                        .name(sysUser.getNickName())
                        .year(year)
                        .month(month)
                        .value(BigDecimal.ZERO)
                        .build();
                    if (CollUtil.isNotEmpty(workStats)) {
                        // 根据用户Git名称汇总项目总数
                        long count = workStats.stream().filter(e -> StrUtil.equals(e.getCommitter(), sysUser.getGitAuthorName())
                            || StrUtil.equals(e.getCommitter(), sysUser.getGitCommitterName())).count();
                        personal.setValue(BigDecimal.valueOf(count));
                    }
                    personals.add(personal);
                }
                // 根据项目总数降序
                Collections.sort(personals, valueComparator.reversed());
                groupIndicator.setPersonal(personals);
            }
            group.add(groupIndicator);
        }
        vo.setGroup(group);
        return vo;
    }

    private KeyIndicatorVO handleReleaseCount(List<SysUser> sysUsers, String deptName, boolean isDetail) {
        DateTime now = DateTime.now();
        int year = DateUtil.year(now);
        DateTime beginOfYear = DateUtil.beginOfYear(now);
        DateTime endOfYear = DateUtil.endOfYear(now);
        List<Long> userIds = sysUsers.stream().map(SysUser::getUserId).collect(Collectors.toList());
        // 按"内部管理系统发布"类型的审批流程的创建人、创建时间进行统计
        Integer instanceType = 0;
        List<DingTalkProcessInstanceVO> instances = dingTalkProcessInstanceMapper
            .queryByInstanceTypeAndUserIdAndCreateTime(instanceType, userIds, beginOfYear.toString(), endOfYear.toString());
        KeyIndicatorVO vo = KeyIndicatorVO.builder().build();
        // 汇总
        Map<Integer, List<DingTalkProcessInstanceVO>> monthGroup = instances.stream().collect(Collectors.groupingBy(e -> DateUtil.month(e.getInstanceCreateTime()) + 1));
        // 添加组指标集合
        List<KeyIndicatorVO.Indicator> group = new ArrayList<>();
        for (int month = 1; month <= CommConstants.CommonVal.TWELVE; month++) {
            List<DingTalkProcessInstanceVO> workStats = monthGroup.get(month);
            // 遍历月份列表，设置组指标集合的组名、年、月、上线数
            KeyIndicatorVO.Indicator groupIndicator = KeyIndicatorVO.Indicator.builder()
                .name(deptName)
                .year(year)
                .month(month)
                .value(BigDecimal.ZERO)
                .build();
            if (CollUtil.isNotEmpty(workStats)) {
                groupIndicator.setValue(BigDecimal.valueOf(workStats.size()));
            }
            if (isDetail) {
                // 添加个人指标集合
                List<KeyIndicatorVO.Indicator> personals = new ArrayList<>();
                for (SysUser sysUser : sysUsers) {
                    // 遍历用户列表，设置个人指标集合的用户名、年、月、上线数
                    KeyIndicatorVO.Indicator personal = KeyIndicatorVO.Indicator.builder()
                        .name(sysUser.getNickName())
                        .year(year)
                        .month(month)
                        .value(BigDecimal.ZERO)
                        .build();
                    if (CollUtil.isNotEmpty(workStats)) {
                        // 根据用户ID汇总上线数
                        long count = workStats.stream().filter(e -> e.getUserId().equals(sysUser.getUserId())).count();
                        personal.setValue(BigDecimal.valueOf(count));
                    }
                    personals.add(personal);
                }
                // 根据上线数降序
                Collections.sort(personals, valueComparator.reversed());
                groupIndicator.setPersonal(personals);
            }
            group.add(groupIndicator);
        }
        vo.setGroup(group);
        return vo;
    }

    private KeyIndicatorVO handleProdFaultCount(List<SysUser> sysUsers, String deptName, boolean isDetail) {
        DateTime now = DateTime.now();
        int year = DateUtil.year(now);
        DateTime beginOfYear = DateUtil.beginOfYear(now);
        DateTime endOfYear = DateUtil.endOfYear(now);
        List<Long> userIds = sysUsers.stream().map(SysUser::getUserId).collect(Collectors.toList());
        // 按"开发修复"节点的完成人员、节点审批完成时间进行统计
        String nodeName = "开发修复";
        List<DingTalkProcessInstanceTaskVO> nodes = dingTalkProcessInstanceTaskMapper
            .queryByNodeNameAndUserIdAndFinishTime(nodeName, userIds, beginOfYear.toString(), endOfYear.toString());
        KeyIndicatorVO vo = KeyIndicatorVO.builder().build();
        // 汇总
        Map<Integer, List<DingTalkProcessInstanceTaskVO>> monthGroup = nodes.stream().collect(Collectors.groupingBy(e -> DateUtil.month(e.getInstanceFinishTime()) + 1));
        // 添加组指标集合
        List<KeyIndicatorVO.Indicator> group = new ArrayList<>();
        for (int month = 1; month <= CommConstants.CommonVal.TWELVE; month++) {
            List<DingTalkProcessInstanceTaskVO> workStats = monthGroup.get(month);
            // 遍历月份列表，设置组指标集合的组名、年、月、生产故障数
            KeyIndicatorVO.Indicator groupIndicator = KeyIndicatorVO.Indicator.builder()
                .name(deptName)
                .year(year)
                .month(month)
                .value(BigDecimal.ZERO)
                .build();
            if (CollUtil.isNotEmpty(workStats)) {
                groupIndicator.setValue(BigDecimal.valueOf(workStats.size()));
            }
            if (isDetail) {
                // 添加个人指标集合
                List<KeyIndicatorVO.Indicator> personals = new ArrayList<>();
                for (SysUser sysUser : sysUsers) {
                    // 遍历用户列表，设置个人指标集合的用户名、年、月、生产故障数
                    KeyIndicatorVO.Indicator personal = KeyIndicatorVO.Indicator.builder()
                        .name(sysUser.getNickName())
                        .year(year)
                        .month(month)
                        .value(BigDecimal.ZERO)
                        .build();
                    if (CollUtil.isNotEmpty(workStats)) {
                        // 根据用户ID汇总生产故障数
                        long count = workStats.stream().filter(e -> e.getUserId().equals(sysUser.getUserId())).count();
                        personal.setValue(BigDecimal.valueOf(count));
                    }
                    personals.add(personal);
                }
                // 根据生产故障数降序
                Collections.sort(personals, valueComparator.reversed());
                groupIndicator.setPersonal(personals);
            }
            group.add(groupIndicator);
        }
        vo.setGroup(group);
        return vo;
    }

    /**
     * 获取git提交名称
     * @param sysUsers
     * @return
     */
    private Set<String> getCommitterNameBySysUsers(List<SysUser> sysUsers) {
        Set<String> names = new HashSet<>();
        sysUsers.stream().forEach(
            u ->{
                names.add(u.getGitAuthorName());
                names.add(u.getGitCommitterName());
            }
        );
        return names;
    }

}
