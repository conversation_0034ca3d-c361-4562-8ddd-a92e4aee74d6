package com.qmqb.imp.system.domain.vo;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 【请填写功能名称】视图对象 zt_build
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@ExcelIgnoreUnannotated
public class ZtBuildVo {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Integer id;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Integer project;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Integer product;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String branch;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Integer execution;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String builds;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String name;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Integer system;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String scmPath;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String filePath;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Date date;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String stories;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String bugs;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Integer artifactRepoId;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String builder;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String desc;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String createdBy;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Date createdDate;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String deleted;


}
