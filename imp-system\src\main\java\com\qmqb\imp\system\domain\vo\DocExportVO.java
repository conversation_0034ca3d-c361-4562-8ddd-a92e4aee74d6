package com.qmqb.imp.system.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 18:00 2025/5/13
 * @Description TODO
 * @MethodName
 * @return null
 */
@Data
public class DocExportVO {

    @ExcelProperty(value = "id")
    private Integer id;

    @ExcelProperty(value = "文档标题")
    private String title;

    @ExcelProperty(value = "所属组")
    private String groupName;

    @ExcelProperty(value = "作者")
    private String addedBy;

    @ExcelProperty(value = "添加日期")
    private Date addedDate;
}
