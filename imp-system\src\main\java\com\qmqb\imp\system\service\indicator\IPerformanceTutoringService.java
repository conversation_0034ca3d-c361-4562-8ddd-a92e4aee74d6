package com.qmqb.imp.system.service.indicator;


import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.system.domain.bo.performance.PerformanceTutoringBo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceTutoringQueryBo;
import com.qmqb.imp.system.domain.performance.PerformanceTutoring;
import com.qmqb.imp.system.domain.vo.performance.PerformanceTutoringVo;

import java.util.Collection;
import java.util.List;

/**
 * 绩效辅导Service接口
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IPerformanceTutoringService {

    /**
     * 查询绩效辅导
     * @param id
     * @return
     */
    PerformanceTutoringVo queryById(Long id);

    /**
     * 查询绩效辅导列表
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<PerformanceTutoringVo> queryPageList(PerformanceTutoringQueryBo bo, PageQuery pageQuery);

    /**
     * 查询绩效辅导列表
     * @param bo
     * @return
     */
    List<PerformanceTutoringVo> queryList(PerformanceTutoringBo bo);

    /**
     * 新增绩效辅导
     * @param bo
     * @return
     */
    Boolean insertByBo(PerformanceTutoringBo bo);

    /**
     * 绩效辅导登记
     * @param bo
     * @return
     */
    Boolean tutoring(PerformanceTutoringBo bo);


    /**
     * 绩效辅导建议
     * @param bo
     * @return
     */
    Boolean suggest(PerformanceTutoringBo bo);

    /**
     * 批量添加绩效辅导数据
     * @param tutoringList
     * @return
     */
    Boolean saveBatch(List<PerformanceTutoring> tutoringList);

    /**
     * 根据主键批量删除绩效辅导
     * @param mainIds
     * @return
     */
    void removeByMainIds(List<String> mainIds);

    /**
     * 获取当前登录人的最后一条编辑记录
     * @param username
     * @return
     */
    PerformanceTutoringVo getLastEditTotur(String username);
}
