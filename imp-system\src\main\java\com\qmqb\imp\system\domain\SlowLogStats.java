package com.qmqb.imp.system.domain;

import java.io.Serializable;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06 10:15:00
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_slow_log_stats")
@Schema(name = "SlowLogStatsDO对象", description = "")
public class SlowLogStats implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "`id`")
    private Long id;

    @TableField("`DBName`")
    private String dbName;

    @TableField("`DBNodeId`")
    private String dbNodeId;

    @Schema(description = "生成时间")
    @TableField("`CreateTime`")
    private LocalDate createTime;

    @Schema(description = "执行时长（最大值），单位：秒")
    @TableField("`MaxExecutionTime`")
    private Long maxExecutionTime;

    @Schema(description = "锁定时长（最大值），单位：秒")
    @TableField("`MaxLockTime`")
    private Long maxLockTime;

    @Schema(description = "解析的SQL行数（最大值）")
    @TableField("`ParseMaxRowCount`")
    private Long parseMaxRowCount;

    @Schema(description = "解析的SQL行数（总值）")
    @TableField("`ParseTotalRowCounts`")
    private Long parseTotalRowCounts;

    @Schema(description = "返回的SQL行数（最大值）")
    @TableField("`ReturnMaxRowCount`")
    private Long returnMaxRowCount;

    @Schema(description = "返回的SQL行数（总值）")
    @TableField("`ReturnTotalRowCounts`")
    private Long returnTotalRowCounts;

    @TableField("`SQLHash`")
    private String sqlHash;

    @TableField("`SQLText`")
    private String sqlText;

    @Schema(description = "执行次数（总值）")
    @TableField("`TotalExecutionCounts`")
    private Long totalExecutionCounts;

    @Schema(description = "执行时长（总值），单位：秒")
    @TableField("`TotalExecutionTimes`")
    private Long totalExecutionTimes;

    @Schema(description = "锁定时长（总值），单位：秒")
    @TableField("`TotalLockTimes`")
    private Long totalLockTimes;


}
