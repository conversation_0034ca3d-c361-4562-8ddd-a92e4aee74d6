package com.qmqb.imp.system.service.impl.performance;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.entity.SysRole;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.*;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackBo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackMainBo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackMainQueryBo;
import com.qmqb.imp.system.domain.performance.PerformanceFeedback;
import com.qmqb.imp.system.domain.performance.PerformanceFeedbackMain;
import com.qmqb.imp.system.domain.performance.PerformanceFeedbackMainIndicatorResult;
import com.qmqb.imp.system.domain.performance.PerformanceTutoring;
import com.qmqb.imp.system.domain.vo.performance.PerformanceFeedbackMainVo;
import com.qmqb.imp.system.domain.vo.performance.PerformanceFeedbackVo;
import com.qmqb.imp.system.mapper.performance.PerformanceFeedbackMainMapper;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.indicator.IPerformanceFeedbackMainIndicatorResultService;
import com.qmqb.imp.system.service.indicator.IPerformanceFeedbackMainService;
import com.qmqb.imp.system.service.indicator.IPerformanceFeedbackService;
import com.qmqb.imp.system.service.indicator.IPerformanceTutoringService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 绩效反馈主表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@RequiredArgsConstructor
@Service
public class PerformanceFeedbackMainServiceImpl extends ServiceImpl<PerformanceFeedbackMainMapper, PerformanceFeedbackMain> implements IPerformanceFeedbackMainService {

    private final PerformanceFeedbackMainMapper baseMapper;
    private final IPerformanceFeedbackService performanceFeedbackService;
    private final PerformanceFeedbackCodeGenerator codeGenerator;
    private final ISysUserService sysUserService;
    private final IPerformanceFeedbackMainIndicatorResultService performanceFeedbackMainIndicatorResultService;
    private final PerformanceFeedbackValidationService validationService;
    private final IPerformanceTutoringService performanceTutoringService;

    /**
     * 查询绩效反馈主表
     */
    @Override
    public PerformanceFeedbackMainVo queryById(Long id) {
        PerformanceFeedbackMainVo performanceFeedbackMainVo = baseMapper.selectVoById(id);
        if (performanceFeedbackMainVo == null) {
            return null;
        }
        // 查询任务反馈详情
        PerformanceFeedbackBo bo = new PerformanceFeedbackBo();
        bo.setMainFeedbackId(id);
        List<PerformanceFeedbackVo> feedbackList = performanceFeedbackService.queryList(bo);
        performanceFeedbackMainVo.setPerformanceFeedbackList(feedbackList);
        return performanceFeedbackMainVo;
    }

    /**
     * 查询绩效反馈主表列表（分页）
     */
    @Override
    public TableDataInfo<PerformanceFeedbackMainVo> queryPageList(PerformanceFeedbackMainQueryBo bo, PageQuery pageQuery) {
        // 获取当前登录用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("当前用户未登录");
        }
        SysUser currentUser = sysUserService.selectUserById(loginUser.getUserId());
        if (currentUser == null) {
            throw new ServiceException("当前用户信息不存在");
        }
        if (ObjUtil.isEmpty(pageQuery.getOrderByColumn())){
            // 默认按反馈时间降序排序
            pageQuery.setOrderByColumn("feedbackTime");
            pageQuery.setIsAsc("desc");
        }

        // 设置权限相关字段
        bo.setIsAdmin(currentUser.isAdmin());
        bo.setIsJszxAdmin(currentUser.isJszxAdmin());
        bo.setIsProjectManager(currentUser.isProjectManager());
        bo.setUserName(currentUser.getUserName());

        // 分页查询（含权限过滤）
        IPage<PerformanceFeedbackMainVo> result = baseMapper.selectMainPageByFeedbackCondition(processFinalAuditOrder(pageQuery.build()), bo);
        List<PerformanceFeedbackMainVo> records = result.getRecords();
        // 查询任务反馈详情
        records.forEach(vo -> {
            PerformanceFeedbackBo feedbackBo = new PerformanceFeedbackBo();
            feedbackBo.setMainFeedbackId(vo.getId());
            List<PerformanceFeedbackVo> feedbackList = performanceFeedbackService.queryList(feedbackBo);
            // 处理feedbackList， 拼接推荐绩效级别，小王：S，小李：A，小青：C
            String feedbackListStr = feedbackList.stream()
                .map(feedback -> feedback.getNickName() + "：" + feedback.getRecommendedLevel())
                .collect(Collectors.joining(", "));
            // 处理feedbackList，拼接推荐原因
            String feedbackReasons = feedbackList.stream()
                .map(PerformanceFeedbackVo::getRecommendedReason)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(", "));
            vo.setRecommendedLevel(feedbackListStr);
            vo.setRecommendedReason(feedbackReasons);
            vo.setPerformanceFeedbackList(feedbackList);
        });
        return TableDataInfo.build(result);
    }

    private IPage<?> processFinalAuditOrder(IPage<?> page) {
        if (page.orders() == null || page.orders().isEmpty()) {
            return page;
        }

        List<OrderItem> newOrders = new ArrayList<>();

        for (OrderItem order : page.orders()) {
            // 处理 final_audit 和 project_manager_audit_status 的自定义排序
            if ("final_audit".equals(order.getColumn()) || "finalAudit".equals(order.getColumn())) {
                String customOrder = "CASE final_audit WHEN 'NOT_AUDITED' THEN 1 WHEN 'APPROVED' THEN 2 WHEN 'REJECTED' THEN 3 ELSE 4 END";
                newOrders.add(order.isAsc() ? OrderItem.asc(customOrder) : OrderItem.desc(customOrder));
            }else if ("project_manager_audit_status".equals(order.getColumn()) || "projectManagerAuditStatus".equals(order.getColumn())) {
                String customOrder = "CASE project_manager_audit_status WHEN 'NOT_AUDITED' THEN 1 WHEN 'APPROVED' THEN 2 WHEN 'REJECTED' THEN 3 ELSE 4 END";
                newOrders.add(order.isAsc() ? OrderItem.asc(customOrder) : OrderItem.desc(customOrder));
            }else if ("submit_status".equals(order.getColumn()) || "submitStatus".equals(order.getColumn())) {
                String customOrder = "CASE submit_status WHEN 'NOT_SUBMITTED' THEN 1 WHEN 'PENDING_RESUBMIT' THEN 2 WHEN 'SUBMITTED' THEN 3 ELSE 4 END";
                newOrders.add(order.isAsc() ? OrderItem.asc(customOrder) : OrderItem.desc(customOrder));
            } else {
                newOrders.add(order);
            }
        }

        page.orders().clear();
        page.orders().addAll(newOrders);

        return page;
    }

    /**
     * 查询绩效反馈主表列表
     */
    @Override
    public List<PerformanceFeedbackMainVo> queryList(PerformanceFeedbackMainQueryBo bo) {
        LambdaQueryWrapper<PerformanceFeedbackMain> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 新增绩效反馈主表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(PerformanceFeedbackMainBo bo) {
        LocalDate now = LocalDate.now();
        int evalYear = now.getYear();
        int evalMonth = now.getMonthValue();
        Date date = new Date();
        bo.setFeedbackCode(bo.getFeedbackCode());
        bo.setYear(evalYear);
        bo.setMonth(evalMonth);
        bo.setFeedbackTime(date);
        bo.setDataSource(PerformanceFeedbackDataSourceEnum.MANUAL_ADD.getCode());
        bo.setSubmitStatus(PerformanceFeedbackSubmitStatusEnum.NOT_SUBMITTED.getCode());
        bo.setProjectManagerAuditStatus(PerformanceFeedbackAuditStatusEnum.NOT_AUDITED.getCode());
        bo.setFinalAudit(PerformanceFeedbackAuditStatusEnum.NOT_AUDITED.getCode());

        PerformanceFeedbackMain add = BeanUtil.toBean(bo, PerformanceFeedbackMain.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            List<PerformanceFeedbackBo> boFeedbackList = bo.getFeedbackList();
            if (boFeedbackList != null && !boFeedbackList.isEmpty()) {
                List<PerformanceFeedback> feedbackList = boFeedbackList.stream().map(feedbackBo -> {
                    PerformanceFeedback feedback = new PerformanceFeedback();
                    BeanUtil.copyProperties(feedbackBo, feedback, true);
                    feedback.setMainFeedbackId(add.getId());
                    SysUser sysUser = sysUserService.selectUserByNickName(feedbackBo.getNickName());
                    Long groupId = null;
                    String groupName = null;
                    if (sysUser != null && sysUser.getDept() != null) {
                        groupId = sysUser.getDept().getDeptId();
                        groupName = sysUser.getDept().getDeptName();
                    }
                    feedback.setGroupId(groupId);
                    feedback.setGroupName(groupName);
                    feedback.setPersonType(sysUser.getRoles().stream().map(SysRole::getRoleId).map(String::valueOf).collect(Collectors.joining(",")));

                    BeanUtil.copyProperties(bo, feedback, true);
                    feedback.setId(null);
                    feedback.setPrimaryIndicator(feedbackBo.getPrimaryIndicator());
                    feedback.setSecondaryIndicator(feedbackBo.getSecondaryIndicator());
                    return feedback;
                }).collect(Collectors.toList());
                performanceFeedbackService.saveBatch(feedbackList);
            }
        }
        return flag;
    }

    /**
     * 修改绩效反馈主表（增量同步子表）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(PerformanceFeedbackMainBo bo) {
        // 校验：仅限于未提交或待重提状态有效
        PerformanceFeedbackMain dbMain = baseMapper.selectById(bo.getId());
        if (dbMain != null) {
            String submitStatus = dbMain.getSubmitStatus();
            if (!PerformanceFeedbackSubmitStatusEnum.NOT_SUBMITTED.getCode().equals(submitStatus)
                    && !PerformanceFeedbackSubmitStatusEnum.PENDING_RESUBMIT.getCode().equals(submitStatus)) {
                throw new ServiceException("仅限于未提交或待重提状态的数据可编辑");
            }
        }

        // 1. 更新主表
        PerformanceFeedbackMain update = BeanUtil.toBean(bo, PerformanceFeedbackMain.class);
        List<PerformanceFeedbackBo> boFeedbackList = bo.getFeedbackList();
        if (boFeedbackList != null && !boFeedbackList.isEmpty()) {
            // 1.1 更新主表的一类指标
            update.setPrimaryIndicator(boFeedbackList.get(0).getPrimaryIndicator());
            // 1.2 更新主表的二类指标
            update.setSecondaryIndicator(boFeedbackList.get(0).getSecondaryIndicator());
        } else {
            // 如果没有子表数据，直接返回false
            return false;
        }
        boolean mainFlag = baseMapper.updateById(update) > 0;
        if (!mainFlag) {
            return false;
        }
        // 2. 查询数据库中该主表下所有子表
        List<PerformanceFeedback> dbList = performanceFeedbackService.list(Wrappers.lambdaQuery(PerformanceFeedback.class)
            .eq(PerformanceFeedback::getMainFeedbackId, bo.getId()));
        List<Long> dbIds = dbList.stream().map(PerformanceFeedback::getId).collect(Collectors.toList());
        // 3. 前端传来的feedbackList分类
        List<PerformanceFeedbackBo> inputList = boFeedbackList;
        if (inputList == null) {
            inputList = Collections.emptyList();
        }
        // 3.1 需要insert的
        List<PerformanceFeedback> toInsert = inputList.stream()
            .filter(fb -> fb.getId() == null)
            .map(fb -> {
                PerformanceFeedback feedback = new PerformanceFeedback();
                BeanUtil.copyProperties(fb, feedback, true);
                feedback.setMainFeedbackId(bo.getId());
                SysUser sysUser = sysUserService.selectUserByNickName(fb.getNickName());
                Long groupId = null;
                String groupName = null;
                if (sysUser != null && sysUser.getDept() != null) {
                    groupId = sysUser.getDept().getDeptId();
                    groupName = sysUser.getDept().getDeptName();
                }
                feedback.setGroupId(groupId);
                feedback.setGroupName(groupName);
                feedback.setPersonType(sysUser != null ? sysUser.getRoles().stream().map(SysRole::getRoleId).map(String::valueOf).collect(Collectors.joining(",")) : null);
                BeanUtil.copyProperties(bo, fb, true);
                feedback.setId(null);
                feedback.setPrimaryIndicator(fb.getPrimaryIndicator());
                feedback.setSecondaryIndicator(fb.getSecondaryIndicator());
                return feedback;
            })
            .collect(Collectors.toList());
        // 3.2 需要update的
        List<PerformanceFeedback> toUpdate = inputList.stream()
            .filter(fb -> fb.getId() != null && dbIds.contains(fb.getId()))
            .map(fb -> BeanUtil.toBean(fb, PerformanceFeedback.class))
            .collect(Collectors.toList());
        // 3.3 需要delete的
        List<Long> inputIds = inputList.stream().map(PerformanceFeedbackBo::getId).filter(id -> id != null).collect(Collectors.toList());
        List<Long> toDelete = dbIds.stream().filter(id -> !inputIds.contains(id)).collect(Collectors.toList());
        savePerformance(bo, toInsert, inputList, toUpdate, toDelete);
        return true;
    }

    private void savePerformance(PerformanceFeedbackMainBo bo, List<PerformanceFeedback> toInsert, List<PerformanceFeedbackBo> inputList, List<PerformanceFeedback> toUpdate, List<Long> toDelete) {
        // 4. 执行子表操作
        if (!toInsert.isEmpty()) {
            performanceFeedbackService.saveBatch(toInsert);
        }
        if (!toUpdate.isEmpty()) {
            for (PerformanceFeedback feedback : toUpdate) {
                feedback.setEventTitle(bo.getEventTitle());
                feedback.setEventDetail(bo.getEventDetail());
                performanceFeedbackService.updateById(feedback);
            }
        }
        if (!toDelete.isEmpty()) {
            performanceFeedbackService.removeByIds(toDelete);
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PerformanceFeedbackMain entity) {
        if (Objects.requireNonNull(PerformanceIndicatorEnum.fromCode(entity.getSecondaryIndicator())).isSystemGenerated()) {
            throw new ServiceException("二级指标不能为系统生成的指标");
        }
    }

    /**
     * 批量删除绩效反馈主表信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        // 获取当前登录用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("当前用户未登录");
        }
        SysUser currentUser = sysUserService.selectUserById(loginUser.getUserId());
        if (currentUser == null) {
            throw new ServiceException("当前用户信息不存在");
        }
        // 不是管理员或技术总监时，校验权限
        if (!currentUser.isJszxAdmin() && !currentUser.isAdmin()) {
            // 组长、项管只能删除自己填写或提交的（createBy 或 submitter 字段为当前用户）或自己为 projectManagerAuditor 的
            boolean hasPermission = !(currentUser.isProjectManager() || (!currentUser.isAdmin() && !currentUser.isJszxAdmin()));
            if (hasPermission) {
                throw new ServiceException("无权删除他人数据");
            }
            String userName = currentUser.getUserName();
            List<PerformanceFeedbackMain> mainList = baseMapper.selectBatchIds(ids);
            for (PerformanceFeedbackMain main : mainList) {
                boolean isSelf = userName.equals(main.getCreateBy()) || userName.equals(main.getSubmitter()) || userName.equals(main.getProjectManagerAuditor());
                if (!isSelf) {
                    throw new ServiceException("仅能删除自己填写、提交或审核的数据");
                }
            }
        }
        if (isValid) {
            // 校验：仅能删除未提交或待重提的数据
            List<PerformanceFeedbackMain> mainList = baseMapper.selectBatchIds(ids);
            for (PerformanceFeedbackMain main : mainList) {
                String submitStatus = main.getSubmitStatus();
                if (PerformanceFeedbackSubmitStatusEnum.SUBMITTED.getCode().equals(submitStatus)) {
                    throw new ServiceException("仅能删除未提交或待重提的数据");
                }
            }
        }
        // 先删除子表
        performanceFeedbackService.deleteByMainFeedbackIds(new ArrayList<>(ids));
        // 再删除主表
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<String> getIdByNickNameAndYearMonth(String nickName, Integer year, Integer month) {
        return baseMapper.getIdByNickNameAndYearMonth(nickName, year, month);
    }

    /**
     * 批量项目经理审核
     *
     * @param ids     绩效反馈主表ID集合
     * @param status  审核状态
     * @param auditor 审核人
     * @param remark  审核备注
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchProjectManagerAudit(Collection<Long> ids, String status, String auditor, String remark) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        List<PerformanceFeedbackMain> mainList = baseMapper.selectBatchIds(ids);
        if (mainList.isEmpty()) {
            return false;
        }
        // 校验：仅限于提交状态为已提交，项管审核状态为未审核时有效
        for (PerformanceFeedbackMain main : mainList) {
            if (!PerformanceFeedbackSubmitStatusEnum.SUBMITTED.getCode().equals(main.getSubmitStatus())
                    || !PerformanceFeedbackAuditStatusEnum.NOT_AUDITED.getCode().equals(main.getProjectManagerAuditStatus())) {
                throw new ServiceException("仅限于提交状态为已提交，项管审核状态为未审核时可审核");
            }
        }

        // 校验绩效数据登记规则
        validationService.validateProjectManagerAudit(mainList,status,false);

        Date auditTime = new Date();
        for (PerformanceFeedbackMain main : mainList) {
            main.setProjectManagerAuditStatus(status);
            main.setProjectManagerAuditor(auditor);
            main.setProjectManagerAuditTime(auditTime);
            main.setProjectManagerRemark(remark);
            // 项管审核拒绝时，提交状态变为待重提
            if (PerformanceFeedbackAuditStatusEnum.REJECTED.getCode().equals(status)) {
                main.setSubmitStatus(PerformanceFeedbackSubmitStatusEnum.PENDING_RESUBMIT.getCode());
            }
        }

        // 当绩效反馈等级为C或D时，项管审核通过时，生成辅导记录
        List<PerformanceTutoring> tutoringList = new ArrayList<>();
        List<Long> mainIdList =  mainList.stream().map(PerformanceFeedbackMain::getId).collect(Collectors.toList());
        List<PerformanceFeedback> list = performanceFeedbackService.lambdaQuery()
            .in(PerformanceFeedback::getMainFeedbackId, mainIdList).list();
        if (PerformanceFeedbackAuditStatusEnum.APPROVED.getCode().equals(status)) {
            for (PerformanceFeedback feedback : list) {
                if (feedback.getRecommendedLevel().equals(ScoreLevelEnum.SCORE_C.getCode()) ||
                    feedback.getRecommendedLevel().equals(ScoreLevelEnum.SCORE_D.getCode())) {
                    PerformanceTutoring performanceTutoring =  PerformanceTutoring.builder().feedbackId(feedback.getId()).build();
                    tutoringList.add(performanceTutoring);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(tutoringList)){
            performanceTutoringService.saveBatch(tutoringList);
        }
        return this.updateBatchById(mainList);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<PerformanceFeedbackMain> buildQueryWrapper(PerformanceFeedbackMainQueryBo bo) {
        LambdaQueryWrapper<PerformanceFeedbackMain> lqw = Wrappers.lambdaQuery();
        // 事件id
        lqw.eq(bo.getEventId() != null, PerformanceFeedbackMain::getEventId, bo.getEventId());
        // 反馈编码
        lqw.eq(StringUtils.isNotBlank(bo.getFeedbackCode()), PerformanceFeedbackMain::getFeedbackCode, bo.getFeedbackCode());
        // 反馈时间范围
        lqw.ge(bo.getFeedbackTimeBegin() != null, PerformanceFeedbackMain::getFeedbackTime, bo.getFeedbackTimeBegin());
        lqw.le(bo.getFeedbackTimeEnd() != null, PerformanceFeedbackMain::getFeedbackTime, bo.getFeedbackTimeEnd());
        // 提交时间范围
        lqw.ge(bo.getSubmitTimeBegin() != null, PerformanceFeedbackMain::getSubmitTime, bo.getSubmitTimeBegin());
        lqw.le(bo.getSubmitTimeEnd() != null, PerformanceFeedbackMain::getSubmitTime, bo.getSubmitTimeEnd());
        // 项管审核时间范围
        lqw.ge(bo.getProjectManagerAuditTimeBegin() != null, PerformanceFeedbackMain::getProjectManagerAuditTime, bo.getProjectManagerAuditTimeBegin());
        lqw.le(bo.getProjectManagerAuditTimeEnd() != null, PerformanceFeedbackMain::getProjectManagerAuditTime, bo.getProjectManagerAuditTimeEnd());
        // 最终审核时间范围
        lqw.ge(bo.getFinalAuditTimeBegin() != null, PerformanceFeedbackMain::getFinalAuditTime, bo.getFinalAuditTimeBegin());
        lqw.le(bo.getFinalAuditTimeEnd() != null, PerformanceFeedbackMain::getFinalAuditTime, bo.getFinalAuditTimeEnd());
        // 其余精确/模糊条件
        lqw.eq(StringUtils.isNotBlank(bo.getDataSource()), PerformanceFeedbackMain::getDataSource, bo.getDataSource());
        lqw.eq(StringUtils.isNotBlank(bo.getSubmitStatus()), PerformanceFeedbackMain::getSubmitStatus, bo.getSubmitStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getProjectManagerAuditStatus()), PerformanceFeedbackMain::getProjectManagerAuditStatus, bo.getProjectManagerAuditStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getFinalAudit()), PerformanceFeedbackMain::getFinalAudit, bo.getFinalAudit());
        lqw.eq(StringUtils.isNotBlank(bo.getPrimaryIndicator()), PerformanceFeedbackMain::getPrimaryIndicator, bo.getPrimaryIndicator());
        lqw.eq(StringUtils.isNotBlank(bo.getSecondaryIndicator()), PerformanceFeedbackMain::getSecondaryIndicator, bo.getSecondaryIndicator());
        lqw.eq(bo.getEventStartTime() != null, PerformanceFeedbackMain::getEventStartTime, bo.getEventStartTime());
        lqw.eq(bo.getEventEndTime() != null, PerformanceFeedbackMain::getEventEndTime, bo.getEventEndTime());
        return lqw;
    }

    /**
     * 批量提交
     *
     * @param ids       绩效反馈主表ID集合
     * @param submitter 提交人
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchSubmit(Collection<Long> ids, String submitter) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        List<PerformanceFeedbackMain> mainList = baseMapper.selectBatchIds(ids);
        if (mainList.isEmpty()) {
            return false;
        }

        // 校验：仅限于未提交或待重提状态有效
        for (PerformanceFeedbackMain main : mainList) {
            String submitStatus = main.getSubmitStatus();
            if (!PerformanceFeedbackSubmitStatusEnum.NOT_SUBMITTED.getCode().equals(submitStatus)
                    && !PerformanceFeedbackSubmitStatusEnum.PENDING_RESUBMIT.getCode().equals(submitStatus)) {
                throw new ServiceException("仅限于未提交或待重提状态的数据可提交");
            }
        }

        Date submitTime = new Date();
        for (PerformanceFeedbackMain main : mainList) {
            // 校验SACD绩效反馈数据数量
            List<PerformanceFeedback> feedbackList = performanceFeedbackService.list(Wrappers.lambdaQuery(PerformanceFeedback.class)
                .eq(PerformanceFeedback::getMainFeedbackId, main.getId()));
            validationService.validateNewPerformanceFeedback(feedbackList, submitter, main.getYear(), main.getMonth(), main.getEventEndTime());

            main.setSubmitStatus(PerformanceFeedbackSubmitStatusEnum.SUBMITTED.getCode());
            main.setProjectManagerAuditStatus(PerformanceFeedbackAuditStatusEnum.NOT_AUDITED.getCode());
            main.setProjectManagerRemark("");
            main.setProjectManagerAuditTime(null);
            main.setFinalAudit(PerformanceFeedbackAuditStatusEnum.NOT_AUDITED.getCode());
            main.setFinalRemark("");
            main.setFinalAuditTime(null);
            main.setSubmitTime(submitTime);
            main.setSubmitter(submitter);
            // 更新绩效反馈主表的提交人
            this.updateById(main);
        }

        return true;
    }

    /**
     * 批量最终审核
     *
     * @param ids     绩效反馈主表ID集合
     * @param status  审核状态
     * @param auditor 审核人
     * @param remark  审核备注
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchFinalAudit(Collection<Long> ids, String status, String auditor, String remark) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        List<PerformanceFeedbackMain> mainList = baseMapper.selectBatchIds(ids);
        if (mainList.isEmpty()) {
            return false;
        }
        // 校验：仅限于提交状态为已提交，项管审核状态为同意，最终审核状态为未审核时有效
        for (PerformanceFeedbackMain main : mainList) {
            if (!PerformanceFeedbackSubmitStatusEnum.SUBMITTED.getCode().equals(main.getSubmitStatus())
                    || !PerformanceFeedbackAuditStatusEnum.APPROVED.getCode().equals(main.getProjectManagerAuditStatus())
                    || !PerformanceFeedbackAuditStatusEnum.NOT_AUDITED.getCode().equals(main.getFinalAudit())) {
                throw new ServiceException("仅限于提交状态为已提交，项管审核状态为同意，最终审核状态为未审核时可审核");
            }
        }

        // 校验绩效数据登记规则
        validationService.validateFinalAudit(mainList,status);

        Date auditTime = new Date();
        for (PerformanceFeedbackMain main : mainList) {
            main.setFinalAudit(status);
            main.setFinalAuditTime(auditTime);
            main.setFinalRemark(remark);
            // 审核拒绝时，提交状态变为待重提
            if (PerformanceFeedbackAuditStatusEnum.REJECTED.getCode().equals(status)) {
                main.setSubmitStatus(PerformanceFeedbackSubmitStatusEnum.PENDING_RESUBMIT.getCode());
            }
        }
        return this.updateBatchById(mainList);
    }

    @Override
    public List<PerformanceFeedback> getBySecondaryIndicator(String secondaryIndicator, Integer year, Integer month, List<String> nickNames) {
        return baseMapper.getBySecondaryIndicator(secondaryIndicator, year, month, nickNames);
    }
}
