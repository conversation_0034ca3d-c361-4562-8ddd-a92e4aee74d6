package com.qmqb.imp.app.domain.enums;

import com.qmqb.imp.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <p>
 * 指标类型：
 * 1、任务总数；
 * 2、工作耗时；
 * 3、文档数；
 * 4、代码总数；
 * 5、用例总数；
 * 6、项目总数；
 * 7、上线数；
 * 8、生产故障数。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Getter
@AllArgsConstructor
public enum IndicatorType {

    /**
     * 指标类型
     */
    TASK_COUNT("任务总数", 1),
    WORK_TIME("工作耗时", 2),
    DOC_COUNT("文档数", 3),
    CODE_COUNT("代码总数", 4),
    CASE_COUNT("用例总数", 5),
    PROJECT_COUNT("项目总数", 6),
    RELEASE_COUNT("上线数", 7),
    PROD_FAULT_COUNT("生产故障数", 8),

    ;

    final String name;
    final int type;

    public static IndicatorType indexOf(Integer type) {
        return Arrays.stream(values()).filter(e -> Objects.equals(e.type, type)).findFirst().orElseThrow(() -> new ServiceException("未知的指标类型"));
    }
}
