package com.qmqb.imp.system.domain.bo.performance;


import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 绩效评审等级入参
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
public class PerformanceLevelBo {

    /**
     * id
     */
    @NotEmpty(message = "id不能为空")
    private List<Long> ids;

    /**
     * 评审等级
     */
    @NotBlank(message = "评审等级不能为空")
    private String level;
}
