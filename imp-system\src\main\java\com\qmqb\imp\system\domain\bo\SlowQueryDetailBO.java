package com.qmqb.imp.system.domain.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;


/**
 * 分支详细信息对象 tb_branch
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
@Data
public class SlowQueryDetailBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "SQL哈希值")
    private String sqlHash;

}
