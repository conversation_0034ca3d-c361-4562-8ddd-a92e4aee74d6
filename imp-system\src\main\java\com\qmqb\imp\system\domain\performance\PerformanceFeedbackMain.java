package com.qmqb.imp.system.domain.performance;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 绩效反馈主表对象 tb_performance_feedback_main
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_performance_feedback_main")
public class PerformanceFeedbackMain extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 事件ID
     */
    @TableField("event_id")
    private Long eventId;

    /**
     * 反馈编码
     */
    private String feedbackCode;
    /**
     * 年份
     */
    private Integer year;

    /**
     * 月份
     */
    private Integer month;

    /**
     * 反馈时间
     */
    private Date feedbackTime;

    /**
     * 一类指标
     */
    private String primaryIndicator;

    /**
     * 二类指标
     */
    private String secondaryIndicator;

    /**
     * 事件标题
     */
    private String eventTitle;

    /**
     * 事件明细
     */
    private String eventDetail;

    /**
     * 事件发生开始时间
     */
    private Date eventStartTime;

    /**
     * 事件发生结束时间
     */
    private Date eventEndTime;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 提交状态
     */
    private String submitStatus;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 提交人
     */
    private String submitter;

    /**
     * 项管审核状态
     */
    private String projectManagerAuditStatus;

    /**
     * 项管审核人
     */
    private String projectManagerAuditor;

    /**
     * 项管审核时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date projectManagerAuditTime;

    /**
     * 项管审核备注
     */
    private String projectManagerRemark;

    /**
     * 最终审核状态
     */
    private String finalAudit;

    /**
     * 最终审核时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date finalAuditTime;

    /**
     * 最终审核备注
     */
    private String finalRemark;

    /**
     * 备注
     */
    private String remark;
}
