package com.qmqb.imp.system.service.impl.performance;

import cn.hutool.core.bean.BeanUtil;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.qmqb.imp.common.enums.IndicatorCategoryEnum;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.domain.bo.performance.PerformIndicatorResultBo;
import com.qmqb.imp.system.domain.performance.PerformIndicatorResult;
import com.qmqb.imp.system.mapper.performance.PerformIndicatorResultMapper;
import com.qmqb.imp.system.service.indicator.IPerformIndicatorResultService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.qmqb.imp.system.domain.vo.PerformIndicatorResultVo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 绩效指标原因Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@RequiredArgsConstructor
@Service
public class PerformIndicatorResultServiceImpl implements IPerformIndicatorResultService {

    private final PerformIndicatorResultMapper baseMapper;


    /**
     * 查询绩效指标原因列表
     */
    @Override
    public TableDataInfo<PerformIndicatorResultVo> queryPageList(PerformIndicatorResultBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PerformIndicatorResult> lqw = buildQueryWrapper(bo);
        Page<PerformIndicatorResultVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        result.getRecords().stream().forEach(item -> {
            item.setFirstIndecatorName(IndicatorCategoryEnum.getByCode(item.getFirstIndecator()).getName());
            item.setSecondIndecatorName(PerformanceIndicatorEnum.fromCode(item.getSecondIndecator()).getName());
        });
        return TableDataInfo.build(result);
    }

    /**
     * 查询绩效指标原因列表
     */
    @Override
    public List<PerformIndicatorResultVo> queryList(PerformIndicatorResultBo bo) {
        LambdaQueryWrapper<PerformIndicatorResult> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PerformIndicatorResult> buildQueryWrapper(PerformIndicatorResultBo bo) {
        LambdaQueryWrapper<PerformIndicatorResult> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getFirstIndecator()), PerformIndicatorResult::getFirstIndecator, bo.getFirstIndecator());
        lqw.eq(StringUtils.isNotBlank(bo.getSecondIndecator()), PerformIndicatorResult::getSecondIndecator, bo.getSecondIndecator());
        lqw.eq(StringUtils.isNotBlank(bo.getLevel()), PerformIndicatorResult::getLevel, bo.getLevel());
        lqw.eq(StringUtils.isNotBlank(bo.getResult()), PerformIndicatorResult::getResult, bo.getResult());
        return lqw;
    }

    /**
     * 新增绩效指标原因
     */
    @Override
    public Boolean insertByBo(PerformIndicatorResultBo bo) {
        // 按换行符拆分
        String[] results = bo.getResult().split("\\r?\\n");
        List<PerformIndicatorResult> list = new ArrayList<>();
        for (String result : results) {
            if (StringUtils.isBlank(result)) {
                continue;
            }
            PerformIndicatorResult add = BeanUtil.toBean(bo, PerformIndicatorResult.class);
            add.setResult(result.trim());
            list.add(add);
        }
        if (!list.isEmpty()) {
            baseMapper.insertBatch(list);
        }
        return true;
    }

    /**
     * 修改绩效指标原因
     */
    @Override
    public Boolean updateByBo(PerformIndicatorResultBo bo) {
        PerformIndicatorResult update = BeanUtil.toBean(bo, PerformIndicatorResult.class);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public void deleteByIds(Long[] resultIds) {
        baseMapper.deleteBatchIds(Arrays.asList(resultIds));
    }

}
