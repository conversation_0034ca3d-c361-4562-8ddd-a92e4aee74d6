package com.qmqb.imp.system.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-06-03 15:42
 */

@Data
public class PerfStatExportVO {

    @ExcelProperty(value = "编号")
    private Integer index;

    /**
     * 年份
     */
    @ExcelProperty(value = "年份")
    private Integer workYear;

    /**
     * 月份
     */
    @ExcelProperty(value = "月份")
    private Integer workMonth;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String workUsername;

    /**
     * 工作时长(时)
     */
    @ExcelProperty(value = "工作时长(小时)")
    private String kqAttendanceWorkTime;

    /**
     * 每日工时(时)
     */
    @ExcelProperty(value = "每日工时(工作日)")
    private String kqDayAttendanceWorkTime;

    /**
     * 文档数
     */
    @ExcelProperty(value = "文档数")
    private Integer docCount;



    /**
     * 完成的任务
     */
    @ExcelProperty(value = "完成的任务")
    private Integer workDoneClosedTaskCount;


    /**
     * 开发人员任务总数:完成的任务+解决bug数
     */
    @ExcelProperty(value = "开发人员任务总数")
    private Integer allWorkTaskCount;

    /**
     * 测试人员任务总数:完成执行用例数量+关闭bug数
     */
    @ExcelProperty(value = "测试人员任务总数")
    private Integer testerAllWorkCount;


    /**
     * 进行中的任务
     */
    @ExcelProperty(value = "进行中的任务")
    private Integer workDoingTaskCount;

    /**
     * 解决bug数
     */
    @ExcelProperty(value = "解决bug数")
    private Integer workResolveBugCount;

    /**
     * 小时/每任务:工作时长/完成的总任务数
     */
    @ExcelProperty(value = "小时/每任务")
    private String timePerTask;


    /**
     * 完成执行用例数量
     */
    @ExcelProperty(value = "完成执行用例数量")
    private Integer workCaseCount;

    /**
     * 关闭bug数
     */
    @ExcelProperty(value = "关闭bug数")
    private Integer workCloseBugCount;

    /**
     * 小时/每测试任务
     */
    @ExcelProperty(value = "小时/每测试任务")
    private String timePerTestTask;


    /**
     * 旷工天数
     */
    @ExcelProperty(value = "旷工天数")
    private Integer kqAbsenteeismDays;

    /**
     * 出勤天数
     */
    @ExcelProperty(value = "出勤天数")
    private Integer kqAttendanceDays;


    /**
     * 迟到时长
     */
    @ExcelProperty(value = "迟到时长(分钟)")
    private Integer kqLateMinute;

    /**
     * 加班-审批单次数
     */
    @ExcelProperty(value = "加班时长(小时)")
    private Integer kqOvertimeApproveCount;

    /**
     * 迟到次数
     */
    @ExcelProperty(value = "迟到次数")
    private Integer kqLateCount = 0;

    /**
     * 严重迟到时长
     */
    @ExcelProperty(value = "严重迟到时长")
    private Integer kqYzLateTimes = 0;

    /**
     * 严重迟到次数
     */
    @ExcelProperty(value = "严重迟到次数")
    private Integer kqYzLateCount = 0;
}
