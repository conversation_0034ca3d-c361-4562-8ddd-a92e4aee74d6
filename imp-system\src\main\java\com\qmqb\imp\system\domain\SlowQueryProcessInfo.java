package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 慢SQL处理信息表实体
 * @date 2025/5/28 14:12
 */
@Data
@TableName("t_slow_query_process_info")
public class SlowQueryProcessInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 同类SQL的HASH值（唯一标识）
     */
    private String sqlHash;


    /**
     * 处理状态：NO=未处理，YES=已处理
     */
    private String processStatus;
    /**
     * 处理时间
     */
    private LocalDateTime processTime;
    /**
     * 处理人
     */
    private String processBy;
    /**
     * 处理结果
     */
    private String processResult;

    /**
     * AI分析结果（存储分析结论/优化建议）
     */
    private String analysisResult;
}
