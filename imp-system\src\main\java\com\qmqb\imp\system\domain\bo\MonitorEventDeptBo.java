package com.qmqb.imp.system.domain.bo;

import com.qmqb.imp.common.core.domain.BaseEntity;
import com.qmqb.imp.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 预警事件和部门关联业务对象 tb_monitor_event_dept
 *
 * <AUTHOR>
 * @date 2025-04-23
 */

@Data
public class MonitorEventDeptBo{

    /**
     * 事件id
     */
    @NotNull(message = "事件id不能为空", groups = { EditGroup.class })
    private Long eventId;

    /**
     * 部门id
     */
    @NotNull(message = "部门id不能为空", groups = { EditGroup.class })
    private Long deptId;


}
