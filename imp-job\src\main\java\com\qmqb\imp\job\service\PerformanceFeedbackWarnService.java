package com.qmqb.imp.job.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.Constants;
import com.qmqb.imp.common.constant.UserConstants;
import com.qmqb.imp.common.core.domain.BaseEntity;
import com.qmqb.imp.common.core.domain.TreeEntity;
import com.qmqb.imp.common.core.domain.entity.SysDept;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.DeptTypeEnum;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.common.enums.PerformanceFeedbackSubmitStatusEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.job.api.config.DingTalkConfig;
import com.qmqb.imp.system.domain.ReleaseRecord;
import com.qmqb.imp.system.domain.bo.message.DdRobotMsgBo;
import com.qmqb.imp.system.domain.performance.PerformanceFeedback;
import com.qmqb.imp.system.domain.performance.PerformanceFeedbackMain;
import com.qmqb.imp.system.domain.performance.PerformanceTutoring;
import com.qmqb.imp.system.mapper.ReleaseRecordMapper;
import com.qmqb.imp.system.mapper.SysDeptMapper;
import com.qmqb.imp.system.mapper.SysUserMapper;
import com.qmqb.imp.system.mapper.performance.PerformanceFeedbackMainMapper;
import com.qmqb.imp.system.mapper.performance.PerformanceFeedbackMapper;
import com.qmqb.imp.system.mapper.performance.PerformanceTutoringMapper;
import com.qmqb.imp.system.mapper.performance.PerformanceEventMapper;
import com.qmqb.imp.system.runner.HolidayUtil;
import com.qmqb.imp.system.service.message.IMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import com.qmqb.imp.system.domain.performance.PerformanceEvent;

/**
 * <AUTHOR>
 * @description 绩效反馈通知
 * @date 2025/7/2 17:40
 */
@Component
@Slf4j
public class PerformanceFeedbackWarnService {

    @Resource
    private SysDeptMapper sysDeptMapper;
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private DingTalkConfig dingTalkConfig;
    @Resource
    private IMessageService messageService;
    @Resource
    private PerformanceFeedbackMainMapper performanceFeedbackMainMapper;
    @Resource
    private PerformanceFeedbackMapper performanceFeedbackMapper;
    @Resource
    private PerformanceTutoringMapper performanceTutoringMapper;
    @Resource
    private ReleaseRecordMapper releaseRecordMapper;
    @Resource
    private PerformanceEventMapper performanceEventMapper;


    /**
     * 组长绩效反馈通知
     *
     * @param param
     * @return
     */
    @TraceId("组长绩效反馈通知")
    @XxlJob("leaderPerformanceFeedbackWarnJobHandler")
    public ReturnT<String> leaderPerformanceFeedbackWarnJobHandler(String param) {
        log.info("开始执行组长绩效反馈通知任务，参数：{}", param);
        try {
            Date nowDate = DateUtils.getNowDate();
            Date nowDateStart = DateUtils.dateToStart(nowDate);
            //节假日不执行定时任务
            if (HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, nowDate))) {
                log.info("节假日不执行定时任务");
                return ReturnT.SUCCESS;
            }
            List<SysDept> deptList = sysDeptMapper.selectList(new LambdaQueryWrapper<SysDept>()
                .eq(TreeEntity::getParentId, UserConstants.JSZX_DEPT_ID)
                .ne(SysDept::getDeptType, DeptTypeEnum.PM.getCode()));
            Map<Long, String> deptLeaderMap = deptList.stream()
                .collect(Collectors.toMap(SysDept::getDeptId, SysDept::getLeader));
            List<String> leaderNameList = deptList.stream()
                .flatMap(sysDept -> Arrays.stream(sysDept.getLeader().split(",")).filter(StringUtils::isNotBlank))
                .collect(Collectors.toList());
            List<SysUser> leaderList = sysUserMapper.selectList(new LambdaQueryWrapper<SysUser>().in(SysUser::getNickName, leaderNameList));
            Map<String, String> leaderNickNameMap = leaderList.stream().collect(Collectors.toMap(SysUser::getUserName, SysUser::getNickName));

            // 发送昨日及以前有未提交状态绩效反馈数据的组长名单
            sendNotSubmittedWarning(nowDateStart, leaderNickNameMap);
            // 连续5个工作日以上没有提交任何绩效反馈的组长名单
            sendInFiveWorkDayNotSubmittedFeedBackWarning(nowDateStart, leaderNickNameMap);
            // 超过2周只提交S或A指标，未提交C或D反馈的组长名单
            sendSubmitLevelAbnormalLeaderWarn(nowDateStart, leaderNickNameMap);
            // 有C或D绩效，超过3个工作日未填写绩效辅导的组长名单
            sendMoreThanThreeWorkDayNotTutoringLeaderList(nowDateStart, deptLeaderMap);
            log.info("组长绩效反馈通知任务执行完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("组长绩效反馈通知任务执行失败", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 新增绩效事件登记预警
     *
     * 规则：
     * 1) 从本月1号到今天，统计未登记任何绩效事件的组长&项目经理名单
     * 2) 统计本月未提交状态的绩效事件数量X
     * 3) 发送固定文案
     */
    @TraceId("新增绩效事件登记预警")
    @XxlJob("performanceEventRegistrationWarnJobHandler")
    public ReturnT<String> performanceEventRegistrationWarnJobHandler(String param) {
        log.info("开始执行新增绩效事件登记预警任务，参数：{}", param);
        try {
            Date nowDate = DateUtils.getNowDate();
            if (HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, nowDate))) {
                log.info("节假日不执行定时任务");
                return ReturnT.SUCCESS;
            }

            Date monthStartDate = DateUtils.dateToStart(DateUtils.getFirstDayOfMonth(nowDate));
            Date nowDateEnd = DateUtils.dateToEnd(nowDate);

            List<SysUser> leaderUsers = findLeaderUsers();
            List<SysUser> pmUsers = findPmUsers();

            Map<String, String> leaderNickNameMap = toUsernameNickMap(leaderUsers);
            Map<String, String> pmNickNameMap = toUsernameNickMap(pmUsers);
            Map<String, String> combinedUserNickMap = new HashMap<>(leaderNickNameMap);
            combinedUserNickMap.putAll(pmNickNameMap);

            List<String> inactiveUserNames = findInactiveUsernames(combinedUserNickMap.keySet(), monthStartDate, nowDateEnd);
            List<String> inactiveNickNames = inactiveUserNames.stream()
                .map(combinedUserNickMap::get)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

            Long unsubmittedCount = countUnsubmittedEvents(monthStartDate, nowDateEnd);
            String content = buildRegistrationWarnContent(inactiveNickNames, unsubmittedCount);

            Map<String, String> usernameToPhone = toUsernamePhoneMap(leaderUsers, pmUsers);
            List<String> atMobiles = buildAtMobiles(inactiveUserNames, usernameToPhone);
            if (CollectionUtils.isNotEmpty(atMobiles)) {
                send(dingTalkConfig.getRobotUrl(), content, atMobiles, false);
            } else {
                send(dingTalkConfig.getRobotUrl(), content);
            }
            log.info("新增绩效事件登记预警任务执行完成，未登记人数：{}，未提交事件数：{}", inactiveNickNames.size(), unsubmittedCount);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("新增绩效事件登记预警任务执行失败", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 项目管理绩效反馈通知
     *
     * @param param
     * @return
     */
    @TraceId("项目管理绩效反馈通知")
    @XxlJob("managerPerformanceFeedbackWarnJobHandler")
    public ReturnT<String> managerPerformanceFeedbackWarnJobHandler(String param) {
        log.info("开始执行项目管理绩效反馈通知任务，参数：{}", param);
        try {
            Date nowDate = DateUtils.getNowDate();
            Date nowDateStart = DateUtils.dateToStart(nowDate);
            //节假日不执行定时任务
            if (HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, nowDate))) {
                log.info("节假日不执行定时任务");
                return ReturnT.SUCCESS;
            }
            List<Long> pmDeptIdList = sysDeptMapper.selectList(new LambdaQueryWrapper<SysDept>()
                    .eq(TreeEntity::getParentId, UserConstants.JSZX_DEPT_ID)
                    .eq(SysDept::getDeptType, DeptTypeEnum.PM.getCode()))
                .stream()
                .map(SysDept::getDeptId)
                .collect(Collectors.toList());
            Map<String, String> pmNickNameMap = sysUserMapper.selectList(new LambdaQueryWrapper<SysUser>().in(SysUser::getDeptId, pmDeptIdList))
                .stream().collect(Collectors.toMap(SysUser::getUserName, SysUser::getNickName));

            // 连续2个工作日没有新增的项目成果
            inTwoFiveWorkDayNotCreateRelease(nowDateStart, pmNickNameMap);
            // 连续5个工作日以上没有提交任何绩效反馈
            sendInFiveWorkDayNotCreateFeedBackWarning(nowDateStart, pmNickNameMap);
            // 超过2周只提交S或A指标，未提交C或D反馈的项目经理名单
            sendSubmitLevelAbnormalPmWarn(nowDateStart, pmNickNameMap);
            log.info("项目管理绩效反馈通知任务执行完成");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("项目管理绩效反馈通知任务执行失败", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 发送预警
     *
     * @param robotUrl 机器人url
     * @param template 模板
     * @param map      数据
     */
    private void send(String robotUrl, String template, Map<String, String> map) {
        String content = StrUtil.format(template, map);
        DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder()
            .url(robotUrl)
            .msgtype("text")
            .content(content)
            .build();
        robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
        try {
            messageService.sendBase(robotMsgBo);
        } catch (Exception e) {
            log.error("调用消息中心发送消息异常。异常信息：{}", e.getMessage());
        }
    }

    /**
     * 发送纯文本预警
     *
     * @param robotUrl 机器人url
     * @param content  内容
     */
    private void send(String robotUrl, String content) {
        DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder()
            .url(robotUrl)
            .msgtype("text")
            .content(content)
            .build();
        robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
        try {
            messageService.sendBase(robotMsgBo);
        } catch (Exception e) {
            log.error("调用消息中心发送消息异常。异常信息：{}", e.getMessage());
        }
    }

    /**
     * 发送携带@手机号的文本预警
     *
     * @param robotUrl  机器人url
     * @param content   内容
     * @param atMobiles 需要@的手机号集合
     * @param isAtAll   是否@所有人
     */
    private void send(String robotUrl, String content, List<String> atMobiles, boolean isAtAll) {
        DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder()
            .url(robotUrl)
            .msgtype("text")
            .content(content)
            .isAtAll(Boolean.toString(isAtAll))
            .atMobiles(atMobiles)
            .build();
        robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
        try {
            messageService.sendBase(robotMsgBo);
        } catch (Exception e) {
            log.error("调用消息中心发送消息异常。异常信息：{}", e.getMessage());
        }
    }

    /**
     * 发送昨日及以前有未提交状态绩效反馈数据的组长名单
     *
     * @param nowDateStart
     * @param leaderNickNameMap
     */
    private void sendNotSubmittedWarning(Date nowDateStart, Map<String, String> leaderNickNameMap) {
        List<String> yesterdayNotSubmittedLeaderList = new ArrayList<>();
        List<String> beforeNotSubmittedLeaderList = new ArrayList<>();
        if (!leaderNickNameMap.isEmpty()) {
            List<PerformanceFeedbackMain> notSubmittedList = performanceFeedbackMainMapper.selectList(new LambdaQueryWrapper<PerformanceFeedbackMain>()
                .in(PerformanceFeedbackMain::getCreateBy, leaderNickNameMap.keySet())
                .lt(BaseEntity::getCreateTime, nowDateStart)
                .eq(PerformanceFeedbackMain::getSubmitStatus, PerformanceFeedbackSubmitStatusEnum.NOT_SUBMITTED.getCode()));
            yesterdayNotSubmittedLeaderList = notSubmittedList.stream()
                .filter(item -> !item.getCreateTime().before(DateUtils.plusDay(nowDateStart, -1)))
                .map(item -> leaderNickNameMap.getOrDefault(item.getCreateBy(), ""))
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
            beforeNotSubmittedLeaderList = notSubmittedList.stream()
                .filter(item -> item.getCreateTime().before(DateUtils.plusDay(nowDateStart, -1)))
                .map(item -> leaderNickNameMap.getOrDefault(item.getCreateBy(), ""))
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        }
        Map<String, String> map = new HashMap<>(16);
        map.put("yesterdayNotSubmitLeaders", CollectionUtils.isEmpty(yesterdayNotSubmittedLeaderList) ? "无" :
            StringUtils.join(yesterdayNotSubmittedLeaderList, "、"));
        map.put("beforeNotSubmitLeaders", CollectionUtils.isEmpty(beforeNotSubmittedLeaderList) ? "无" :
            StringUtils.join(beforeNotSubmittedLeaderList, "、"));
        send(dingTalkConfig.getRobotUrl(), Constants.NOT_SUBMIT_PERFORMANCE_FEEDBACK, map);
    }

    /**
     * 连续5个工作日以上没有提交任何绩效反馈的组长名单
     *
     * @param nowDateStart
     * @param leaderNickNameMap
     */
    private void sendInFiveWorkDayNotSubmittedFeedBackWarning(Date nowDateStart, Map<String, String> leaderNickNameMap) {
        Date beforeFiveWorkDay = DateUtils.getBeforeWorkDay(nowDateStart, 5);
        List<PerformanceFeedbackMain> inFiveWorkDaySumbitList = performanceFeedbackMainMapper.selectList(new LambdaQueryWrapper<PerformanceFeedbackMain>()
            .eq(PerformanceFeedbackMain::getSubmitStatus, PerformanceFeedbackSubmitStatusEnum.SUBMITTED.getCode())
            .lt(PerformanceFeedbackMain::getSubmitTime, nowDateStart)
            .ge(PerformanceFeedbackMain::getSubmitTime, beforeFiveWorkDay));
        List<String> inFiveWorkDaySumbitUserNameList = inFiveWorkDaySumbitList.stream()
            .filter(item -> !HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, item.getSubmitTime())))
            .map(PerformanceFeedbackMain::getSubmitter)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toList());
        List<String> notSubmitUserList = leaderNickNameMap.entrySet().stream()
            .filter(entry -> !inFiveWorkDaySumbitUserNameList.contains(entry.getKey())).map(Map.Entry::getValue).collect(Collectors.toList());
        Map<String, String> map = Collections.singletonMap("users", CollectionUtils.isEmpty(notSubmitUserList) ? "无" :
            StringUtils.join(notSubmitUserList, "、"));
        send(dingTalkConfig.getRobotUrl(), Constants.NOT_SUBMIT_PERFORMANCE_FEEDBACK_MORE_THAN_FIVE_DAYS, map);
    }

    /**
     * 超过2周只提交S或A指标，未提交C或D反馈的组长名单
     *
     * @param nowDateStart
     * @param leaderNickNameMap
     */
    private void sendSubmitLevelAbnormalLeaderWarn(Date nowDateStart, Map<String, String> leaderNickNameMap) {
        List<String> submitLevelAbnormalLeaderList = new ArrayList<>();
        if (!leaderNickNameMap.isEmpty()) {
            Date beforeTwoWeeks = DateUtils.plusDay(nowDateStart, -14);
            List<Long> inTwoWeeksSumbitFeedbackIdList = performanceFeedbackMainMapper.selectList(new LambdaQueryWrapper<PerformanceFeedbackMain>()
                    .select(PerformanceFeedbackMain::getId)
                    .eq(PerformanceFeedbackMain::getSubmitStatus, PerformanceFeedbackSubmitStatusEnum.SUBMITTED.getCode())
                    .ge(PerformanceFeedbackMain::getSubmitTime, beforeTwoWeeks)
                    .lt(PerformanceFeedbackMain::getSubmitTime, nowDateStart))
                .stream().map(PerformanceFeedbackMain::getId)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(inTwoWeeksSumbitFeedbackIdList)) {
                Map<String, List<String>> levelMap = performanceFeedbackMapper.selectList(new LambdaQueryWrapper<PerformanceFeedback>()
                        .in(PerformanceFeedback::getMainFeedbackId, inTwoWeeksSumbitFeedbackIdList)
                        .eq(PerformanceFeedback::getIsCanceled, 0))
                    .stream()
                    .filter(item -> StringUtils.isNotBlank(item.getCreateBy()))
                    .collect(Collectors.groupingBy(PerformanceFeedback::getCreateBy, Collectors.collectingAndThen(Collectors.toList(),
                        list -> list.stream().map(PerformanceFeedback::getRecommendedLevel).collect(Collectors.toList()))));
                submitLevelAbnormalLeaderList = leaderNickNameMap.entrySet().stream().filter(entry -> {
                    List<String> levelList = levelMap.getOrDefault(entry.getKey(), Collections.emptyList());
                    if (CollectionUtils.containsAny(levelList, ScoreLevelEnum.SCORE_S.getCode(), ScoreLevelEnum.SCORE_A.getCode())
                        && !CollectionUtils.containsAny(levelList, ScoreLevelEnum.SCORE_C.getCode(), ScoreLevelEnum.SCORE_D.getCode())) {
                        return true;
                    }
                    return false;
                }).map(Map.Entry::getValue).collect(Collectors.toList());
            }
        }
        Map<String, String> map = Collections.singletonMap("leaders", CollectionUtils.isEmpty(submitLevelAbnormalLeaderList) ? "无" :
            StringUtils.join(submitLevelAbnormalLeaderList, "、"));
        send(dingTalkConfig.getRobotUrl(), Constants.ONLY_SUBMIT_S_OR_A_MORE_THAN_TWO_WEEKS, map);
    }

    /**
     * 有C或D绩效，超过3个工作日未填写绩效辅导的组长名单
     *
     * @param nowDateStart
     * @param deptLeaderMap
     */
    private void sendMoreThanThreeWorkDayNotTutoringLeaderList(Date nowDateStart, Map<Long, String> deptLeaderMap) {
        List<String> moreThanThreeWorkDayNotTutoringLeaderList = new ArrayList<>();
        Date beforeThreeWorkDay = DateUtils.getBeforeWorkDay(nowDateStart, 3);
        List<Long> beforeThreeWorkDayNotTutoringFeedbackIdList = performanceTutoringMapper.selectList(new LambdaQueryWrapper<PerformanceTutoring>()
                .lt(BaseEntity::getCreateTime, beforeThreeWorkDay)
                .isNull(PerformanceTutoring::getTutoringTime))
            .stream().map(PerformanceTutoring::getFeedbackId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(beforeThreeWorkDayNotTutoringFeedbackIdList)) {
            List<Long> beforeThreeWorkDayNotTutoringGroupIdList = performanceFeedbackMapper.selectList(new LambdaQueryWrapper<PerformanceFeedback>()
                    .in(PerformanceFeedback::getId, beforeThreeWorkDayNotTutoringFeedbackIdList)
                    .eq(PerformanceFeedback::getIsCanceled, 0))
                .stream().map(PerformanceFeedback::getGroupId).collect(Collectors.toList());
            moreThanThreeWorkDayNotTutoringLeaderList = deptLeaderMap.entrySet()
                .stream()
                .filter(entry -> beforeThreeWorkDayNotTutoringGroupIdList.contains(entry.getKey()))
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());
        }
        Map<String, String> map = Collections.singletonMap("leaders", CollectionUtils.isEmpty(moreThanThreeWorkDayNotTutoringLeaderList) ? "无" :
            StringUtils.join(moreThanThreeWorkDayNotTutoringLeaderList, "、"));
        send(dingTalkConfig.getRobotUrl(), Constants.NOT_SUBMIT_PERFORMANCE_COACHING, map);
    }

    /**
     * 连续2个工作日没有新增的项目成果
     *
     * @param nowDateStart
     * @param pmNickNameMap
     */
    private void inTwoFiveWorkDayNotCreateRelease(Date nowDateStart, Map<String, String> pmNickNameMap) {
        Date beforeTwoWorkDay = DateUtils.getBeforeWorkDay(nowDateStart, 2);
        List<String> pmList = releaseRecordMapper.selectList(new LambdaQueryWrapper<ReleaseRecord>()
                .ge(ReleaseRecord::getCreatedTime, beforeTwoWorkDay)
                .lt(ReleaseRecord::getCreatedTime, nowDateStart))
            .stream()
            .filter(item -> !HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, item.getCreatedTime())))
            .map(ReleaseRecord::getProjectManager)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toList());
        List<String> inTwoFiveWorkDayNotCreateReleasePmList = pmNickNameMap.entrySet()
            .stream()
            .filter(entry -> !pmList.contains(entry.getKey()))
            .map(Map.Entry::getValue)
            .collect(Collectors.toList());
        Map<String, String> map = Collections.singletonMap("managers", CollectionUtils.isEmpty(inTwoFiveWorkDayNotCreateReleasePmList) ? "无" :
            StringUtils.join(inTwoFiveWorkDayNotCreateReleasePmList, "、"));
        send(dingTalkConfig.getPmRobotUrl(), Constants.NOT_ADD_PROJECT_OUTCOMES_MORE_THAN_TWO_DAYS, map);
    }


    /**
     * 连续5个工作日以上没有提交任何绩效反馈
     *
     * @param nowDateStart
     * @param pmNickNameMap
     */
    private void sendInFiveWorkDayNotCreateFeedBackWarning(Date nowDateStart, Map<String, String> pmNickNameMap) {

        Date beforeFiveWorkDay = DateUtils.getBeforeWorkDay(nowDateStart, 5);
        List<PerformanceFeedbackMain> inFiveWorkDaySumbitList = performanceFeedbackMainMapper.selectList(new LambdaQueryWrapper<PerformanceFeedbackMain>()
            .eq(PerformanceFeedbackMain::getSubmitStatus, PerformanceFeedbackSubmitStatusEnum.SUBMITTED.getCode())
            .lt(PerformanceFeedbackMain::getSubmitTime, nowDateStart)
            .ge(PerformanceFeedbackMain::getSubmitTime, beforeFiveWorkDay));
        List<String> inFiveWorkDaySumbitUserNameList = inFiveWorkDaySumbitList.stream()
            .filter(item -> !HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, item.getSubmitTime())))
            .map(PerformanceFeedbackMain::getSubmitter)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toList());
        List<String> notCreatePmList = pmNickNameMap.entrySet().stream()
            .filter(entry -> !inFiveWorkDaySumbitUserNameList.contains(entry.getKey())).map(Map.Entry::getValue).collect(Collectors.toList());
        Map<String, String> map = Collections.singletonMap("managers", CollectionUtils.isEmpty(notCreatePmList) ? "无" :
            StringUtils.join(notCreatePmList, "、"));
        send(dingTalkConfig.getPmRobotUrl(), Constants.NOT_SUBMIT_PERFORMANCE_FEEDBACK_MORE_THAN_FIVE_DAYS_MANAGER, map);
    }

    /**
     * 超过2周只提交S或A指标，未提交C或D反馈的项目经理名单
     *
     * @param nowDateStart
     * @param pmNickNameMap
     */
    private void sendSubmitLevelAbnormalPmWarn(Date nowDateStart, Map<String, String> pmNickNameMap) {
        List<String> submitLevelAbnormalPmList = new ArrayList<>();
        if (!pmNickNameMap.isEmpty()) {
            Date beforeTwoWeeks = DateUtils.plusDay(nowDateStart, -14);
            List<Long> inTwoWeeksSumbitFeedbackIdList = performanceFeedbackMainMapper.selectList(new LambdaQueryWrapper<PerformanceFeedbackMain>()
                    .select(PerformanceFeedbackMain::getId)
                    .eq(PerformanceFeedbackMain::getSubmitStatus, PerformanceFeedbackSubmitStatusEnum.SUBMITTED.getCode())
                    .ge(PerformanceFeedbackMain::getSubmitTime, beforeTwoWeeks)
                    .lt(PerformanceFeedbackMain::getSubmitTime, nowDateStart))
                .stream().map(PerformanceFeedbackMain::getId)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(inTwoWeeksSumbitFeedbackIdList)) {
                Map<String, List<String>> levelMap = performanceFeedbackMapper.selectList(new LambdaQueryWrapper<PerformanceFeedback>()
                        .in(PerformanceFeedback::getMainFeedbackId, inTwoWeeksSumbitFeedbackIdList)
                        .eq(PerformanceFeedback::getIsCanceled, 0))
                    .stream()
                    .filter(item -> StringUtils.isNotBlank(item.getCreateBy()))
                    .collect(Collectors.groupingBy(PerformanceFeedback::getCreateBy, Collectors.collectingAndThen(Collectors.toList(),
                        list -> list.stream().map(PerformanceFeedback::getRecommendedLevel).collect(Collectors.toList()))));
                submitLevelAbnormalPmList = pmNickNameMap.entrySet().stream().filter(entry -> {
                    List<String> levelList = levelMap.getOrDefault(entry.getKey(), Collections.emptyList());
                    return CollectionUtils.containsAny(levelList, ScoreLevelEnum.SCORE_S.getCode(), ScoreLevelEnum.SCORE_A.getCode())
                        && !CollectionUtils.containsAny(levelList, ScoreLevelEnum.SCORE_C.getCode(), ScoreLevelEnum.SCORE_D.getCode());
                }).map(Map.Entry::getValue).collect(Collectors.toList());
            }
        }
        Map<String, String> map = Collections.singletonMap("managers", CollectionUtils.isEmpty(submitLevelAbnormalPmList) ? "无" :
            StringUtils.join(submitLevelAbnormalPmList, "、"));
        send(dingTalkConfig.getPmRobotUrl(), Constants.ONLY_SUBMIT_S_OR_A_MORE_THAN_TWO_WEEKS_MANAGER, map);
    }

    private List<SysUser> findLeaderUsers() {
        List<SysDept> deptList = sysDeptMapper.selectList(new LambdaQueryWrapper<SysDept>()
            .eq(TreeEntity::getParentId, UserConstants.JSZX_DEPT_ID)
            .ne(SysDept::getDeptType, DeptTypeEnum.PM.getCode()));
        List<String> leaderNameList = deptList.stream()
            .flatMap(sysDept -> Arrays.stream(sysDept.getLeader().split(",")).filter(StringUtils::isNotBlank))
            .collect(Collectors.toList());
        if (leaderNameList.isEmpty()) {
            return Collections.emptyList();
        }
        return sysUserMapper.selectList(new LambdaQueryWrapper<SysUser>().in(SysUser::getNickName, leaderNameList));
    }

    private List<SysUser> findPmUsers() {
        List<Long> pmDeptIdList = sysDeptMapper.selectList(new LambdaQueryWrapper<SysDept>()
                .eq(TreeEntity::getParentId, UserConstants.JSZX_DEPT_ID)
                .eq(SysDept::getDeptType, DeptTypeEnum.PM.getCode()))
            .stream().map(SysDept::getDeptId).collect(Collectors.toList());
        if (pmDeptIdList.isEmpty()) {
            return Collections.emptyList();
        }
        return sysUserMapper.selectList(new LambdaQueryWrapper<SysUser>().in(SysUser::getDeptId, pmDeptIdList));
    }

    private Map<String, String> toUsernameNickMap(List<SysUser> users) {
        if (CollectionUtils.isEmpty(users)) {
            return Collections.emptyMap();
        }
        return users.stream().collect(Collectors.toMap(SysUser::getUserName, SysUser::getNickName, (a, b) -> a));
    }

    private Map<String, String> toUsernamePhoneMap(List<SysUser> leaderUsers, List<SysUser> pmUsers) {
        Map<String, String> map = new HashMap<>(16);
        for (SysUser user : leaderUsers) {
            if (StringUtils.isNotBlank(user.getUserName())) {
                map.putIfAbsent(user.getUserName(), user.getPhonenumber());
            }
        }
        for (SysUser user : pmUsers) {
            if (StringUtils.isNotBlank(user.getUserName())) {
                map.putIfAbsent(user.getUserName(), user.getPhonenumber());
            }
        }
        return map;
    }

    private List<String> findInactiveUsernames(Set<String> allUserNames, Date start, Date end) {
        if (CollectionUtils.isEmpty(allUserNames)) {
            return Collections.emptyList();
        }
        List<PerformanceEvent> monthEvents = performanceEventMapper.selectList(new LambdaQueryWrapper<PerformanceEvent>()
            .ge(BaseEntity::getCreateTime, start)
            .lt(BaseEntity::getCreateTime, end)
            .and(w -> w.in(BaseEntity::getCreateBy, allUserNames)
                .or().in(PerformanceEvent::getSubmitter, allUserNames)));
        Set<String> registeredUserNames = monthEvents.stream()
            .flatMap(ev -> Arrays.stream(new String[]{ev.getCreateBy(), ev.getSubmitter()}))
            .filter(StringUtils::isNotBlank)
            .filter(allUserNames::contains)
            .collect(Collectors.toSet());
        return allUserNames.stream().filter(u -> !registeredUserNames.contains(u)).collect(Collectors.toList());
    }

    private Long countUnsubmittedEvents(Date start, Date end) {
        return performanceEventMapper.selectCount(new LambdaQueryWrapper<PerformanceEvent>()
            .ge(BaseEntity::getCreateTime, start)
            .lt(BaseEntity::getCreateTime, end)
            .eq(PerformanceEvent::getSubmitStatus, PerformanceFeedbackSubmitStatusEnum.NOT_SUBMITTED.getCode()));
    }

    private String buildRegistrationWarnContent(List<String> inactiveNickNames, Long unsubmittedCount) {
        StringBuilder content = new StringBuilder();
        if (CollectionUtils.isNotEmpty(inactiveNickNames)) {
            content.append("从本月1号到今天，未登记任何绩效事件的组长&项目经理如下：\n");
            content.append(String.join("、", inactiveNickNames));
            content.append("\n\n请以上人员注意观察团队各项事件，并记录优秀或不足表现，避免月度0绩效事件情况。");
            if (unsubmittedCount != null && unsubmittedCount > 0) {
                content.append("\n\n有").append(unsubmittedCount).append("个事件是未提交状态，请填写人注意提交，提交后才会进入审批流程。");
            }
            content.append("\n\n请评估为C或D的绩效事件及时填写绩效辅导记录。");
        } else {
            content.append("本月暂无未登记任何绩效事件的组长&项目经理");
            if (unsubmittedCount != null && unsubmittedCount > 0) {
                content.append("\n\n有").append(unsubmittedCount).append("个事件是未提交状态，请填写人注意提交，提交后才会进入审批流程。");
            }
            content.append("\n\n请评估为C或D的绩效事件及时填写绩效辅导记录。");
        }
        return content.toString();
    }

    private List<String> buildAtMobiles(List<String> inactiveUserNames, Map<String, String> usernameToPhone) {
        if (CollectionUtils.isEmpty(inactiveUserNames) || usernameToPhone.isEmpty()) {
            return Collections.emptyList();
        }
        return inactiveUserNames.stream()
            .map(usernameToPhone::get)
            .filter(StringUtils::isNotBlank)
            .distinct()
            .collect(Collectors.toList());
    }

}
