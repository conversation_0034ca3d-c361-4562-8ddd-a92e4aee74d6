package com.qmqb.imp.system.domain.bo.performance;

import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 绩效反馈主表与绩效指标结果中间表Bo
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PerformanceFeedbackMainIndicatorResultBo extends BaseEntity {

    /** 主键ID */
    private Long id;

    /** 绩效反馈主表ID */
    private Long mainId;

    /** 绩效指标结果ID */
    private Long indicatorResultId;

    /** 删除标志（0正常 1删除） */
    private Integer delFlag;
} 