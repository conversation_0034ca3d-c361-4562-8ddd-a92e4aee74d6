CREATE TABLE `tb_performance_event` (
                                        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                        `feedback_code` varchar(50) NOT NULL COMMENT '反馈编码',
                                        `year` int(4) NOT NULL COMMENT '年份',
                                        `month` int(2) NOT NULL COMMENT '月份',
                                        `feedback_time` datetime NOT NULL COMMENT '反馈时间',
                                        `event_title` varchar(200) DEFAULT NULL COMMENT '事件标题',
                                        `event_detail` text COMMENT '事件明细',
                                        `event_start_time` datetime DEFAULT NULL COMMENT '事件发生时间-开始',
                                        `event_end_time` datetime DEFAULT NULL COMMENT '事件发生时间-结束',
                                        `data_source` varchar(20) DEFAULT NULL COMMENT '数据来源',
                                        `submit_status` varchar(20) DEFAULT NULL COMMENT '提交状态',
                                        `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
                                        `submitter` varchar(100) DEFAULT NULL COMMENT '提交人',
                                        `project_manager_audit_status` varchar(20) DEFAULT NULL COMMENT '项管审核状态',
                                        `project_manager_auditor` varchar(100) DEFAULT NULL COMMENT '项管审核人',
                                        `final_audit` varchar(20) DEFAULT NULL COMMENT '最终审核状态',
                                        `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                        `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
                                        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                        `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
                                        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                        PRIMARY KEY (`id`),
                                        UNIQUE KEY `uk_feedback_code` (`feedback_code`),
                                        KEY `idx_data_source` (`data_source`)
) ENGINE=InnoDB AUTO_INCREMENT=1948669548687826946 DEFAULT CHARSET=utf8mb4 COMMENT='绩效事件表';

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1941324689187315722, '确认并提交', 1939932590323122177, 10, '#', '', NULL, 1, 0, 'F', '0', '0', 'system:performanceFeedback:edit', '#', 'admin', '2025-07-05 10:57:13', '', NULL, '');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1947482610177937408, '绩效事件管理', 1600327964042485761, 12, 'FeedbackManagement', 'business/feedbackManagement/index', NULL, 1, 0, 'C', '0', '0', 'system:performanceEvent:list', 'log', 'admin', '2025-07-23 09:54:02', 'admin', '2025-07-23 10:36:51', '绩效事件挂历菜单');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1947482610177937409, '查询', 1947482610177937408, 1, '#', '', NULL, 1, 0, 'F', '0', '0', 'system:performanceEvent:query', '#', 'admin', '2025-07-23 09:54:02', '', NULL, '');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1947482610177937410, '新增', 1947482610177937408, 2, '#', '', NULL, 1, 0, 'F', '0', '0', 'system:performanceEvent:add', '#', 'admin', '2025-07-23 09:54:02', '', NULL, '');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1947482610177937411, '修改', 1947482610177937408, 3, '#', '', NULL, 1, 0, 'F', '0', '0', 'system:performanceEvent:edit', '#', 'admin', '2025-07-23 09:54:02', '', NULL, '');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1947482610177937412, '删除', 1947482610177937408, 4, '#', '', NULL, 1, 0, 'F', '0', '0', 'system:performanceEvent:remove', '#', 'admin', '2025-07-23 09:54:02', '', NULL, '');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1947482610177937413, '批量提交', 1947482610177937408, 5, '#', '', NULL, 1, 0, 'F', '0', '0', 'system:performanceEvent:batchSubmit', '#', 'admin', '2025-07-23 09:54:02', '', NULL, '');

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1948570951451533313, 'AI绩效报告查询', 1600327964042485761, 14, 'AIPerformanceReport', 'business/AIPerformanceReport/index', NULL, 1, 0, 'C', '0', '0', 'system:performanceReport:list', 'chart', 'admin', '2025-07-25 10:28:12', 'admin', '2025-07-25 14:22:25', '');

ALTER TABLE`tb_performance_feedback_main`
DROP INDEX `uk_feedback_code`;
