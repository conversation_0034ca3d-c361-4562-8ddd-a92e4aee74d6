package com.qmqb.imp.job.indicator;

import com.qmqb.imp.common.constant.PerformanceFeedbackConstants;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.*;
import com.qmqb.imp.common.utils.PerformanceFeedbackUtils;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.performance.PerformanceFeedbackMain;
import com.qmqb.imp.system.service.IUserKqStatService;
import com.qmqb.imp.system.service.impl.performance.PerformanceFeedbackCodeGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Date;

/**
 * 绩效反馈主表生成器
 * 根据员工信息和指标计算结果生成主反馈记录
 *
 * <AUTHOR>
 */
@Component
public class PerformanceFeedbackMainGenerator {

    @Autowired
    private PerformanceFeedbackCodeGenerator codeGenerator;

    @Autowired
    private IUserKqStatService iUserKqStatService;

    /**
     * 创建主反馈记录
     *
     * @param nickName      员工昵称
     * @param year          年份
     * @param month         月份
     * @param personType    角色类型
     * @param indicatorCode 指标编码
     * @param calcResult    指标计算结果
     * @return 主反馈记录
     */
    public PerformanceFeedbackMain createMainFeedbackRecord(String nickName, Integer year, Integer month,
                                                            String personType, String indicatorCode,
                                                            IndicatorLevelCalcService.IndicatorCalcResult calcResult, SysUser sysUser) {
        PerformanceFeedbackMain mainFeedback = new PerformanceFeedbackMain();

        Boolean userLeaveOverWeek = iUserKqStatService.userLeaveOverWeek(year, month, nickName);

        // 生成反馈编码
        mainFeedback.setFeedbackCode(codeGenerator.generateFeedbackCode());

        // 设置基本信息
        mainFeedback.setYear(year);
        mainFeedback.setMonth(month);
        mainFeedback.setFeedbackTime(new Date());

        // 设置指标信息
        IndicatorCategoryEnum category = IndicatorCategoryEnum.getByIndicatorCode(indicatorCode);
        if (category != null) {
            mainFeedback.setPrimaryIndicator(category.getCode());
        }

        String indicatorName = PerformanceFeedbackUtils.getIndicatorName(indicatorCode);
        mainFeedback.setSecondaryIndicator(indicatorCode);

        // 设置事件信息
        if (calcResult != null) {
            // 这里需要根据实际的calcResult类型来获取相关信息
            // 暂时使用默认值，实际使用时需要根据calcResult的具体结构来设置
            mainFeedback.setEventTitle(PerformanceFeedbackUtils.generateEventTitle(indicatorName, calcResult.getLevel()));
            mainFeedback.setEventDetail(calcResult.getLogContent());
            // 取上个月的开始和结束时间
            LocalDate firstDayOfMonth = LocalDate.of(year, month, 1);
            LocalDate lastDayOfMonth = firstDayOfMonth.withDayOfMonth(firstDayOfMonth.lengthOfMonth());

            Date start = java.sql.Date.valueOf(firstDayOfMonth);
            Date end = java.sql.Timestamp.valueOf(lastDayOfMonth.atTime(23, 59, 59));

            mainFeedback.setEventStartTime(start);
            mainFeedback.setEventEndTime(end);
        }
        PerformanceIndicatorEnum indicatorEnum = PerformanceIndicatorEnum.fromCode(indicatorCode);

        // 设置数据来源
        mainFeedback.setDataSource(PerformanceFeedbackDataSourceEnum.SYSTEM_GENERATED.getCode());

        // 设置提交状态（初始状态）
        mainFeedback.setSubmitStatus(PerformanceFeedbackSubmitStatusEnum.SUBMITTED.getCode());
        mainFeedback.setSubmitTime(new Date());
        mainFeedback.setSubmitter(PerformanceFeedbackConstants.SYSTEM_SUBMITTER);

        // 设置审核状态（初始状态）
        mainFeedback.setProjectManagerAuditStatus(PerformanceFeedbackAuditStatusEnum.NOT_AUDITED.getCode());
        if (PerformanceIndicatorEnum.PROJECT_RELEASE_COUNT.getCode().equals(indicatorCode)) {
            mainFeedback.setProjectManagerAuditor(sysUser.getUserName());
        }
        boolean approved = (indicatorEnum != null && !indicatorEnum.isNeedProjectManagementReview()) || sysUser.isTechnicalManager();
        if (approved){
            mainFeedback.setProjectManagerAuditStatus(PerformanceFeedbackAuditStatusEnum.APPROVED.getCode());
            mainFeedback.setProjectManagerAuditTime(new Date());
            mainFeedback.setProjectManagerAuditor(PerformanceFeedbackConstants.SYSTEM_SUBMITTER);
        }
        mainFeedback.setFinalAudit(PerformanceFeedbackAuditStatusEnum.NOT_AUDITED.getCode());
        if (userLeaveOverWeek) {
            // 如果请假超过一周添加备注
            mainFeedback.setRemark("员工请假超过一周，绩效反馈仅供参考。");
        }

        return mainFeedback;
    }
}
