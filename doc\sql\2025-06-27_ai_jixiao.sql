DROP TABLE IF EXISTS `tb_performance_report`;
CREATE TABLE `tb_performance_report` (
    `id` bigint(20) NOT NULL COMMENT 'id',
    `year` varchar(20) NOT NULL COMMENT '年份',
    `month` varchar(20) NOT NULL COMMENT '月份',
    `report_type` char(2) DEFAULT NULL COMMENT '报告类型 （0-个人 1-小组 2-岗位）',
    `user_name` varchar(20) DEFAULT NULL COMMENT '个人名称',
    `group_name` varchar(30) DEFAULT NULL COMMENT '小组名称',
    `post_name` varchar(30) DEFAULT NULL COMMENT '岗位名称',
    `report_url` varchar(255) DEFAULT NULL COMMENT '报告地址',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='绩效分析报告';
