package com.qmqb.imp.system.domain.bo;

import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 数据库权限业务对象 tb_data_base_permission
 *
 * <AUTHOR>
 * @date 2025-06-26
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class DataBasePermissionQueryBo extends BaseEntity {


    /**
     * 数据库名
     */

    private String dbName;

    /**
     * 组id
     */
    private String groupId;


    /**
     * 成员名称
     */
    private String memberName;


}
