package com.qmqb.imp.system.domain.performance;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 绩效指标对象 tb_performance_indicator
 *
 * <AUTHOR>
 * @date 2025-06-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_performance_indicator")
public class PerformanceIndicator extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 绩效主表ID
     */
    private Long performanceId;
    /**
     * 指标编码
     */
    private String indicatorCode;
    /**
     * 指标名称
     */
    private String indicatorName;
    /**
     * 指标分类编码
     */
    private String categoryCode;
    /**
     * 评价等级S/A/B/C/D
     */
    private String scoreLevel;
    /**
     * 日志登记内容
     */
    private String logContent;

}
