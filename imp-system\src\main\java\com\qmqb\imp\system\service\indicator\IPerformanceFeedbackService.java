package com.qmqb.imp.system.service.indicator;

import com.baomidou.mybatisplus.extension.service.IService;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackBo;
import com.qmqb.imp.system.domain.performance.PerformanceFeedback;
import com.qmqb.imp.system.domain.performance.PerformanceFeedbackMain;
import com.qmqb.imp.system.domain.vo.performance.PerformanceFeedbackVo;

import java.util.Collection;
import java.util.List;

/**
 * 绩效反馈Service接口
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IPerformanceFeedbackService extends IService<PerformanceFeedback> {

    /**
     * 查询绩效反馈
     *
     * @param id 绩效反馈ID
     * @return 绩效反馈信息
     */
    PerformanceFeedbackVo queryById(Long id);

    /**
     * 查询绩效反馈列表（分页）
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 绩效反馈分页列表
     */
    TableDataInfo<PerformanceFeedbackVo> queryPageList(PerformanceFeedbackBo bo, PageQuery pageQuery);

    /**
     * 查询绩效反馈列表
     *
     * @param bo 查询条件
     * @return 绩效反馈列表
     */
    List<PerformanceFeedbackVo> queryList(PerformanceFeedbackBo bo);

    /**
     * 查询绩效通过的反馈列表
     *
     * @param bo 查询条件
     * @return 绩效反馈列表
     */
    List<PerformanceFeedback> queryApprovalList(PerformanceFeedbackBo bo);


    /**
     * 新增绩效反馈
     *
     * @param bo 新增对象
     * @return 是否成功
     */
    Boolean insertByBo(PerformanceFeedbackBo bo);

    /**
     * 修改绩效反馈
     *
     * @param bo 修改对象
     * @return 是否成功
     */
    Boolean updateByBo(PerformanceFeedbackBo bo);

    /**
     * 校验并批量删除绩效反馈信息
     *
     * @param ids     待删除ID集合
     * @param isValid 是否校验
     * @return 是否成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据员工昵称、年份、月份删除绩效反馈记录
     *
     * @param nickName 员工昵称
     * @param year     年份
     * @param month    月份
     */
    void removeByNickNameAndMonth(String nickName, Integer year, Integer month);

    /**
     * 根据mainids删除记录
     *
     * @param mainIds 主表ID列表
     */
    void removeByMainIds(List<String> mainIds);

    /**
     * 根据年份、月份删除绩效反馈记录
     *
     * @param year  年份
     * @param month 月份
     */
    void removeByYearAndMonth(Integer year, Integer month);

    /**
     * 根据年份、月份查询绩效反馈记录
     *
     * @param year  年份
     * @param month 月份
     * @return 绩效反馈记录列表
     */
    List<PerformanceFeedback> getByYearAndMonth(Integer year, Integer month);

    /**
     * 根据员工昵称、年份、月份查询绩效反馈记录
     *
     * @param nickName 员工昵称
     * @param year     年份
     * @param month    月份
     * @return 绩效反馈记录列表
     */
    List<PerformanceFeedback> getByNickNameAndYearMonth(String nickName, Integer year, Integer month);

    /**
     * 批量保存绩效反馈记录
     *
     * @param feedbackList 反馈记录列表
     */
    void saveBatch(List<PerformanceFeedback> feedbackList);

    /**
     * 根据日期前缀查询最大反馈编码
     *
     * @param datePrefix 日期前缀，格式：FKyyyyMMdd
     * @return 最大反馈编码，如果没有记录则返回null
     */
    String getMaxFeedbackCodeByDatePrefix(String datePrefix);

    /**
     * 根据主表编码查询最大明细反馈编码
     *
     * @param mainFeedbackCode 主表编码
     * @return 最大明细反馈编码，如果没有记录则返回null
     */
    String getMaxFeedbackCodeByMainFeedbackCode(String mainFeedbackCode);

    /**
     * 根据主表ID集合批量删除绩效反馈明细
     *
     * @param mainFeedbackIds 主表ID集合
     * @return 删除条数
     */
    int deleteByMainFeedbackIds(List<Long> mainFeedbackIds);


    /**
     * 获取已审核的反馈记录
     *
     * @param nickName
     * @param year
     * @param month
     * @param secondaryIndicator
     * @return
     */
    List<PerformanceFeedback> getMainBySecondaryIndicator(String nickName, Integer year, Integer month, String secondaryIndicator);

    /**
     * 批量取消绩效反馈
     *
     * @param ids        反馈ID集合
     * @param reason     取消原因
     * @param operatorId 操作人ID
     * @return 受影响条数
     */
    int batchCancelByIds(List<Long> ids, String reason, Long operatorId);
}
