package com.qmqb.imp.common.constant;

/**
 * 绩效反馈相关常量
 * <p>
 * 定义了绩效反馈系统中使用的各种常量值，包括：
 * - 系统提交人标识
 * - 反馈编码前缀
 * - 默认值
 * - 其他业务常量
 * </p>
 *
 * <AUTHOR>
 */
public interface PerformanceFeedbackConstants {

    /**
     * 系统提交人标识
     */
    String SYSTEM_SUBMITTER = "系统";

    /**
     * 反馈编码前缀
     */
    String FEEDBACK_CODE_PREFIX = "FK";

    /**
     * 反馈编码日期格式
     */
    String FEEDBACK_CODE_DATE_FORMAT = "yyyyMMdd";

    /**
     * 反馈编码流水号格式
     */
    String FEEDBACK_CODE_SEQUENCE_FORMAT = "%06d";

    /**
     * 未知指标名称
     */
    String UNKNOWN_INDICATOR_NAME = "未知指标";

    /**
     * 事件标题格式
     */
    String EVENT_TITLE_FORMAT = "%s绩效评估 - %s级";

    /**
     * 推荐原因格式
     */
    String RECOMMENDED_REASON_FORMAT = "基于%s指标评估，员工表现被评为%s级。";

    /**
     * 推荐原因详情格式
     */
    String RECOMMENDED_REASON_DETAIL_FORMAT = "具体原因：%s";

    /**
     * 默认流水号起始值
     */
    int DEFAULT_SEQUENCE_START = 1;

    /**
     * 最大流水号（每天最多9999个反馈记录）
     */
    int MAX_SEQUENCE_NUMBER = 999999;
}
