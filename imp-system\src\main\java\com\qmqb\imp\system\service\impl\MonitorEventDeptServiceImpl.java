package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.qmqb.imp.system.domain.bo.MonitorEventDeptBo;
import com.qmqb.imp.system.domain.vo.MonitorEventDeptVo;
import com.qmqb.imp.system.domain.MonitorEventDept;
import com.qmqb.imp.system.mapper.MonitorEventDeptMapper;
import com.qmqb.imp.system.service.IMonitorEventDeptService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 预警事件和部门关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@RequiredArgsConstructor
@Service
public class MonitorEventDeptServiceImpl implements IMonitorEventDeptService {

    private final MonitorEventDeptMapper baseMapper;

    /**
     * 查询预警事件和部门关联
     */
    @Override
    public MonitorEventDeptVo queryById(Long eventId){
        return baseMapper.selectVoById(eventId);
    }

    /**
     * 查询预警事件和部门关联列表
     */
    @Override
    public TableDataInfo<MonitorEventDeptVo> queryPageList(MonitorEventDeptBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MonitorEventDept> lqw = buildQueryWrapper(bo);
        Page<MonitorEventDeptVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询预警事件和部门关联列表
     */
    @Override
    public List<MonitorEventDeptVo> queryList(MonitorEventDeptBo bo) {
        LambdaQueryWrapper<MonitorEventDept> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MonitorEventDept> buildQueryWrapper(MonitorEventDeptBo bo) {
        LambdaQueryWrapper<MonitorEventDept> lqw = Wrappers.lambdaQuery();
        return lqw;
    }

    /**
     * 新增预警事件和部门关联
     */
    @Override
    public Boolean insertByBo(MonitorEventDeptBo bo) {
        MonitorEventDept add = BeanUtil.toBean(bo, MonitorEventDept.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setEventId(add.getEventId());
        }
        return flag;
    }

    /**
     * 修改预警事件和部门关联
     */
    @Override
    public Boolean updateByBo(MonitorEventDeptBo bo) {
        MonitorEventDept update = BeanUtil.toBean(bo, MonitorEventDept.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MonitorEventDept entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除预警事件和部门关联
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
