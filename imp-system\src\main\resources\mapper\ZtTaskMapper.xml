<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.ZtTaskMapper">

    <resultMap type="com.qmqb.imp.system.domain.ZtTask" id="ZtTaskResult">
        <result property="id" column="id"/>
        <result property="project" column="project"/>
        <result property="parent" column="parent"/>
        <result property="execution" column="execution"/>
        <result property="module" column="module"/>
        <result property="design" column="design"/>
        <result property="story" column="story"/>
        <result property="storyVersion" column="storyVersion"/>
        <result property="designVersion" column="designVersion"/>
        <result property="fromBug" column="fromBug"/>
        <result property="feedback" column="feedback"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="pri" column="pri"/>
        <result property="estimate" column="estimate"/>
        <result property="consumed" column="consumed"/>
        <result property="left" column="left"/>
        <result property="deadline" column="deadline"/>
        <result property="status" column="status"/>
        <result property="subStatus" column="subStatus"/>
        <result property="color" column="color"/>
        <result property="mailto" column="mailto"/>
        <result property="desc" column="desc"/>
        <result property="version" column="version"/>
        <result property="openedBy" column="openedBy"/>
        <result property="openedDate" column="openedDate"/>
        <result property="assignedTo" column="assignedTo"/>
        <result property="assignedDate" column="assignedDate"/>
        <result property="estStarted" column="estStarted"/>
        <result property="realStarted" column="realStarted"/>
        <result property="finishedBy" column="finishedBy"/>
        <result property="finishedDate" column="finishedDate"/>
        <result property="finishedList" column="finishedList"/>
        <result property="canceledBy" column="canceledBy"/>
        <result property="canceledDate" column="canceledDate"/>
        <result property="closedBy" column="closedBy"/>
        <result property="closedDate" column="closedDate"/>
        <result property="realDuration" column="realDuration"/>
        <result property="planDuration" column="planDuration"/>
        <result property="closedReason" column="closedReason"/>
        <result property="lastEditedBy" column="lastEditedBy"/>
        <result property="lastEditedDate" column="lastEditedDate"/>
        <result property="activatedDate" column="activatedDate"/>
        <result property="deleted" column="deleted"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id, t.project, t.parent, t.execution, t.module, t.design, t.story, t.storyVersion, t.designVersion, t.fromBug,
        t.feedback, t.name,t.name as sonName, t.type, t.pri, t.estimate, t.left, t.deadline, t.status, t.subStatus, t.color, t.mailto, t.desc,
        t.version, t.openedBy, t.openedDate, t.assignedDate, t.estStarted, ztAction.startDate as realStarted, t.finishedDate,
        t.finishedList, t.canceledBy, t.canceledDate, t.closedDate, t.realDuration, t.planDuration,
        t.closedReason, t.lastEditedBy, t.lastEditedDate, t.activatedDate, t.deleted,
        p.name as pName, y.title as title, finishedBy.realname as finishedBy,taskStarter.realname as taskStarter, closedBy.realname as closedBy
    </sql>

    <select id="getLists" resultType="com.qmqb.imp.system.domain.vo.TaskVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM zt_task t
        LEFT JOIN zt_story y ON t.story = y.id
        LEFT JOIN zt_project p ON t.execution = p.id
        LEFT JOIN (SELECT objectID, max(date) as startDate, actor from zt_action where action = 'started' and objectType = 'task' GROUP BY objectID) ztAction ON t.id = ztAction.objectID
        LEFT JOIN zt_user finishedBy
        ON t.finishedBy = finishedBy.account
        LEFT JOIN zt_user taskStarter
        ON ztAction.actor = taskStarter.account
        LEFT JOIN zt_user closedBy
        ON t.closedBy = closedBy.account
        <where>
            t.isParent = 0
            <if test="request.createYear !=null and request.createYear !=''">
                AND YEAR(t.openedDate) = #{request.createYear}
            </if>
            <if test="request.createMonth !=null and request.createMonth !=''">
                AND MONTH(t.openedDate) = #{request.createMonth}
            </if>
            <if test="request.finishYear !=null and request.finishYear !=''">
                AND YEAR(t.finishedDate) = #{request.finishYear}
            </if>

            <if test="request.finishMonth !=null and request.finishMonth !=''">
                AND MONTH(t.finishedDate) = #{request.finishMonth}
            </if>
            <if test="request.completedBy !=null and request.completedBy !=''">
                AND finishedBy.realname LIKE CONCAT ('%',#{request.completedBy},'%')
            </if>
            <if test="request.taskStarter !=null and request.taskStarter !=''">
                AND taskStarter.realname LIKE CONCAT ('%',#{request.taskStarter},'%')
            </if>
            <if test="request.status !=null">
                <if test="request.status != 'all'">
                    AND t.status = #{request.status}
                </if>
            </if>
            <if test="request.userNameList !=null">
                AND t.finishedBy IN
                <foreach collection="request.userNameList" item="userName" open="(" close=")" separator=",">
                    #{userName}
                </foreach>
            </if>
        </where>
        <choose>
            <when test="request.orderByColumn !=null and request.orderByColumn !='' and request.orderByColumn != 'consumed' and request.orderByColumn != 'finishedDate'">
                ORDER BY ${request.orderByColumn} ${request.sort}
            </when>
            <otherwise>
                order by realStarted desc, name desc
            </otherwise>
        </choose>
    </select>


    <!-- 通过分页插件的方式获取任务数据列表 -->
    <select id="getListsByPage" resultType="com.qmqb.imp.system.domain.vo.TaskVO">
        SELECT t.id, t.project, t.parent, t.execution, t.module, t.design, t.story, t.name,t.name as sonName, t.type, t.status, t.closedBy, y.title as title,
        t.subStatus, t.assignedDate, t.closedDate, t.closedReason, t.activatedDate, t.deleted, finishedBy.realname as finishedBy,
        taskStarter.realname as taskStarter, ztAction.startDate as realStarted, IFNULL(ztAction2.finishedDate,t.finishedDate) as finishedDate,
        round(TIMESTAMPDIFF(minute,ztAction.startDate,IFNULL(ztAction2.finishedDate,t.finishedDate)) / 60, 2) as consumed
        FROM zt_task t
        LEFT JOIN (SELECT objectID, max(date) as startDate, actor from zt_action where action = 'started' and objectType = 'task' GROUP BY objectID) ztAction ON t.id = ztAction.objectID
        LEFT JOIN (SELECT objectID, max(date) as finishedDate, actor from zt_action where action = 'finished' and objectType = 'task' GROUP BY objectID) ztAction2 ON t.id = ztAction2.objectID
        LEFT JOIN zt_story y ON t.story = y.id
        LEFT JOIN zt_user finishedBy ON t.finishedBy = finishedBy.account
        LEFT JOIN zt_user taskStarter ON ztAction.actor = taskStarter.account
        LEFT JOIN zt_project project ON project.id = t.execution
        <where>
            t.isParent = 0
            AND t.deleted = '0' AND project.deleted = '0'
            <if test="request.createYear !=null and request.createYear !=''">
                AND YEAR(t.openedDate) = #{request.createYear}
            </if>
            <if test="request.createMonth !=null and request.createMonth !=''">
                AND MONTH(t.openedDate) = #{request.createMonth}
            </if>
            <if test="request.finishYear !=null and request.finishYear !=''">
                AND YEAR(t.finishedDate) = #{request.finishYear}
            </if>
            <if test="request.finishMonth !=null and request.finishMonth !=''">
                AND MONTH(t.finishedDate) = #{request.finishMonth}
            </if>
            <if test="request.completedBy !=null and request.completedBy !=''">
                AND finishedBy.realname LIKE CONCAT ('%',#{request.completedBy},'%')
            </if>
            <if test="request.taskStarter !=null and request.taskStarter !=''">
                AND taskStarter.realname LIKE CONCAT ('%',#{request.taskStarter},'%')
            </if>
            <if test="request.status !=null">
                <if test="request.status != 'all'">
                    AND t.status = #{request.status}
                </if>
            </if>
            <if test="request.userNameList !=null">
                AND (
                (t.status = 'done' AND t.finishedBy IN
                <foreach collection="request.userNameList" item="userName" open="(" close=")" separator=",">
                    #{userName}
                </foreach>)
                OR
                ((t.status = 'doing' or t.status = 'pause' or t.status = 'cancel' or t.status = 'wait') AND t.assignedTo IN
                <foreach collection="request.userNameList" item="userName" open="(" close=")" separator=",">
                    #{userName}
                </foreach>)
                OR
                (
                (t.status = 'closed' and ztAction.actor is not null and ztAction.actor IN
                <foreach collection="request.userNameList" item="userName" open="(" close=")" separator=",">
                    #{userName}
                </foreach>)
                or
                (t.status = 'closed' and ztAction.actor is null and t.openedBy IN
                <foreach collection="request.userNameList" item="userName" open="(" close=")" separator=",">
                    #{userName}
                </foreach>)
                )
                )
            </if>
        </where>
        <choose>
            <when test="request.orderByColumn !=null and request.orderByColumn !=''">
                ORDER BY ${request.orderByColumn} ${request.sort}
            </when>
            <otherwise>
                order by realStarted desc, name desc
            </otherwise>
        </choose>
    </select>

    <!-- 根据用户拼音名小写列表以及开始和结束时间获取用户的任务个数列表 -->
    <select id="getTaskListByTimeAndUser" resultType="com.qmqb.imp.system.domain.vo.ZtTaskCountVO">
        SELECT finishedBy AS userName, COUNT(*) AS taskCount
        FROM zt_task
        WHERE finishedBy in
        <foreach collection="userNameList" item="userName" open="(" close=")" separator=",">
            #{userName}
        </foreach>
        AND finishedDate BETWEEN #{beginTime} AND #{endTime}
        GROUP BY finishedBy
    </select>


    <!-- 根据月份和组别查询任务列表 -->
    <select id="getTaskListByGroupAndMonth" resultType="com.qmqb.imp.system.domain.vo.TaskInfoVO">
        SELECT `id` AS taskId,(CASE finishedBy WHEN '' THEN assignedTo ELSE finishedBy END) AS userName, finishedDate, `name` AS taskName, status, pri as taskPrimary, realStarted, closedDate, finishedDate
        FROM zt_task
        WHERE (assignedTo != 'closed' OR finishedDate BETWEEN #{beginTime} AND #{endTime})
        AND
        (assignedTo IN
        <foreach collection="userNameList" item="userName" open="(" close=")" separator=",">
            #{userName}
        </foreach>
        OR finishedBy IN
        <foreach collection="userNameList" item="userName" open="(" close=")" separator=",">
            #{userName}
        </foreach>
        )
        AND (CASE finishedBy WHEN '' THEN assignedTo ELSE finishedBy END) IN
        <foreach collection="userNameList" item="userName" open="(" close=")" separator=",">
            #{userName}
        </foreach>
        AND openedDate BETWEEN #{beginTime} AND #{endTime}
        <if test="status != null and status != 'all'">
            AND status = #{status}
        </if>
        <if test="name != null and name != ''">
            AND name  LIKE CONCAT ('%',#{name},'%')
        </if>
        AND deleted = '0'
    </select>

    <select id="listByIds" resultType="com.qmqb.imp.system.domain.vo.ZtTaskVo">
        select * from zt_task where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="queryDoingTaskByStartedTime" resultMap="ZtTaskResult">
        SELECT
        t1.`id`, t1.`project`, t1.`parent`, t1.`execution`, t1.`module`, t1.`design`, t1.`story`, t1.`storyVersion`,
        t1.`designVersion`, t1.`fromBug`, t1.`feedback`, t1.`name`, t1.`type`, t1.`pri`, t1.`estimate`, t1.`consumed`,
        t1.`left`, t1.`deadline`, t1.`status`, t1.`subStatus`, t1.`color`, t1.`mailto`, t1.`desc`, t1.`version`,
        t1.`openedBy`, t1.`openedDate`, t1.`assignedTo`, t1.`assignedDate`, t1.`estStarted`, t1.`realStarted`,
        t1.`finishedBy`, t1.`finishedDate`, t1.`finishedList`, t1.`canceledBy`, t1.`canceledDate`, t1.`closedBy`,
        t1.`closedDate`, t1.`realDuration`, t1.`planDuration`, t1.`closedReason`, t1.`lastEditedBy`,
        t1.`lastEditedDate`, t1.`activatedDate`, t1.`deleted`
        FROM
        `zt_task` t1
        LEFT JOIN `zt_action` t2 ON t1.id = t2.objectID
        AND t1.assignedTo = t2.actor
        WHERE
        t1.assignedTo IN
        <foreach collection="usernames" item="username" open="(" close=")" separator=",">
            #{username}
        </foreach>
        AND t1.`status` = 'doing'
        AND t2.action = 'started'
        AND ( t2.`date` BETWEEN #{beginTime} AND #{endTime} );
    </select>

    <select id="getTechnologyTaskCounts" resultType="java.util.Map">
        SELECT t.finishedBy,t.assignedTo,ztAction.actor as startBy,t.openedBy,t.`status`,
               COUNT(1) AS taskCount,
               LPAD(MONTH(t.openedDate), 2, '0') AS month,
               YEAR(t.openedDate) AS year
        FROM zt_task t
        LEFT JOIN (
            SELECT objectID,  actor
            from zt_action where action = 'started' and objectType = 'task'
            GROUP BY objectID)
        ztAction ON t.id = ztAction.objectID
        WHERE t.deleted = '0' and t.isParent = 0
        <if test="yearList != null">
            AND YEAR(t.openedDate) IN
            <foreach collection="yearList" item="year" open="(" close=")" separator=",">
                #{year}
            </foreach>
        </if>
        <if test="userNameList != null">
            AND
        (
            (t.status = 'done' AND
            t.finishedBy IN
            <foreach collection="userNameList" item="userName" open="(" close=")" separator=",">
                #{userName}
            </foreach>)
            OR
            ((t.status = 'doing' or t.status = 'pause' or t.status = 'cancel' or t.status = 'wait')
            and t.assignedTo IN
            <foreach collection="userNameList" item="userName" open="(" close=")" separator=",">
                #{userName}
            </foreach>)
            OR
            (
            ( t.status = 'closed' and ztAction.actor is not null
            and ztAction.actor IN
            <foreach collection="userNameList" item="userName" open="(" close=")" separator=",">
                #{userName}
            </foreach>)
            OR
            (t.status = 'closed' and ztAction.actor is null
            and t.openedBy IN
            <foreach collection="userNameList" item="userName" open="(" close=")" separator=",">
                #{userName}
            </foreach>)
            )
        )
        </if>
        GROUP BY
        t.finishedBy,t.assignedTo,ztAction.actor,t.openedBy,
        LPAD(MONTH(t.openedDate), 2, '0'),
        YEAR(t.openedDate)
    </select>

    <select id="selectTaskCountByStatus" resultType="java.lang.Long">
        select count(1) from zt_task t
        LEFT JOIN (SELECT objectID, actor from zt_action where action = 'started' and objectType = 'task' GROUP BY
        objectID) ztAction ON t.id = ztAction.objectID
        where t.status = #{status} and t.openedDate between #{beginTime} and #{endTime} and t.isParent = 0 AND t.deleted ='0'
        <if test="userNameList !=null">
            AND (
            (t.status = 'done' AND t.finishedBy IN
            <foreach collection="userNameList" item="userName" open="(" close=")" separator=",">
                #{userName}
            </foreach>)
            OR
            ((t.status = 'doing' or t.status = 'pause' or t.status = 'cancel' or t.status = 'wait') AND t.assignedTo IN
            <foreach collection="userNameList" item="userName" open="(" close=")" separator=",">
                #{userName}
            </foreach>)
            OR
            (
            (t.status = 'closed' and ztAction.actor is not null and ztAction.actor IN
            <foreach collection="userNameList" item="userName" open="(" close=")" separator=",">
                #{userName}
            </foreach>)
            or
            (t.status = 'closed' and ztAction.actor is null and t.openedBy IN
            <foreach collection="userNameList" item="userName" open="(" close=")" separator=",">
                #{userName}
            </foreach>)
            )
            )
        </if>
    </select>

    <select id="queryUserTask" resultType="com.qmqb.imp.system.domain.vo.TaskVO">
        select t.id,t.status, t.type, t.name as sonName, t.finishedby,
               t.closedby, t.closedDate, t.parent, t.closedReason,t.activatedDate,
               y.title as title, p.name as pName,
               startAction.startDate as realStarted, startAction.actor as taskStarter,
        IFNULL(finishAction.finishedDate,t.finishedDate) as finishedDate, finishAction.actor as finishedby,
               round(TIMESTAMPDIFF(minute,startAction.startDate,IFNULL(finishAction.finishedDate,t.finishedDate)) / 60, 2) as consumed
        from  zt_task t
        LEFT JOIN zt_story y ON t.story = y.id
        LEFT JOIN  zt_project p ON t.project = p.id
        LEFT JOIN (SELECT objectID,max(date) as startDate, actor from zt_action
                          where action = 'started' and objectType = 'task' GROUP BY objectID) startAction ON t.id = startAction.objectID
        LEFT JOIN (SELECT objectID, max(date) as finishedDate, actor from zt_action
                          where action = 'finished' and objectType = 'task' GROUP BY objectID) finishAction ON t.id = finishAction.objectID
        where t.deleted = '0' and t.isParent = 0
        and ( startAction.actor = #{workDetail.ztUserName}
        OR t.finishedby = #{workDetail.ztUserName} )
        AND (
                (
                YEAR(t.openedDate) = #{workDetail.evalYear}
                <if test="workDetail.evalMonth != null and workDetail.evalMonth != -1 ">
                AND MONTH(t.openedDate) = #{workDetail.evalMonth}
                </if>
                )
        OR
                (
                YEAR(t.finishedDate) = #{workDetail.evalYear}
                <if test="workDetail.evalMonth != null and workDetail.evalMonth != -1 ">
                AND MONTH(t.finishedDate) = #{workDetail.evalMonth}
                </if>
                )
            )
        <if test="workDetail.orderByField !=null and workDetail.orderByField !='' and workDetail.orderRule !=null and workDetail.orderRule !=''">
        ORDER BY
        <choose>
            <when test="workDetail.orderByField == 'finishedDate'">
                t.finishedDate ${workDetail.orderRule}
            </when>
            <when test="workDetail.orderByField == 'finishedby'">
                t.finishedby ${workDetail.orderRule}
            </when>
            <when test="workDetail.orderByField == 'name'">
                t.name ${workDetail.orderRule}
            </when>
            <otherwise>
                ${workDetail.orderByField} ${workDetail.orderRule}
            </otherwise>
        </choose>
        </if>
    </select>
    <select id="doingTaskTimeout" resultType="com.qmqb.imp.system.domain.vo.TaskVO">
        select t.id, startAction.actor as taskStarter
        from zt_task t
        left join (select objectID,max(date) as startDate, actor from zt_action
                   where action = 'started' and objectType = 'task' GROUP BY objectID) startAction ON t.id = startAction.objectID
        where t.deleted = '0'
          and t.isParent = 0
          and t.status = 'doing'
          <if test="symbol != null and symbol != '' and compareValue != null">
               and TIMESTAMPDIFF(SECOND, startAction.startDate, NOW()) > (${compareValue} * 24 * 60 * 60)
          </if>
    </select>
    <select id="selectPauseTimeoutCount" resultType="java.lang.Long">
        SELECT count(1)
        FROM zt_task task
        INNER JOIN (
        SELECT objectID, MAX(date) AS last_paused_date
        FROM zt_action
        WHERE objectType = 'task'
        AND action = 'paused'
        GROUP BY objectID
        ) last_action ON task.id = last_action.objectID
        WHERE task.status = 'pause' and task.deleted = '0' and task.isParent = 0
        AND last_action.last_paused_date &lt; #{date} AND task.assignedTo in
        <foreach collection="usernameList" item="name" open="(" close=")" separator=",">#{name}</foreach>
    </select>

</mapper>
