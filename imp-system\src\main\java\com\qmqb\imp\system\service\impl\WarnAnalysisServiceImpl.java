package com.qmqb.imp.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzed.structure.common.util.date.DateUtil;
import com.qmqb.imp.common.core.domain.entity.SysDept;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.core.domain.vo.WarnSelectedGroupVO;
import com.qmqb.imp.common.enums.WarnHandleStatusEnum;
import com.qmqb.imp.common.enums.WarnLevelEnum;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.common.utils.LocalDateTimeUtils;
import com.qmqb.imp.system.domain.WarnRecordDetail;
import com.qmqb.imp.system.domain.bo.*;
import com.qmqb.imp.system.domain.vo.*;
import com.qmqb.imp.system.mapper.SysDeptMapper;
import com.qmqb.imp.system.mapper.SysUserMapper;
import com.qmqb.imp.system.mapper.WarnRecordDetailMapper;
import com.qmqb.imp.system.mapper.WarnRecordMapper;
import com.qmqb.imp.system.service.IWarnRecordService;
import com.qmqb.imp.system.service.WarnAnalysisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 预警serviceImpl
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WarnAnalysisServiceImpl implements WarnAnalysisService {

    private static List<String> months = Arrays.asList("01月", "02月", "03月", "04月", "05月", "06月", "07月", "08月", "09月", "10月", "11月", "12月");

    private static List<String> levels = Arrays.asList("P0", "P1", "P2");

    private final WarnRecordMapper warnRecordMapper;

    private final IWarnRecordService iWarnRecordService;

    private final SysUserMapper sysUserMapper;

    private final WarnRecordDetailMapper warnRecordDetailMapper;

    private final SysDeptMapper sysDeptMapper;

    @Override
    public DepartmentWarnAnalysisVo getDeptWarnAnalysisList(DeptWarnAnalysisBo request) {
        //默认查询一周
        if (Objects.isNull(request.getStartTime()) && Objects.isNull(request.getEndTime())) {
            request.setStartTime(DateUtil.format(LocalDateTimeUtils.last7DaysStartTime(), "yyyy-MM-dd"));
            request.setEndTime(DateUtil.format(LocalDateTimeUtils.now(), "yyyy-MM-dd"));
        }
        DepartmentWarnAnalysisVo departmentWarnAnalysisVo = new DepartmentWarnAnalysisVo();
        List<DepartmentWarnAnalysisVo.MultipleRecord> multipleRecords = new ArrayList<>();
        List<DepartmentWarnAnalysisVo.SingleRecord> singleRecords = new ArrayList<>();
        departmentWarnAnalysisVo.setMultipleRecords(multipleRecords);
        departmentWarnAnalysisVo.setSingleRecords(singleRecords);

        List<DepartmentWarnAnalysisVo.MultipleRecord> record = getMultipleRecord(request);
        if (record == null) {
            return departmentWarnAnalysisVo;
        }
        multipleRecords.addAll(record);
        List<Map<String, Object>> membersInGroupTotalWarnRecord = iWarnRecordService.countEveryOneInDepatWarnRecord(request);
        if (!CollectionUtils.isEmpty(membersInGroupTotalWarnRecord)) {
            for (Map<String, Object> map : membersInGroupTotalWarnRecord) {
                DepartmentWarnAnalysisVo.SingleRecord singleRecord = new DepartmentWarnAnalysisVo.SingleRecord();
                singleRecord.setNickName((String) map.get("nickName"));
                singleRecord.setNum(((Long) map.get("num")).intValue());
                singleRecords.add(singleRecord);
            }
        }
        Collections.sort(singleRecords);
        return departmentWarnAnalysisVo;
    }

    @Override
    public List<DepartmentWarnAnalysisVo.MultipleRecord> getMultipleRecord(DeptWarnAnalysisBo request) {
        List<DepartmentWarnAnalysisVo.MultipleRecord> multipleRecords = new ArrayList<>();
        List<SysDept> sysDepts = sysDeptMapper.selectList().stream().filter(s -> s.getParentId() == 101L).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sysDepts)) {
            return null;
        }
        List<Map<String, Object>> deptByLevelWarnRecords = iWarnRecordService.countDeptByLevelWarnRecord(request);
        for (SysDept dept : sysDepts) {
            DepartmentWarnAnalysisVo.MultipleRecord multipleRecord = new DepartmentWarnAnalysisVo.MultipleRecord();
            multipleRecord.setDeptId(dept.getDeptId());
            multipleRecord.setDeptName(dept.getDeptName());
            List<DepartmentWarnAnalysisVo.LevelRecord> levelRecords = new ArrayList<>();
            multipleRecord.setLevelRecords(levelRecords);
            multipleRecords.add(multipleRecord);
            for (String level : levels) {
                DepartmentWarnAnalysisVo.LevelRecord levelRecord = new DepartmentWarnAnalysisVo.LevelRecord();
                levelRecord.setLevel(level);
                levelRecord.setNum(0);
                levelRecords.add(levelRecord);
                if (!CollectionUtils.isEmpty(deptByLevelWarnRecords)) {
                    for (Map<String, Object> map : deptByLevelWarnRecords) {
                        if (level.equals(WarnLevelEnum.getEnumsByValue((Integer) map.get("level")).getName()) && dept.getDeptName().equals(map.get("deptName"))) {
                            levelRecord.setNum(((Long) map.get("num")).intValue());
                            break;
                        }
                    }
                }
            }
        }
        return multipleRecords;
    }



    @Override
    public GroupAnalysisVo getGroupAnalysisList(WarnAnalysisBo request) {
        //默认查询一周
        if (Objects.isNull(request.getStartTime()) && Objects.isNull(request.getEndTime())) {
            request.setStartTime(DateUtil.format(LocalDateTimeUtils.last7DaysStartTime(), "yyyy-MM-dd"));
            request.setEndTime(DateUtil.format(LocalDateTimeUtils.now(), "yyyy-MM-dd"));
        }
        GroupAnalysisVo warnAnalysisVo = new GroupAnalysisVo();
        List<GroupAnalysisVo.MultipleRecord> multipleRecords = new ArrayList<>();
        List<GroupAnalysisVo.SingleRecord> singleRecords = new ArrayList<>();
        warnAnalysisVo.setMultipleRecords(multipleRecords);
        warnAnalysisVo.setSingleRecords(singleRecords);
        List<Map<String, Object>> membersInGroupWarnRecords = iWarnRecordService.countMembersInGroupByLevelWarnRecord(request);
        List<SysUser> sysUsers = sysUserMapper.selectDeptUsers(request.getDeptId());
        if (CollectionUtils.isEmpty(sysUsers)) {
            return warnAnalysisVo;
        }
        for (SysUser user : sysUsers) {
            GroupAnalysisVo.MultipleRecord multipleRecord = new GroupAnalysisVo.MultipleRecord();
            multipleRecord.setNickName(user.getNickName());
            List<GroupAnalysisVo.LevelRecord> levelRecords = new ArrayList<>();
            multipleRecord.setLevelRecords(levelRecords);
            multipleRecords.add(multipleRecord);
            for (String level : levels) {
                GroupAnalysisVo.LevelRecord levelRecord = new GroupAnalysisVo.LevelRecord();
                levelRecord.setLevel(level);
                levelRecord.setNum(0);
                levelRecords.add(levelRecord);
                if (!CollectionUtils.isEmpty(membersInGroupWarnRecords)) {
                    for (Map<String, Object> map : membersInGroupWarnRecords) {
                        if (level.equals(WarnLevelEnum.getEnumsByValue((Integer) map.get("level")).getName()) && user.getNickName().equals(map.get("nickName"))) {
                            levelRecord.setNum(((Long) map.get("num")).intValue());
                            break;
                        }
                    }

                }
            }
        }

        List<Map<String, Object>> membersInGroupTotalWarnRecord = iWarnRecordService.countMembersInGroupTotalWarnRecord(request);
        for (SysUser user : sysUsers) {
            GroupAnalysisVo.SingleRecord singleRecord = new GroupAnalysisVo.SingleRecord();
            singleRecord.setNickName(user.getNickName());
            singleRecord.setNum(0);
            singleRecords.add(singleRecord);
            if (!CollectionUtils.isEmpty(membersInGroupTotalWarnRecord)) {
                for (Map<String, Object> map : membersInGroupTotalWarnRecord) {
                    if (user.getNickName().equals(map.get("nickName"))) {
                        singleRecord.setNum(((Long) map.get("num")).intValue());
                        break;
                    }
                }
            }
        }
        Collections.sort(singleRecords);
        return warnAnalysisVo;
    }

    @Override
    public PersonalAnalysisVo getPersonslAnalysisList(PersonalAnalysisBo request) {
        PersonalAnalysisVo personalAnalysisVo = new PersonalAnalysisVo();
        List<PersonalAnalysisVo.SingleRecord> singleRecords = new ArrayList<>();
        List<PersonalAnalysisVo.MultipleRecord> multipleRecords = new ArrayList<>();
        personalAnalysisVo.setSingleRecords(singleRecords);
        personalAnalysisVo.setMultipleRecords(multipleRecords);
        if (Objects.isNull(request)) {
            String startTime = DateUtil.format(LocalDateTimeUtils.yearStartTime(), "yyyy-MM-dd HH:mm:ss");
            String endTime = DateUtil.format(LocalDateTimeUtils.yearEndTime(), "yyyy-MM-dd HH:mm:ss");
            request.setStartTime(startTime);
            request.setEndTime(endTime);
        } else {
            String startTime = request.getYear() + "-01-01 00:00：00";
            String endTime = request.getYear() + "-12-31 12:59：59";
            request.setStartTime(startTime);
            request.setEndTime(endTime);
        }
        List<Map<String, Object>> queryRecords = iWarnRecordService.countPersonalSingleRecord(request);
        months = months.subList(0, LocalDateTimeUtils.now().getMonth().getValue());
        months.forEach(month -> {
            PersonalAnalysisVo.SingleRecord singleRecord = new PersonalAnalysisVo.SingleRecord();
            if (!CollectionUtils.isEmpty(queryRecords)) {
                for (Map<String, Object> map : queryRecords) {
                    if (month.equals(map.get("month"))) {
                        singleRecord.setMonth(month);
                        singleRecord.setNum(((Long) map.get("num")).intValue());
                        singleRecords.add(singleRecord);
                        return;
                    }
                }
            }
            singleRecord.setMonth(month);
            singleRecord.setNum(0);
            singleRecords.add(singleRecord);
        });

        List<PersonalLevelRecordVO> levelRecordList = iWarnRecordService.countPersonalLevelRecord(request);
        months = months.subList(0, LocalDateTimeUtils.now().getMonth().getValue());
        months.forEach(month -> {
            PersonalAnalysisVo.MultipleRecord multipleRecord = new PersonalAnalysisVo.MultipleRecord();
            List<PersonalAnalysisVo.LevelRecord> levelRecords = new ArrayList<>();
            multipleRecord.setLevelRecords(levelRecords);
            multipleRecord.setMonth(month);
            multipleRecords.add(multipleRecord);
            for (String level : levels) {
                PersonalAnalysisVo.LevelRecord levelRecord = new PersonalAnalysisVo.LevelRecord();
                levelRecord.setLevel(level);
                levelRecord.setNum(0);
                levelRecords.add(levelRecord);
                if (!CollectionUtils.isEmpty(levelRecordList)) {
                    for (PersonalLevelRecordVO vo : levelRecordList) {
                        if (month.equals(vo.getMonth()) && level.equals(WarnLevelEnum.getEnumsByValue(vo.getLevel()).getName())) {
                            levelRecord.setNum(vo.getNum());
                            break;
                        }
                    }
                }
            }
        });
        return personalAnalysisVo;
    }

    @Override
    public Page<WarningAllVO> getWarnPersonalPage(WarningBaseBO request, LoginUser user) {
        Long userId = user.getUserId();
        request.setUserId(userId);
        Page<WarningAllVO> page = new Page<>(request.getPageNo(), request.getPageSize());

        List<WarningAllVO> warningVOList = warnRecordMapper.getWarningList(page, request);
        // 批量处理超过10天未更新的
        this.updateOverDueHandleContent(warningVOList);
        // 重新查一遍
        List<WarningAllVO> list = warnRecordMapper.getWarningList(page, request);
        // 按创建时间倒序
        page.setRecords(list);
        return page;
    }

    @Override
    public Page<WarningAllVO> getWarnGroupsPage(WarningBaseBO request, LoginUser user) {
        Long deptId = user.getDeptId();
        List<SysUser> users = sysUserMapper.selectDeptUsers(deptId);
        List<Long> userIds = users.stream().map(SysUser::getUserId).collect(Collectors.toList());
        request.setUserIds(userIds);
        Page<WarningAllVO> page = new Page<>(request.getPageNo(), request.getPageSize());

        List<WarningAllVO> warningVOList = warnRecordMapper.getWarningList(page, request);
        // 批量处理超过10天未更新的
        this.updateOverDueHandleContent(warningVOList);
        // 重新查一遍
        List<WarningAllVO> warningList = warnRecordMapper.getWarningList(page, request);
        // 按创建时间倒序
        page.setRecords(warningList);
        return page;
    }

    @Override
    public Page<WarningAllVO> getWarnAllPage(WarningBaseAllBO request) {
        Page<WarningAllVO> page = new Page<>(request.getPageNo(), request.getPageSize());
        List<WarningAllVO> warningVOList = warnRecordMapper.getWarnAllList(page, request);
        // 批量处理超过10天未更新的
        this.updateOverDueHandleContent(warningVOList);
        // 在重新查一遍
        List<WarningAllVO> warnAllList = warnRecordMapper.getWarnAllList(page, request);
        // 按创建时间倒序
        page.setRecords(warnAllList);
        return page;
    }

    @Override
    public Page<WarningAllVO> getWarnAllPageByUser(WorkDetailBo workDetail) {
        Page<WarningAllVO> page = new Page<>(workDetail.getPageNo(), workDetail.getPageSize());
        WarningBaseAllBO request = new WarningBaseAllBO();
        request.setNickName(workDetail.getWorkUsername());
        //时间处理，传入的时间为年份和月份，需要转化成开始和结束时间，如果月份为空或者-1就表示查整年
        String beginDate = "";
        String endDate = "";
        if (workDetail.getEvalMonth() == null || workDetail.getEvalMonth() == -1) {
            beginDate = workDetail.getEvalYear() + "-01-01 00:00:00";
            endDate = workDetail.getEvalYear() + "-12-31 23:59:59";
        } else {
            beginDate = workDetail.getEvalYear() + "-" + workDetail.getEvalMonth() + "-01 00:00:00";
            endDate = workDetail.getEvalYear() + "-" + workDetail.getEvalMonth() + "-31 23:59:59";
        }
        request.setBeginDate(beginDate);
        request.setEndDate(endDate);
        List<WarningAllVO> warningVOList = warnRecordMapper.getWarnAllList(page, request);
        // 批量处理超过10天未更新的
        this.updateOverDueHandleContent(warningVOList);
        // 在重新查一遍
        List<WarningAllVO> warnAllList = warnRecordMapper.getWarnAllList(page, request);
        // 按创建时间倒序
        page.setRecords(warnAllList);
        return page;
    }

    @Override
    public List<WarnSelectedGroupVO> getWarnGroupSelets() {
        List<SysDept> sysDepts = sysDeptMapper.selectList(Wrappers.lambdaQuery(SysDept.class).eq(SysDept::getParentId, 101L));
        List<WarnSelectedGroupVO> selectedList = new ArrayList<>();
        // 全部
        WarnSelectedGroupVO selectedVO = new WarnSelectedGroupVO();
        selectedVO.setName("全部");
        selectedVO.setValue(-1L);
        selectedList.add(selectedVO);
        // 遍历
        sysDepts.stream().forEach(
            sysDept -> {
                WarnSelectedGroupVO vo = new WarnSelectedGroupVO();
                vo.setValue(sysDept.getDeptId());
                vo.setName(sysDept.getDeptName());
                selectedList.add(vo);
            }
        );
        // 升序
        selectedList.stream().sorted(Comparator.comparing(WarnSelectedGroupVO::getValue)).collect(Collectors.toList());
        return selectedList;
    }

    @Override
    public void updateHandleContent(WarnHandleContentBo contentBo) {
        WarnRecordDetail recordDetail = new WarnRecordDetail();
        recordDetail.setId(contentBo.getWarnDetailId());
        recordDetail.setHandleContent(contentBo.getHandleContent());
        recordDetail.setHandleStatus(WarnHandleStatusEnum.PROCESSED.getValue());
        warnRecordDetailMapper.updateById(recordDetail);
    }

    @Override
    public void updateOverDueHandleContent(List<WarningAllVO> voList) {

        if (!CollectionUtils.isEmpty(voList)) {
            List<WarnRecordDetail> detailList = new ArrayList<>();
            // 遍历
            for (WarningAllVO vo : voList) {
                int i = DateUtils.differentDaysByMillisecond(vo.getCreateTime(), new Date());
                log.info("id: {} , 未处理的工作日时长: {} , 处理状态: {} ", vo.getWarnDetailId(), i, WarnHandleStatusEnum.getEnumsByValue(vo.getHandleStatus()).getName());
                // 校验没有超过10个工作日且是已处理
                if (i < 10 && vo.getHandleStatus().equals(WarnHandleStatusEnum.PENDING.getValue())) {
                    continue;
                }
                // 校验超过10个工作日且是已经处理就不在处理
                if (i < 10 && vo.getHandleStatus().equals(WarnHandleStatusEnum.PROCESSED.getValue())) {
                    continue;
                }
            }

            if (!CollectionUtils.isEmpty(detailList)) {
                // 批量更新
                warnRecordDetailMapper.updateBatchById(detailList);
            }
        }
    }
}
