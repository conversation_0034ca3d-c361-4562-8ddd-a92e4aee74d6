package com.qmqb.imp.web.controller.performance;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackMainBo;
import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackMainQueryBo;
import com.qmqb.imp.system.domain.vo.performance.PerformanceFeedbackMainVo;
import com.qmqb.imp.system.service.indicator.IPerformanceFeedbackMainService;
import com.qmqb.imp.system.domain.dto.BatchFinalAuditRequest;
import com.qmqb.imp.system.domain.dto.BatchProjectManagerAuditRequest;
import com.qmqb.imp.system.domain.dto.BatchSubmitRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 绩效反馈主表
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/performanceFeedbackMain")
public class PerformanceFeedbackMainController extends BaseController {

    private final IPerformanceFeedbackMainService iPerformanceFeedbackMainService;

    /**
     * 查询绩效反馈主表列表
     */
    @SaCheckPermission("system:performanceFeedbackMain:list")
    @GetMapping("/list")
    public TableDataInfo<PerformanceFeedbackMainVo> list(PerformanceFeedbackMainQueryBo bo, PageQuery pageQuery) {
        return iPerformanceFeedbackMainService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出绩效反馈主表列表
     */
    @SaCheckPermission("system:performanceFeedbackMain:export")
    @Log(title = "绩效反馈主表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PerformanceFeedbackMainQueryBo bo, HttpServletResponse response) {
        List<PerformanceFeedbackMainVo> list = iPerformanceFeedbackMainService.queryList(bo);
        ExcelUtil.exportExcel(list, "绩效反馈主表", PerformanceFeedbackMainVo.class, response);
    }

    /**
     * 获取绩效反馈主表详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:performanceFeedbackMain:query")
    @GetMapping("/{id}")
    public R<PerformanceFeedbackMainVo> getInfo(@NotNull(message = "主键不能为空")
                                                @PathVariable Long id) {
        return R.ok(iPerformanceFeedbackMainService.queryById(id));
    }

    /**
     * 新增绩效反馈主表
     */
    @SaCheckPermission("system:performanceFeedbackMain:add")
    @Log(title = "绩效反馈主表", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PerformanceFeedbackMainBo bo) {
        return toAjax(iPerformanceFeedbackMainService.insertByBo(bo));
    }

    /**
     * 修改绩效反馈主表
     */
    @SaCheckPermission("system:performanceFeedbackMain:edit")
    @Log(title = "绩效反馈主表", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PerformanceFeedbackMainBo bo) {
        return toAjax(iPerformanceFeedbackMainService.updateByBo(bo));
    }

    /**
     * 批量项管审核
     */
    @SaCheckPermission("system:performanceFeedbackMain:projectManagerAudit")
    @Log(title = "绩效反馈主表-批量项管审核", businessType = BusinessType.UPDATE)
    @PostMapping("/projectManagerAudit")
    public R<Void> batchProjectManagerAudit(@RequestBody @Validated BatchProjectManagerAuditRequest request) {
        String auditor = LoginHelper.getUsername();
        return toAjax(iPerformanceFeedbackMainService.batchProjectManagerAudit(request.getIds(), request.getStatus(), auditor, request.getRemark()));
    }

    /**
     * 删除绩效反馈主表
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:performanceFeedbackMain:remove")
    @Log(title = "绩效反馈主表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iPerformanceFeedbackMainService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 批量提交
     */
    @SaCheckPermission("system:performanceFeedbackMain:batchSubmit")
    @Log(title = "绩效反馈主表-批量提交", businessType = BusinessType.UPDATE)
    @PostMapping("/batchSubmit")
    public R<Void> batchSubmit(@RequestBody @Validated BatchSubmitRequest request) {
        String submitter = LoginHelper.getUsername();
        return toAjax(iPerformanceFeedbackMainService.batchSubmit(request.getIds(), submitter));
    }

    /**
     * 批量最终审核
     */
    @SaCheckPermission("system:performanceFeedbackMain:batchFinalAudit")
    @Log(title = "绩效反馈主表-批量最终审核", businessType = BusinessType.UPDATE)
    @PostMapping("/batchFinalAudit")
    public R<Void> batchFinalAudit(@RequestBody @Validated BatchFinalAuditRequest request) {
        String auditor = LoginHelper.getUsername();
        return toAjax(iPerformanceFeedbackMainService.batchFinalAudit(request.getIds(), request.getStatus(), auditor, request.getRemark()));
    }
}
