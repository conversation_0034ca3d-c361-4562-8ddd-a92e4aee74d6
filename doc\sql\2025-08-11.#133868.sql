ALTER TABLE tb_performance_feedback
    ADD COLUMN is_canceled TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否已取消（0 否，1 是)';

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1941329663716004418, '取消记录', 1939931677164421121, 1, '#', '', NULL, 1, 0, 'F', '0', '0', 'system:performanceFeedback:batchCancel', '#', 'admin', NOW(), '', NULL, '');
