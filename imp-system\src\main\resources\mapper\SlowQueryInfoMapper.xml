<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.SlowQueryInfoMapper">

    <resultMap type="com.qmqb.imp.system.domain.SlowQueryInfo" id="SlowQueryInfoResult">
        <result property="id" column="id"/>
        <result column="DBName" property="dbName" jdbcType="VARCHAR"/>
        <result column="IP" property="ip" jdbcType="VARCHAR"/>
        <result column="User" property="user" jdbcType="VARCHAR"/>
        <result column="last_executionStartTime" property="lastExecutionStartTime" jdbcType="TIMESTAMP"/>
        <result column="last_parseRowCounts" property="lastParseRowCounts" jdbcType="VARCHAR"/>
        <result column="last_returnRowCounts" property="lastReturnRowCounts" jdbcType="VARCHAR"/>
        <result column="last_queryTimeMS" property="lastQueryTimeMs" jdbcType="BIGINT"/>
        <result column="SQLHash" property="sqlHash" jdbcType="VARCHAR"/>
        <result column="total_query_times" property="totalQueryTimes" jdbcType="BIGINT"/>
        <result column="total_sum_time" property="totalSumTime" jdbcType="BIGINT"/>
        <result column="avg_query_time" property="avgQueryTime" jdbcType="BIGINT"/>
        <result column="avg_returnRowCounts" property="avgReturnRowCounts" jdbcType="BIGINT"/>
        <result column="avg_parseRowCounts" property="avgParseRowCounts" jdbcType="BIGINT"/>
        <result column="slow_rule" property="slowRule" jdbcType="VARCHAR"/>
        <result column="warn_level" property="warnLevel" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="slowSqlPage" resultType="com.qmqb.imp.system.domain.vo.SlowQueryInfoVo">
        select
        sqi.id id,
        sqi.IP ip,
        sqi.last_executionStartTime lastExecutionStartTime,
        sqi.SQLHash sqlHash,
        sqi.total_query_times totalQueryTimes,
        sqi.total_sum_time totalSumTime,
        sqi.avg_query_time avgQueryTime,
        sqi.slow_rule slowRule,
        sqi.warn_level warnLevel,
        sqpi.process_status processStatus,
        sqpi.process_time processTime,
        sqpi.process_by processBy,
        sqpi.process_result processResult,
        sqpi.analysis_result analysisResult
        from t_slow_query_info sqi
        left join t_slow_query_process_info sqpi on sqi.SQLHash = sqpi.sql_hash
        <where>
            <if test="bo.dbName != null and bo.dbName !=''">
                and sqi.DBName = #{bo.dbName}
            </if>
            <if test="bo.ip != null and bo.ip !=''">
                and sqi.IP = #{bo.ip}
            </if>
            <if test="bo.warnLevel != null and bo.warnLevel !=''">
                and sqi.warn_level = #{bo.warnLevel}
            </if>
            <if test="bo.slowRule != null and bo.slowRule !=''">
                and sqi.slow_rule = #{bo.slowRule}
            </if>
            <if test="bo.lastExecutionStartTimeStart != null">
                and sqi.last_executionStartTime >= #{bo.lastExecutionStartTimeStart}
            </if>
            <if test="bo.lastExecutionStartTimeEnd != null">
                and sqi.last_executionStartTime &lt;= #{bo.lastExecutionStartTimeEnd}
            </if>
            <if test="bo.processStatus != null and bo.processStatus == 'YES'">
                and sqpi.process_status = 'YES'
            </if>
            <if test="bo.processStatus != null and bo.processStatus == 'NO'">
                and (sqpi.process_status = 'NO' or sqpi.process_status is null)
            </if>
            <if test="bo.processTimeStart != null">
                and sqpi.process_time >= #{bo.processTimeStart}
            </if>
            <if test="bo.processTimeEnd != null">
                and sqpi.process_time &lt;= #{bo.processTimeEnd}
            </if>
        </where>
    </select>


</mapper>
