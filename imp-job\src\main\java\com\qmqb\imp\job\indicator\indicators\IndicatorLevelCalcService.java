package com.qmqb.imp.job.indicator.indicators;

import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.common.utils.ScoreLevelUtil;
import com.qmqb.imp.system.domain.performance.PerformanceFeedback;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.service.IUserKqStatService;
import lombok.Data;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 绩效指标等级计算接口
 *
 * <AUTHOR>
 */
public interface IndicatorLevelCalcService {
    /**
     * 获取指标编码
     *
     * @return 指标编码
     */
    String getIndicatorCode();


    /**
     * 计算指定员工、月份的绩效等级和日志内容
     *
     * @param nickName         员工昵称
     * @param month            月份
     * @param trackWorkResults 当前角色全部人员的工作成果跟踪数据
     * @return 指标计算结果，包含等级和日志内容
     */
    default IndicatorCalcResult calcLevel(String nickName, Integer month, List<TrackWorkResultVO> trackWorkResults) {
        Optional<TrackWorkResultVO> userWorkResultOpt = trackWorkResults.stream()
            .filter(r -> nickName.equals(r.getWorkUsername()))
            .findFirst();

        if (!userWorkResultOpt.isPresent()) {
            String indicatorName = "未知指标";
            try {
                indicatorName = PerformanceIndicatorEnum.valueOf(getIndicatorCode().toUpperCase()).getName();
            } catch (IllegalArgumentException e) {
                // Ignore if enum not found
            }
            return new IndicatorCalcResult(ScoreLevelEnum.SCORE_C.getCode(),
                String.format("员工[%s]在%s月份的%s数据未找到", nickName, month, indicatorName));
        }

        TrackWorkResultVO userWorkResult = userWorkResultOpt.get();

        String level = calculateLevel(userWorkResult, nickName, trackWorkResults);
        String logContent = createLogContent(userWorkResult, nickName, level);

        return new IndicatorCalcResult(level, logContent);
    }

    /**
     * 根据工作成果计算等级
     *
     * @param workResult       当前员工工作成果
     * @param nickName         员工昵称
     * @param trackWorkResults 全部人员的工作成果数据
     * @return 绩效等级
     */
    String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults);

    /**
     * 创建指标日志内容
     *
     * @param workResult 工作成果
     * @param nickName   员工昵称
     * @param level      绩效等级
     * @return 日志内容
     */
    String createLogContent(TrackWorkResultVO workResult, String nickName, String level);

    /**
     * 根据反馈记录计算等级
     *
     * @param feedbackList 反馈记录
     * @return 绩效等级
     */
    default String calculateLevelFromFeedbacks(List<PerformanceFeedback> feedbackList) {
        if (feedbackList == null || feedbackList.isEmpty()) {
            // 无反馈记录默认为B级（合格）
            return ScoreLevelEnum.SCORE_B.getCode();
        }

        // 如果只有一个反馈，直接返回其等级
        if (feedbackList.size() == 1) {
            return feedbackList.get(0).getRecommendedLevel();
        }

        // 多个反馈时，使用ScoreLevelUtil计算综合等级
        double totalA = 0.0;
        int cCount = 0;

        for (PerformanceFeedback feedback : feedbackList) {
            String level = feedback.getRecommendedLevel();
            double aValue = ScoreLevelUtil.convertLevelToAvalue(level);
            totalA += aValue;

            if (ScoreLevelEnum.SCORE_C.getCode().equals(level)) {
                cCount++;
            }
        }
        // 获取全部levels
        List<String> levels = feedbackList.stream()
            .map(PerformanceFeedback::getRecommendedLevel)
            .collect(Collectors.toList());

        // 使用ScoreLevelUtil计算综合等级
        return ScoreLevelUtil.determineCategoryLevel(totalA, cCount,levels);
    }

    /**
     * 指标计算结果
     */
    @Data
    class IndicatorCalcResult {
        private final String level;
        private final String logContent;

        public IndicatorCalcResult(String level, String logContent) {
            this.level = level;
            this.logContent = logContent;
        }
    }
}
