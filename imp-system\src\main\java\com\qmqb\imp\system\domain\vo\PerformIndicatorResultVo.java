package com.qmqb.imp.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.qmqb.imp.common.annotation.ExcelDictFormat;
import com.qmqb.imp.common.convert.ExcelDictConvert;
import lombok.Data;


/**
 * 绩效指标原因视图对象 tb_perform_indicator_result
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@ExcelIgnoreUnannotated
public class PerformIndicatorResultVo {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 一类指标
     */
    @ExcelProperty(value = "一类指标")
    private String firstIndecator;

    private String firstIndecatorName;

    /**
     * 二类指标
     */
    @ExcelProperty(value = "二类指标")
    private String secondIndecator;

    private String secondIndecatorName;

    /**
     * 绩效级别(S/A/B/C/D)
     */
    @ExcelProperty(value = "绩效级别(S/A/B/C/D)")
    private String level;

    /**
     * 推荐原因
     */
    @ExcelProperty(value = "推荐原因")
    private String result;


}
