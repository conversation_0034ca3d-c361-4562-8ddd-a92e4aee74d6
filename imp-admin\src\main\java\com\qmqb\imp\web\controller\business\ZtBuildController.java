package com.qmqb.imp.web.controller.business;


import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qmqb.imp.common.core.controller.BaseController;

import com.qmqb.imp.system.service.IZtBuildService;

/**
 * 【请填写功能名称】
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/build")
public class ZtBuildController extends BaseController {

    private final IZtBuildService iZtBuildService;


}
