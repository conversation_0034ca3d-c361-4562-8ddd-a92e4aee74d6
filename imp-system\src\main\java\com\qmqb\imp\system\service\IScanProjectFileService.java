package com.qmqb.imp.system.service;

import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.ScanProjectFile;
import com.qmqb.imp.system.domain.bo.ScanProjectFileAssignBo;
import com.qmqb.imp.system.domain.bo.ScanProjectFileBo;
import com.qmqb.imp.system.domain.bo.ScanProjectFileProcessBo;
import com.qmqb.imp.system.domain.vo.ScanProjectFileVo;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 扫描项目文件记录Service接口
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
public interface IScanProjectFileService {

    /**
     * 查询扫描项目文件记录
     * @param id 主键id
     * @return 扫描项目文件记录
     */
    ScanProjectFileVo queryById(Long id);

    /**
     * 查询扫描项目文件记录列表
     * @param bo 扫描项目文件记录查询条件
     * @param pageQuery 分页查询条件
     * @return 扫描项目文件记录列表
     */
    TableDataInfo<ScanProjectFileVo> queryPageList(ScanProjectFileBo bo, PageQuery pageQuery);

    /**
     * 查询扫描项目文件记录列表（带权限控制）
     * @param bo 扫描项目文件记录查询条件
     * @param pageQuery 分页查询条件
     * @return 扫描项目文件记录列表
     */
    TableDataInfo<ScanProjectFileVo> queryPageListWithPermission(ScanProjectFileBo bo, PageQuery pageQuery);

    /**
     * 查询扫描项目文件记录列表
     * @param bo 扫描项目文件记录查询条件
     * @return 扫描项目文件记录列表
     */
    List<ScanProjectFileVo> queryList(ScanProjectFileBo bo);

    /**
     * 新增扫描项目文件记录
     * @param bo 扫描项目文件记录查询条件
     * @return 新增是否成功
     */
    Boolean insertByBo(ScanProjectFileBo bo);

    /**
     * 新增扫描项目文件记录
     * @param scanProjectFile 扫描项目文件记录
     * @return 新增是否成功
     */
    Boolean insert(ScanProjectFile scanProjectFile);

    /**
     * 修改扫描项目文件记录
     * @param bo 扫描项目文件记录查询条件
     * @return 修改是否成功
     */
    Boolean updateByBo(ScanProjectFileBo bo);

    /**
     * 校验并批量删除扫描项目文件记录信息
     * @param ids 主键id集合
     * @param isValid 是否有效
     * @return 删除是否成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 更新旧扫描记录为非最新状态
     * @param pid 项目ID
     * @param scanFileUrls 文件路径列表，为null时更新项目的所有文件记录
     * @return 更新是否成功
     */
    Boolean updatePreviousScanToOld(Long pid, List<String> scanFileUrls);

    /**
     * 根据项目id和扫描版本查询文件列表
     * @param pid 项目id
     * @param scanVersion 扫描版本
     * @return 扫描项目文件记录列表
     */
    List<ScanProjectFileVo> selectByPidAndVersion(Long pid, Long scanVersion);

    /**
     * 根据状态统计文件数量
     * @param pid 项目id
     * @param status 状态
     * @return 文件数量
     */
    Long countByStatus(Long pid, String status);

    /**
     * 批量插入
     * @param scanProjectFileList 扫描项目文件记录列表
     * @return 批量插入是否成功
     */
    Boolean insertBatch(List<ScanProjectFile> scanProjectFileList);

    /**
     * 通过项目id批量删除文件记录
     * @param deleteIds 要删除的项目id集合
     * @return 批量删除是否成功
     */
    Boolean batchDeleteByPids(Collection<Long> deleteIds);

    /**
     * 根据项目ID和文件路径查询最新的旧数据记录
     * @param pid 项目ID
     * @param scanFileUrl 文件路径
     * @return 扫描项目文件记录
     */
    ScanProjectFile selectLatestByPidAndScanFileUrl(Long pid, String scanFileUrl);

    /**
     * 更新文件状态为已处理
     * @param id 文件ID
     * @param handleTime 处理时间
     * @return 更新是否成功
     */
    Boolean updateFileStatusToHandled(Long id, Date handleTime);

    /**
     * 执行文件操作（指派、更换指派）
     * @param operationBo 操作参数
     * @return 操作是否成功
     */
    Boolean executeFileAssign(ScanProjectFileAssignBo operationBo);

    /**
     * 执行文件操作（处理）
     * @param operationBo 操作参数
     * @return 操作是否成功
     */
    Boolean executeFileProcess(ScanProjectFileProcessBo operationBo);

    /**
     * 批量软删除扫描项目文件记录
     *
     * @param projectIds 项目ID集合
     * @return 是否成功
     */
    Boolean batchSoftDeleteByProjectIds(Set<Long> projectIds);
}
