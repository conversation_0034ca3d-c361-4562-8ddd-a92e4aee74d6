package com.qmqb.imp.job.indicator.indicators.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.WorkStat;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.mapper.WorkStatMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 月创建BUG数指标等级计算
 * @date 2025/7/2 14:35
 */
@Service
public class BugCountIndicatorServiceImpl implements IndicatorLevelCalcService {


    @Resource
    private WorkStatMapper workStatMapper;

    private static final int S_BUG_COUNT = 25;
    private static final int A_BUG_COUNT = 18;
    private static final int B_BUG_COUNT = 10;
    private static final int C_BUG_COUNT = 3;

    @Override
    public String getIndicatorCode() {
        return PerformanceIndicatorEnum.BUG_COUNT.getCode();
    }

    @Override
    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults) {
        int bugCount = Optional.ofNullable(workResult.getCreateBugCount()).orElse(0);
        if (bugCount >= S_BUG_COUNT) {
            return ScoreLevelEnum.SCORE_S.getCode();
        } else if (bugCount >= A_BUG_COUNT) {
            return ScoreLevelEnum.SCORE_A.getCode();
        } else if (bugCount >= B_BUG_COUNT) {
            return ScoreLevelEnum.SCORE_B.getCode();
        } else if (bugCount < C_BUG_COUNT) {
            LocalDate localDate = LocalDate.of(workResult.getWorkYear(), workResult.getWorkMonth(), 1)
                .plusMonths(-1);
            WorkStat lastMonthWorkStat = workStatMapper.selectOne(new LambdaQueryWrapper<WorkStat>()
                .eq(WorkStat::getWorkYear, localDate.getYear())
                .eq(WorkStat::getWorkMonth, localDate.getMonth())
                .eq(WorkStat::getWorkUsername, workResult.getWorkUsername())
                .last("limit 1"));
            if (Optional.ofNullable(lastMonthWorkStat).map(WorkStat::getCreateBugCount).orElse(0L) < C_BUG_COUNT) {
                return ScoreLevelEnum.SCORE_D.getCode();
            }
            return ScoreLevelEnum.SCORE_C.getCode();
        }
        return ScoreLevelEnum.SCORE_B.getCode();
    }

    @Override
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level) {
        int bugCount = Optional.ofNullable(workResult.getCreateBugCount()).orElse(0);
        Integer year = workResult.getWorkYear();
        Integer month = workResult.getWorkMonth();
        StringBuilder logContent = new StringBuilder();
        logContent.append(String.format("[%s]在%s年%s月份创建BUG数：%d个", nickName, year, month, bugCount));
        logContent.append(String.format("，评级：%s", level));
        String reason = getRatingReason(bugCount, level);
        if (reason != null) {
            logContent.append(String.format("，原因：%s", reason));
        }
        return logContent.toString();
    }

    /**
     * 获取评级原因
     *
     * @param bugCount
     * @param level
     * @return
     */
    private String getRatingReason(int bugCount, String level) {
        switch (level) {
            case "S":
                return String.format("创建BUG数(%d)达到S级标准(≥%d个)", bugCount, S_BUG_COUNT);
            case "A":
                return String.format("创建BUG数(%d)达到A级标准(≥%d个)", bugCount, A_BUG_COUNT);
            case "B":
                return String.format("创建BUG数(%d)达到B级标准(≥%d个)", bugCount, B_BUG_COUNT);
            case "C":
                return String.format("创建BUG数(%d)达到C级标准(<%d个)", bugCount, C_BUG_COUNT);
            case "D":
                return String.format("连续2个月创建BUG数为<%d个", C_BUG_COUNT);
            default:
                return null;
        }
    }
}
