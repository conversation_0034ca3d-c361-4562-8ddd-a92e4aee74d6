package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 慢sql信息对象 tb_slow_query_info
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_slow_query_info")
public class SlowQueryInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 数据库名称
     */
    @TableField("DBName")
    private String dbName;

    /**
     * 客户端IP
     */
    @TableField("IP")
    private String ip;

    /**
     * 客户端用户名
     */
    @TableField("User")
    private String user;

    /**
     * 最近执行开始时间
     */
    @TableField("last_executionStartTime")
    private LocalDateTime lastExecutionStartTime;

    /**
     * 最近解析涉及的记录条数
     */
    @TableField("last_parseRowCounts")
    private String lastParseRowCounts;

    /**
     * 最近查询返回的记录条数
     */
    @TableField("last_returnRowCounts")
    private String lastReturnRowCounts;

    /**
     * 最近查询耗时（毫秒）
     */
    @TableField("last_queryTimeMS")
    private Long lastQueryTimeMs;

    /**
     * SQL哈希值（同类SQL标识）
     */
    @TableField("SQLHash")
    private String sqlHash;

    /**
     * 总查询次数（近7天）
     */
    @TableField("total_query_times")
    private Long totalQueryTimes;

    /**
     * 总查询时间（毫秒，近7天）
     */
    @TableField("total_sum_time")
    private Long totalSumTime;

    /**
     * 平均查询时间（毫秒，近7天）
     */
    @TableField("avg_query_time")
    private Long avgQueryTime;

    /**
     * 平均返回记录数（近7天）
     */
    @TableField("avg_returnRowCounts")
    private Long avgReturnRowCounts;

    /**
     * 平均解析记录数（近7天）
     */
    @TableField("avg_parseRowCounts")
    private Long avgParseRowCounts;

    /**
     * 慢查询规则标识<br>
     * returnNull：返回空值<br>
     * returnRowCounts：返回记录数过多<br>
     * parseRowCounts：解析记录数过多<br>
     * query_time：查询时间过长<br>
     * query_hot：慢查询且频繁
     */
    @TableField("slow_rule")
    private String slowRule;

    /**
     * 告警级别（P0、P1、P2）
     */
    @TableField("warn_level")
    private String warnLevel;

}
