package com.qmqb.imp.web.controller.business;

import java.util.List;
import java.util.Arrays;
import java.util.Map;

import com.qmqb.imp.common.core.domain.dto.UserLeaveStatDTO;
import com.qmqb.imp.system.domain.vo.UserLeaveStatVO;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.vo.UserLeaveVo;
import com.qmqb.imp.system.domain.bo.UserLeaveBo;
import com.qmqb.imp.system.service.IUserLeaveService;
import com.qmqb.imp.common.core.page.TableDataInfo;

/**
 * 请假记录
 *
 * <AUTHOR>
 * @date 2023-07-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/userLeave")
public class UserLeaveController extends BaseController {

    private final IUserLeaveService iUserLeaveService;

    /**
     * 查询请假记录列表
     */
    @SaCheckPermission("system:userLeave:list")
    @GetMapping("/list")
    public TableDataInfo<UserLeaveVo> list(UserLeaveBo bo, PageQuery pageQuery) {
        return iUserLeaveService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出请假记录列表
     */
    @SaCheckPermission("system:userLeave:export")
    @Log(title = "请假记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(UserLeaveBo bo, HttpServletResponse response) {
        List<UserLeaveVo> list = iUserLeaveService.queryList(bo);
        ExcelUtil.exportExcel(list, "请假记录", UserLeaveVo.class, response);
    }

    /**
     * 获取请假记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:userLeave:query")
    @GetMapping("/{id}")
    public R<UserLeaveVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iUserLeaveService.queryById(id));
    }

    /**
     * 新增请假记录
     */
    @SaCheckPermission("system:userLeave:add")
    @Log(title = "请假记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody UserLeaveBo bo) {
        return toAjax(iUserLeaveService.insertByBo(bo));
    }

    /**
     * 修改请假记录
     */
    @SaCheckPermission("system:userLeave:edit")
    @Log(title = "请假记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody UserLeaveBo bo) {
        return toAjax(iUserLeaveService.updateByBo(bo));
    }

    /**
     * 删除请假记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:userLeave:remove")
    @Log(title = "请假记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iUserLeaveService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 统计部门本月以及上个月各天的请假人数
     *
     * @param userLeaveStatDTO
     * @return
     */
    @GetMapping("/statistic/userLeaveCount")
    public R<UserLeaveStatVO> statisticUserLeaveCount(UserLeaveStatDTO userLeaveStatDTO) {
        return R.ok(iUserLeaveService.statisticUserLeaveCount(userLeaveStatDTO));
    }

    /**
     * 统计各组近两个月请假的总人数
     * @return
     */
    @GetMapping("/statistic/userLeaveCountByDept")
    public R<Map<String,Integer>> userLeaveCountByDept() {
        return R.ok(iUserLeaveService.userLeaveCountByDept());
    }


}
