package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.domain.Project;
import com.qmqb.imp.system.domain.ScanProjectFile;
import com.qmqb.imp.system.domain.bo.ScanProjectFileAssignBo;
import com.qmqb.imp.system.domain.bo.ScanProjectFileBo;
import com.qmqb.imp.system.domain.bo.ScanProjectFileProcessBo;
import com.qmqb.imp.system.domain.vo.ScanProjectFileVo;
import com.qmqb.imp.system.mapper.ScanProjectFileMapper;
import com.qmqb.imp.system.service.IProjectService;
import com.qmqb.imp.system.service.IScanProjectFileService;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.ZentaoApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 扫描项目文件记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ScanProjectFileServiceImpl implements IScanProjectFileService {

    private final ScanProjectFileMapper baseMapper;
    private final ISysUserService sysUserService;
    private final ZentaoApiService zentaoApiService;
    private final IProjectService projectService;

    /**
     * 查询扫描项目文件记录
     */
    @Override
    public ScanProjectFileVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询扫描项目文件记录列表
     */
    @Override
    public TableDataInfo<ScanProjectFileVo> queryPageList(ScanProjectFileBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ScanProjectFile> lqw = buildQueryWrapper(bo);

        // 设置默认排序：优先按状态升序排序，再按指派时间升序排序
        if (StringUtils.isBlank(pageQuery.getOrderByColumn())) {
            lqw.orderByAsc(ScanProjectFile::getStatus)
               .orderByAsc(ScanProjectFile::getAssignTime);
        }

        Page<ScanProjectFileVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询扫描项目文件记录列表（带权限控制）
     */
    @Override
    public TableDataInfo<ScanProjectFileVo> queryPageListWithPermission(ScanProjectFileBo bo, PageQuery pageQuery) {
        if (StrUtil.isBlank(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn(bo.getOrderByField());
        }
        if (StrUtil.isBlank(pageQuery.getIsAsc())) {
            pageQuery.setIsAsc(bo.getOrderRule());
        }
        // 获取当前登录用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("当前用户未登录");
        }
        SysUser currentUser = sysUserService.selectUserById(loginUser.getUserId());

        // 权限过滤：组员只能看到指派给自己的数据，组长可看到本组的全部数据
        if (currentUser != null && currentUser.getRoles() != null && !currentUser.isTechnicalManager() && !currentUser.isAdmin() && !currentUser.isJszxAdmin()) {
            // 组员只能看到指派给自己的数据
            bo.setHandleUserId(currentUser.getUserId());
        }

        LambdaQueryWrapper<ScanProjectFile> lqw = buildQueryWrapper(bo);

        // 排序逻辑：支持单个字段排序，默认为状态升序+指派时间升序
        if (StringUtils.isBlank(pageQuery.getOrderByColumn())) {
            // 默认排序：优先按状态升序排序，再按指派时间升序排序
            lqw.orderByAsc(ScanProjectFile::getStatus)
               .orderByAsc(ScanProjectFile::getAssignTime);
        }
        Page<ScanProjectFileVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }


    /**
     * 执行指派操作（包含指派和更换指派）
     */
    private void executeAssignOperation(List<ScanProjectFile> files, ScanProjectFileAssignBo operationBo, SysUser currentUser) {
        // 只要状态是未处理的（状态0或1）都可以指派/更换指派
        for (ScanProjectFile file:files){
            if (!CommConstants.CommonValStr.ZERO.equals(file.getStatus()) && !CommConstants.CommonValStr.ONE.equals(file.getStatus())) {
                throw new ServiceException("只能指派未处理状态的文件");
            }
        }

        // 根据处理人ID查找用户信息
        SysUser handleUser = sysUserService.selectUserById(operationBo.getHandleUserId());
        if (handleUser == null) {
            throw new ServiceException("指派的处理人不存在");
        }
        files.forEach(file -> {
            file.setStatus("1");
            file.setHandleUser(handleUser.getNickName());
            file.setHandleUserId(operationBo.getHandleUserId());
            file.setAssignUser(currentUser.getNickName());
            file.setAssignUserId(currentUser.getUserId());
            file.setAssignTime(new Date());

            // 指派后处理时间和处理说明都清空
            file.setHandleTime(null);
            file.setHandleRemark(null);
            file.setUpdateTime(new Date());
        });
    }

    /**
     * 执行处理操作
     */
    private void executeHandleOperation(List<ScanProjectFile> files, ScanProjectFileProcessBo operationBo, SysUser currentUser) {
        // 确定目标状态：如果没有指定targetStatus，默认设为已处理(2)
        String targetStatus = StringUtils.isNotBlank(operationBo.getTargetStatus()) ?
            operationBo.getTargetStatus() : CommConstants.CommonValStr.TWO;
        for (ScanProjectFile file:files){
            // 检查文件状态：只能处理已指派的文件（状态1或2）
            if (!CommConstants.CommonValStr.ONE.equals(file.getStatus()) && !CommConstants.CommonValStr.TWO.equals(file.getStatus())) {
                throw new ServiceException("只能处理已指派的文件");
            }
            // 检查权限：只能处理指派给自己的文件
            if (!currentUser.getUserId().equals(file.getHandleUserId())) {
                throw new ServiceException("只能处理指派给自己的文件");
            }
            // 检查状态是否需要变更
            if (file.getStatus().equals(targetStatus)) {
                throw new ServiceException("文件当前状态与目标状态相同，无需处理");
            }
            // 执行状态变更
            file.setStatus(targetStatus);
            if (CommConstants.CommonValStr.ONE.equals(targetStatus)) {
                // 设为未处理状态：清空处理时间和处理人
                file.setHandleTime(null);
                file.setHandleUser(null);
                file.setHandleUserId(null);
            } else {
                // 设为已处理状态：设置处理时间
                file.setHandleTime(new Date());
                file.setHandleUser(currentUser.getNickName());
                file.setHandleUserId(currentUser.getUserId());
            }
            // 设置处理说明
            if (StringUtils.isNotBlank(operationBo.getHandleRemark())) {
                file.setHandleRemark(operationBo.getHandleRemark());
            }
            file.setUpdateTime(new Date());
        }
    }

    /**
     * 查询扫描项目文件记录列表
     */
    @Override
    public List<ScanProjectFileVo> queryList(ScanProjectFileBo bo) {
        LambdaQueryWrapper<ScanProjectFile> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ScanProjectFile> buildQueryWrapper(ScanProjectFileBo bo) {
        LambdaQueryWrapper<ScanProjectFile> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getPId() != null, ScanProjectFile::getPId, bo.getPId());
        lqw.eq(bo.getScanVersion() != null, ScanProjectFile::getScanVersion, bo.getScanVersion());

        // 核心查询逻辑：查询最新扫描的文件(last_scan_flag=1)，以及以前处理过的文件(status=2)
        lqw.and(wrapper ->
            wrapper.eq(ScanProjectFile::getLastScanFlag, 1)
                   .or()
                   .eq((bo.getStatus()==null || "-1".equals(bo.getStatus())),ScanProjectFile::getStatus, "2")
        );

        lqw.like(StringUtils.isNotBlank(bo.getScanFileUrl()), ScanProjectFile::getScanFileUrl, bo.getScanFileUrl());
        lqw.eq(bo.getStatus() != null && !"-1".equals(bo.getStatus()), ScanProjectFile::getStatus, bo.getStatus());
        lqw.like(StringUtils.isNotBlank(bo.getHandleUser()), ScanProjectFile::getHandleUser, bo.getHandleUser());
        lqw.eq(bo.getHandleUserId() != null, ScanProjectFile::getHandleUserId, bo.getHandleUserId());
        lqw.like(StringUtils.isNotBlank(bo.getAssignUser()), ScanProjectFile::getAssignUser, bo.getAssignUser());
        lqw.eq(bo.getAssignUserId() != null, ScanProjectFile::getAssignUserId, bo.getAssignUserId());

        // 添加时间范围查询
        if (bo.getHandleTimeBegin() != null) {
            lqw.ge(ScanProjectFile::getHandleTime, Timestamp.valueOf(bo.getHandleTimeBegin()));
        }
        if (bo.getHandleTimeEnd() != null) {
            lqw.le(ScanProjectFile::getHandleTime, Timestamp.valueOf(bo.getHandleTimeEnd()));
        }
        if (bo.getAssignTimeBegin() != null) {
            lqw.ge(ScanProjectFile::getAssignTime, Timestamp.valueOf(bo.getAssignTimeBegin()));
        }
        if (bo.getAssignTimeEnd() != null) {
            lqw.le(ScanProjectFile::getAssignTime, Timestamp.valueOf(bo.getAssignTimeEnd()));
        }

        return lqw;
    }

    /**
     * 新增扫描项目文件记录
     */
    @Override
    public Boolean insertByBo(ScanProjectFileBo bo) {
        ScanProjectFile add = BeanUtil.toBean(bo, ScanProjectFile.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 新增扫描项目文件记录
     */
    @Override
    public Boolean insert(ScanProjectFile scanProjectFile) {
        return baseMapper.insert(scanProjectFile) > 0;
    }

    /**
     * 修改扫描项目文件记录
     */
    @Override
    public Boolean updateByBo(ScanProjectFileBo bo) {
        ScanProjectFile update = BeanUtil.toBean(bo, ScanProjectFile.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ScanProjectFile entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除扫描项目文件记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean updatePreviousScanToOld(Long pid, List<String> scanFileUrls) {
        return baseMapper.updatePreviousScanToOld(pid, scanFileUrls) > 0;
    }

    @Override
    public List<ScanProjectFileVo> selectByPidAndVersion(Long pid, Long scanVersion) {
        return baseMapper.selectByPidAndVersion(pid, scanVersion);
    }

    @Override
    public Long countByStatus(Long pid, String status) {
        return baseMapper.countByStatus(pid, status);
    }

    @Override
    public Boolean insertBatch(List<ScanProjectFile> scanProjectFileList) {
        return baseMapper.insertBatch(scanProjectFileList);
    }

    @Override
    public Boolean batchDeleteByPids(Collection<Long> deleteIds) {
        LambdaQueryWrapper<ScanProjectFile> wrapper = Wrappers.lambdaQuery();
        wrapper.in(ScanProjectFile::getPId, deleteIds);
        return baseMapper.delete(wrapper) > 0;
    }

    @Override
    public ScanProjectFile selectLatestByPidAndScanFileUrl(Long pid, String scanFileUrl) {
        return baseMapper.selectLatestByPidAndScanFileUrl(pid, scanFileUrl);
    }

    @Override
    public Boolean updateFileStatusToHandled(Long id, Date handleTime) {
        return baseMapper.updateFileStatusToHandled(id, handleTime) > 0;
    }

    /**
     * 为当前指派操作创建禅道任务
     *
     * @param fileIds    当前指派的文件ID列表
     * @param handleUserId 处理人ID
     * @param assignUser 指派人
     */
    private void createZentaoTaskForCurrentAssignment(List<Long> fileIds, Long handleUserId, String assignUser) {
        try {
            // 根据用户ID获取用户信息
            SysUser handleUser = sysUserService.selectUserById(handleUserId);
            if (handleUser == null) {
                log.error("无法找到用户ID为{}的用户信息，禅道任务创建失败", handleUserId);
                throw new ServiceException("处理人用户信息不存在");
            }

            // 查询当前指派的文件信息
            List<ScanProjectFileVo> assignedFiles = new ArrayList<>();
            for (Long fileId : fileIds) {
                ScanProjectFileVo fileVo = baseMapper.selectVoById(fileId);
                if (fileVo != null) {
                    assignedFiles.add(fileVo);
                }
            }
            //查找项目名称
            List<Project> projects = projectService.selectProjectByIds(assignedFiles.stream().map(ScanProjectFileVo::getPId).collect(Collectors.toList()));

            if (assignedFiles.isEmpty()) {
                log.info("没有找到指派的文件信息，不创建禅道任务");
                return;
            }

            // 生成任务名称
            String taskName = "代码质量问题修复任务" + System.currentTimeMillis();

            // 生成任务描述
            String description = generateTaskDescription(projects,assignedFiles, handleUser.getNickName(), assignUser);

            // 创建禅道任务（使用用户名）
            zentaoApiService.createTask(taskName, handleUser.getZtUserName(), description);

            log.info("为用户{}创建禅道任务成功，任务名称：{}", handleUser.getNickName(), taskName);

        } catch (Exception e) {
            log.error("为用户ID{}创建禅道任务失败", handleUserId, e);
            throw new ServiceException("用户ID"+handleUserId+";"+e.getMessage());
        }
    }

    /**
     * 生成禅道任务描述
     *
     * @param projects      项目列表
     * @param assignedFiles 指派的文件列表
     * @param handleUser    处理人用户名
     * @param assignUser    指派人
     * @return 任务描述
     */
    private String generateTaskDescription(List<Project> projects,List<ScanProjectFileVo> assignedFiles, String handleUser, String assignUser) {
        StringBuilder description = new StringBuilder();
        description.append("<p>您被指派对以下代码质量问题进行处理，具体问题明细请到绩效管理系统-【代码量统计-代码质量管理-问题分配及处理】查看详情。修复完以下问题后请到代码质量管理确认处理完成。</p>");

        // HTML表格开始
        description.append("<table border='1' style='border-collapse: collapse; width: 100%;'>");
        description.append("<thead>");
        description.append("<tr>");
        description.append("<th style='padding: 8px; text-align: left;'>代码库名称</th>");
        description.append("<th style='padding: 8px; text-align: left;'>问题文件路径</th>");
        description.append("<th style='padding: 8px; text-align: center;'>严重问题数量</th>");
        description.append("<th style='padding: 8px; text-align: center;'>一般问题数量</th>");
        description.append("<th style='padding: 8px; text-align: center;'>处理人</th>");
        description.append("<th style='padding: 8px; text-align: center;'>指派人</th>");
        description.append("<th style='padding: 8px; text-align: center;'>指派时间</th>");
        description.append("</tr>");
        description.append("</thead>");
        description.append("<tbody>");

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 添加每个文件的信息
        for (ScanProjectFileVo file : assignedFiles) {
            Project project = projects.stream().filter(p -> p.getPId().equals(file.getPId())).findFirst().orElse(null);
            description.append("<tr>");
            description.append("<td style='padding: 8px;'>");
            description.append(StrUtil.isNotBlank(project.getPName()) ? project.getPName() : "未知项目");
            description.append("</td>");
            description.append("<td style='padding: 8px;'>");
            description.append(file.getScanFileUrl() != null ? file.getScanFileUrl() : "");
            description.append("</td>");
            description.append("<td style='padding: 8px; text-align: center;'>");
            description.append(file.getBlockerAmount() != null ? file.getBlockerAmount() : 0);
            description.append("</td>");
            description.append("<td style='padding: 8px; text-align: center;'>");
            description.append(file.getCriticalAmount() != null ? file.getCriticalAmount() : 0);
            description.append("</td>");
            description.append("<td style='padding: 8px; text-align: center;'>");
            description.append(handleUser);
            description.append("</td>");
            description.append("<td style='padding: 8px; text-align: center;'>");
            description.append(assignUser);
            description.append("</td>");
            description.append("<td style='padding: 8px; text-align: center;'>");
            description.append(file.getAssignTime() != null ? dateFormat.format(file.getAssignTime()) : "");
            description.append("</td>");
            description.append("</tr>");
        }

        // HTML表格结束
        description.append("</tbody>");
        description.append("</table>");

        return description.toString();
    }

    /**
     * 执行文件操作（指派、更换指派）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean executeFileAssign(ScanProjectFileAssignBo operationBo) {
        // 获取当前登录用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("当前用户未登录");
        }
        SysUser currentUser = sysUserService.selectUserById(loginUser.getUserId());
        // 指派/更换指派：仅限技术经理(组长)
        if (!currentUser.isTechnicalManager()) {
            throw new ServiceException("仅限技术经理(组长),您没有权限执行指派操作");
        }
        if (operationBo.getHandleUserId() == null) {
            throw new ServiceException("指派操作时，处理人ID不能为空");
        }
        // 批量处理文件操作
        List<ScanProjectFile> files = baseMapper.selectBatchIds(operationBo.getFileIds());
        if (CollUtil.isEmpty(files) || files.size() != operationBo.getFileIds().size()) {
            throw new ServiceException("文件记录不存在");
        }
        //指派/更换指派
        executeAssignOperation(files, operationBo, currentUser);
        baseMapper.updateBatchById(files);
        try {
            //创建禅道任务
            createZentaoTaskForCurrentAssignment(operationBo.getFileIds(), operationBo.getHandleUserId(), currentUser.getNickName());
        } catch (Exception e) {
            log.error("创建禅道任务失败", e);
            throw new ServiceException("创建禅道任务失败,错误信息:"+e.getMessage());
        }
        return true;
    }

    /**
     * 执行文件操作（处理）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean executeFileProcess(ScanProjectFileProcessBo operationBo) {
        // 获取当前登录用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("当前用户未登录");
        }
        SysUser currentUser = sysUserService.selectUserById(loginUser.getUserId());
        // 处理：除技术总监、项目经理、测试人员外所有人
        if (currentUser.isJszxAdmin() || currentUser.isProjectManager() || currentUser.isTester()) {
            throw new ServiceException("您没有权限执行处理操作");
        }
        if (StringUtils.isNotBlank(operationBo.getTargetStatus())) {
            if (!CommConstants.CommonValStr.ONE.equals(operationBo.getTargetStatus()) &&
                !CommConstants.CommonValStr.TWO.equals(operationBo.getTargetStatus())) {
                throw new ServiceException("目标状态只能是1(未处理)或2(已处理)");
            }
        }
        // 批量处理文件操作
        List<ScanProjectFile> files = baseMapper.selectBatchIds(operationBo.getFileIds());
        if (CollUtil.isEmpty(files) || files.size() != operationBo.getFileIds().size()) {
            throw new ServiceException("文件记录不存在");
        }
        //处理
        executeHandleOperation(files, operationBo, currentUser);
        baseMapper.updateBatchById(files);
        return true;
    }

    @Override
    public Boolean batchSoftDeleteByProjectIds(Set<Long> projectIds) {
        if (CollUtil.isEmpty(projectIds)) {
            return true;
        }
        LambdaUpdateWrapper<ScanProjectFile> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.in(ScanProjectFile::getPId, projectIds)
            .set(ScanProjectFile::getLastScanFlag,CommConstants.CommonVal.ZERO)
                    .set(ScanProjectFile::getDelFlag, CommConstants.CommonVal.TWO);
        return baseMapper.update(null, updateWrapper) > 0;
    }
}
