package com.qmqb.imp.system.mapper.performance;

import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.performance.PerformanceFeedbackMainIndicatorResult;
import com.qmqb.imp.system.domain.vo.performance.PerformanceFeedbackMainIndicatorResultVo;
import java.util.List;

/**
 * 绩效反馈主表与绩效指标结果中间表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface PerformanceFeedbackMainIndicatorResultMapper extends BaseMapperPlus<PerformanceFeedbackMainIndicatorResultMapper, PerformanceFeedbackMainIndicatorResult, PerformanceFeedbackMainIndicatorResultVo> {
    /**
     * 根据主表ID查询所有指标结果ID
     * @param mainId 主表ID
     * @return 指标结果ID列表
     */
    List<Long> selectIndicatorResultIdsByMainId(Long mainId);
} 