-- 备份
CREATE TABLE tb_doc_stat_2024090901 LIKE gitlab_tool.tb_doc_stat;
INSERT INTO backup.tb_doc_stat_2024090901 SELECT * FROM gitlab_tool.tb_doc_stat WHERE syear = 2024;

-- 增加部门ID字段
ALTER TABLE tb_work_stat ADD COLUMN `work_group_id` bigint(20) DEFAULT NULL COMMENT '所属组ID' AFTER `work_username`;
ALTER TABLE tb_doc_stat ADD COLUMN `dept_id` bigint(20) DEFAULT NULL COMMENT '所属组ID' AFTER `smonth`;
-- 增加角色ID字段
ALTER TABLE tb_work_stat ADD COLUMN `work_role_id` bigint(20) DEFAULT NULL COMMENT '角色ID' AFTER `work_group`;

-- 工作统计表修数，补充部门ID
UPDATE tb_work_stat ws
INNER JOIN sys_user su ON ws.work_username = su.nick_name AND su.del_flag = 0
SET ws.work_group_id = su.dept_id
WHERE ws.work_year = 2024;
-- 工作统计表修数，补充角色ID
UPDATE tb_work_stat ws
INNER JOIN sys_role sr ON ws.work_role = sr.role_name AND sr.del_flag = 0 AND sr.`status` = 0
SET ws.work_role_id = sr.role_id
WHERE ws.work_year = 2024;

-- 文档统计表修数，删除2024年数据
DELETE FROM tb_doc_stat WHERE syear = 2024;
