package com.qmqb.imp.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 绩效报告类型枚举
 *
 * <AUTHOR>
 * @since 2025-06-25 15:53
 */
@Getter
@AllArgsConstructor
public enum PerformanceReportTypeEnum {

    /**
     * 个人绩效报告
     */
    PERSON("0", "个人"),

    /**
     * 小组绩效报告
     */
    GROUP("1", "小组"),

    /**
     * 岗位绩效报告
     */
    POST("2", "岗位");

    private final String code;
    private final String name;

    /**
     * 根据code获取枚举实例
     * @param code 类型编码
     * @return 对应的枚举实例
     * @throws IllegalArgumentException 如果code无效
     */
    public static PerformanceReportTypeEnum getByCode(String code) {
        if (StringUtils.isBlank(code)) {
            throw new IllegalArgumentException("绩效报告类型编码不能为空");
        }

        return Arrays.stream(values())
            .filter(e -> e.getCode().equals(code.trim()))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("无效的绩效报告类型编码: " + code));
    }

}
