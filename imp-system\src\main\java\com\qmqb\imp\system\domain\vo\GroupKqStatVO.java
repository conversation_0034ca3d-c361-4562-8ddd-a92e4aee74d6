package com.qmqb.imp.system.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 组内绩效分析
 * @date 2025/5/6 19:47
 */
@Data
@Builder
public class GroupKqStatVO {

    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "每组最长工时和最短工时相差小时数")
    private Integer groupMaxWorkTimeGap;

    @Schema(description = "每组平均工时小于8小时的人数统计")
    private Integer groupAvgWorkTimeLtEightHourCount;

    public GroupKqStatVO() {
        groupMaxWorkTimeGap = 0;
        groupAvgWorkTimeLtEightHourCount = 0;
    }

    public GroupKqStatVO(Long deptId, Integer groupMaxWorkTimeGap, Integer groupAvgWorkTimeLtEightHourCount) {
        this.deptId = deptId;
        this.groupMaxWorkTimeGap = groupMaxWorkTimeGap == null ? 0 : groupMaxWorkTimeGap;
        this.groupAvgWorkTimeLtEightHourCount = groupAvgWorkTimeLtEightHourCount == null ? 0 : groupAvgWorkTimeLtEightHourCount;
    }
}
