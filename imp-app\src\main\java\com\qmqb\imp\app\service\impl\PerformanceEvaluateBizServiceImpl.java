package com.qmqb.imp.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.qmqb.imp.app.domain.bo.PerformanceEvaluateBizBO;
import com.qmqb.imp.app.domain.bo.SubmitEvaluateBO;
import com.qmqb.imp.app.domain.vo.KeyValueVO;
import com.qmqb.imp.app.domain.vo.PerformanceEvaluateBizVO;
import com.qmqb.imp.app.domain.vo.PerformanceReportVO;
import com.qmqb.imp.app.service.HomepageService;
import com.qmqb.imp.app.service.PerformanceEvaluateBizService;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.system.domain.PerformanceEvaluate;
import com.qmqb.imp.system.domain.bo.PerformanceEvaluateBo;
import com.qmqb.imp.system.service.IPerformanceEvaluateService;
import com.qmqb.imp.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PerformanceEvaluateBizServiceImpl implements PerformanceEvaluateBizService {

    private final ISysUserService sysUserService;
    private final IPerformanceEvaluateService performanceEvaluateService;
    private final HomepageService homepageService;

    /**
     * 人员列表
     *
     * @return
     */
    @Override
    public R<List<KeyValueVO>> peopleSelect() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        Long userId = loginUser.getUserId();
        SysUser sysUser = sysUserService.selectUserById(userId);
        List<SysUser> sysUsers;
        if (sysUser.isAdmin() || sysUser.isJszxAdmin()) {
            sysUsers = sysUserService.selectAllUser();
        } else {
            sysUsers = sysUserService.selectUserByDeptId(loginUser.getDeptId());
        }
        List<KeyValueVO> vos = sysUsers.stream().filter(e -> !Objects.equals(e.getUserId(), userId)).map(o -> KeyValueVO.builder().key(o.getNickName()).value(String.valueOf(o.getUserId())).build()).collect(Collectors.toList());
        return R.ok(vos);
    }

    /**
     * 个人绩效报告
     *
     * @param bo
     * @return
     */
    @Override
    public R<PerformanceReportVO> personalPerformanceReport(PerformanceEvaluateBizBO bo) {
        return homepageService.personalPerformanceReport(bo, bo.getUserId());
    }

    /**
     * 点评详情
     *
     * @return
     */
    @Override
    public R<PerformanceEvaluateBizVO> detail(PerformanceEvaluateBizBO bo) {
        PerformanceEvaluate evaluate = performanceEvaluateService.queryLatestByUserIdAndEvaluateTime(bo.getUserId(), DateUtil.beginOfMonth(bo.getDate()), DateUtil.endOfMonth(bo.getDate()));
        if (Objects.isNull(evaluate)) {
            return R.ok();
        }
        PerformanceEvaluateBizVO vo = BeanUtil.copyProperties(evaluate, PerformanceEvaluateBizVO.class);
        vo.setEvaluateTime(DateUtil.format(evaluate.getEvaluateTime(), DatePattern.CHINESE_DATE_PATTERN));
        return R.ok(vo);
    }

    /**
     * 提交点评
     *
     * @param bo
     * @return
     */
    @Override
    public R<Void> submit(SubmitEvaluateBO bo) {
        PerformanceEvaluateBo evaluate = new PerformanceEvaluateBo();
        evaluate.setUserId(bo.getUserId());
        SysUser sysUser = sysUserService.selectUserById(bo.getUserId());
        evaluate.setNickName(sysUser.getNickName());
        Long userId = LoginHelper.getUserId();
        evaluate.setEvaluateUserId(userId);
        SysUser currentUser = sysUserService.selectUserById(userId);
        evaluate.setEvaluator(currentUser.getNickName());
        evaluate.setEvaluateContent(bo.getContent());
        evaluate.setEvaluateTime(DateTime.now());
        Boolean inserted = performanceEvaluateService.insertByBo(evaluate);
        return inserted ?  R.ok() : R.fail();
    }
}
