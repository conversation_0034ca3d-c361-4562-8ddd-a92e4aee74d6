package com.qmqb.imp.system.domain.performance;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 绩效事件对象 tb_performance_event
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_performance_event")
public class PerformanceEvent extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 反馈编码
     */
    private String feedbackCode;
    /**
     * 年份
     */
    private Integer year;
    /**
     * 月份
     */
    private Integer month;
    /**
     * 反馈时间
     */
    private Date feedbackTime;
    /**
     * 事件标题
     */
    private String eventTitle;
    /**
     * 事件明细
     */
    private String eventDetail;
    /**
     * 事件发生时间-开始
     */
    private Date eventStartTime;
    /**
     * 事件发生时间-结束
     */
    private Date eventEndTime;
    /**
     * 数据来源
     */
    private String dataSource;
    /**
     * 提交状态
     */
    private String submitStatus;
    /**
     * 提交时间
     */
    private Date submitTime;
    /**
     * 提交人
     */
    private String submitter;
    /**
     * 项管审核状态
     */
    private String projectManagerAuditStatus;
    /**
     * 项管审核人
     */
    private String projectManagerAuditor;
    /**
     * 最终审核状态
     */
    private String finalAudit;
    /**
     * 备注
     */
    private String remark;

}
