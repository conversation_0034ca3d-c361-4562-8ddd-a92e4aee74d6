package com.qmqb.imp.job.indicator.indicators.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.process.ProcessSystemAlarm;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.mapper.process.ProcessSystemAlarmMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-07-04 10:04
 * 月生产故障数
 */
@Service
public class SystemAlarmIndicatorServiceImpl implements IndicatorLevelCalcService {


    /**
     * S:无故障
     */
    private static final int S_SYSTEM_ALARM_NUM = 0;
    /**
     * A:故障小于2且未造成重大事故
     */
    private static final int A_SYSTEM_ALARM_NUM = 2;
    /**
     * B:故障小于3且未造成重大事故
     */
    private static final int B_SYSTEM_ALARM_NUM = 3;
    /**
     * C:有以下情况之一：
     * 故障数大于3
     * 造成重大事故
     */
    private static final int C_SYSTEM_ALARM_NUM = 3;
    /**
     * D:有以下情况之一：
     * 连续2月故障数大于3
     * 造成重大事故(管理部通报）
     */
    private static final int D_SYSTEM_ALARM_NUM = 3;

    @Resource
    ProcessSystemAlarmMapper processSystemAlarmMapper;

    @Override
    public String getIndicatorCode() {
        return PerformanceIndicatorEnum.PROD_FAULT.getCode();
    }

    @Override
    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults) {
        // 1. 获取当前月和上个月
        YearMonth currentMonth = YearMonth.of(workResult.getWorkYear(), workResult.getWorkMonth());
        YearMonth previousMonth = currentMonth.minusMonths(1);
        // 2. 查询两个月的生产故障
        List<ProcessSystemAlarm> currentMonthAlarms = getAlarms(nickName, currentMonth);
        List<ProcessSystemAlarm> previousMonthAlarms = getAlarms(nickName, previousMonth);
        int currentMonthAlarmNum = currentMonthAlarms.size();
        int previousMonthAlarmNum = previousMonthAlarms.size();
        // 3.评级逻辑
        if (currentMonthAlarmNum > D_SYSTEM_ALARM_NUM && previousMonthAlarmNum > D_SYSTEM_ALARM_NUM) {
            return ScoreLevelEnum.SCORE_D.getCode();
        } else if (currentMonthAlarmNum > C_SYSTEM_ALARM_NUM) {
            return ScoreLevelEnum.SCORE_C.getCode();
        }else if (currentMonthAlarmNum == S_SYSTEM_ALARM_NUM) {
            return ScoreLevelEnum.SCORE_S.getCode();
        } else if (currentMonthAlarmNum < A_SYSTEM_ALARM_NUM) {
            return ScoreLevelEnum.SCORE_A.getCode();
        } else {
            return ScoreLevelEnum.SCORE_B.getCode();
        }
    }

    /**
     * 获取指定月份的生产故障
     * @param nickName
     * @param yearMonth
     * @return
     */
    private List<ProcessSystemAlarm> getAlarms(String nickName, YearMonth yearMonth) {
        LocalDate startDate = yearMonth.atDay(1);
        LocalDate endDate = yearMonth.atEndOfMonth();
        String startTime = startDate.toString() + " 00:00:00";
        String endTime = endDate.toString() + " 23:59:59";
        List<ProcessSystemAlarm> processSystemAlarms = processSystemAlarmMapper.selectList(new LambdaQueryWrapper<ProcessSystemAlarm>()
            .between(ProcessSystemAlarm::getOriginatorTime, startTime, endTime)
            .and(wrapper -> wrapper
                    .eq(ProcessSystemAlarm::getManagerConfirm, nickName)
                    .or()
                    .eq(ProcessSystemAlarm::getTestConfirm, nickName)
                    .or()
                    .eq(ProcessSystemAlarm::getDevRepair, nickName)
                )
        );
        return processSystemAlarms;
    }

    @Override
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level) {
        Integer year = workResult.getWorkYear();
        Integer month = workResult.getWorkMonth();
        // 1. 获取当前月和上个月
        YearMonth currentMonth = YearMonth.of(year, month);
        YearMonth previousMonth = currentMonth.minusMonths(1);
        // 2. 查询两个月的生产故障
        List<ProcessSystemAlarm> currentMonthAlarms = getAlarms(nickName, currentMonth);
        List<ProcessSystemAlarm> previousMonthAlarms = getAlarms(nickName, previousMonth);
        int currentMonthAlarmNum = currentMonthAlarms.size();
        int previousMonthAlarmNum = previousMonthAlarms.size();

        StringBuilder logContent = new StringBuilder();
        logContent.append(String.format("[%s]在%s年%s月份生产故障数：%d个",
            nickName, year, month, currentMonthAlarmNum));
        // 添加评级信息
        logContent.append(String.format("，评级：%s", level));
        // 添加评级原因
        String reason = getRatingReason(currentMonthAlarmNum, previousMonthAlarmNum, level);
        if (reason != null) {
            logContent.append(String.format("，原因：%s", reason));
        }
        return logContent.toString();
    }

    /**
     * 获取评级原因
     */
    private String getRatingReason(int currentMonthAlarmNum, int previousMonthAlarmNum, String level) {
        switch (level) {
            case "S":
                return String.format("故障数(%d)达到S级标准(=0)", currentMonthAlarmNum);
            case "A":
                return String.format("故障数(%d)达到A级标准(<%d)", currentMonthAlarmNum, A_SYSTEM_ALARM_NUM);
            case "B":
                return String.format("故障数(%d)达到B级标准(<%d)", currentMonthAlarmNum, B_SYSTEM_ALARM_NUM);
            case "C":
                return String.format("故障数(%d)超过C级标准(>%d)", currentMonthAlarmNum, C_SYSTEM_ALARM_NUM);
            case "D":
                return String.format("连续2个月故障数(%d,%d)超过D级标准(>%d)", previousMonthAlarmNum, currentMonthAlarmNum, D_SYSTEM_ALARM_NUM);
            default:
                return null;
        }
    }
}
