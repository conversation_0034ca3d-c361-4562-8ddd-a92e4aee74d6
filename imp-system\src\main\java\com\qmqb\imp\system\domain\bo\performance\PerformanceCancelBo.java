package com.qmqb.imp.system.domain.bo.performance;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @className: PerformanceCancelBo
 * @author: ya<PERSON><PERSON><PERSON><PERSON>
 * @description:
 * @date: 2025/8/11 17:18
 * @version: 1.0
 */
@Data
public class PerformanceCancelBo {
    @NotEmpty(message = "待取消ID不能为空")
    private List<Long> ids;
    private String reason;
}
