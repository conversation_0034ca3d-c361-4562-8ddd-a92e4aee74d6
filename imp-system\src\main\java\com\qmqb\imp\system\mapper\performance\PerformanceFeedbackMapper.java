package com.qmqb.imp.system.mapper.performance;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.bo.performance.PerformanceFeedbackBo;
import com.qmqb.imp.system.domain.performance.PerformanceFeedback;
import com.qmqb.imp.system.domain.performance.PerformanceFeedbackMain;
import com.qmqb.imp.system.domain.vo.performance.PerformanceFeedbackVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 绩效反馈Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface PerformanceFeedbackMapper extends BaseMapperPlus<PerformanceFeedbackMapper, PerformanceFeedback, PerformanceFeedbackVo> {

    /**
     * 连表查询绩效反馈明细及主表信息
     *
     * @param bo 查询条件
     * @return 绩效反馈明细VO列表
     */
    List<PerformanceFeedbackVo> selectVoJoinMainList(@Param("bo") PerformanceFeedbackBo bo);
    /**
     * 连表查询绩效反馈明细及主表已审批信息
     *
     * @param bo 查询条件
     * @return 绩效反馈明细VO列表
     */
    List<PerformanceFeedback> selectVoApprovalJoinMainList(@Param("bo") PerformanceFeedbackBo bo);

    /**
     * 连表分页查询绩效反馈明细及主表信息
     *
     * @param page 分页参数
     * @param bo   查询条件
     * @return 绩效反馈明细VO分页
     */
    Page<PerformanceFeedbackVo> selectVoJoinMainPage(IPage<?> page, @Param("bo") PerformanceFeedbackBo bo);

    /**
     * 根据主表ID批量删除子表
     *
     * @param mainFeedbackIds 主表ID集合
     * @return 删除条数
     */
    int deleteByMainFeedbackIds(@Param("mainFeedbackIds") List<Long> mainFeedbackIds);

}
