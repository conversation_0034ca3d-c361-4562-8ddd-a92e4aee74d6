package com.qmqb.imp.system.domain.bo.performance;

import com.qmqb.imp.common.core.domain.BaseEntity;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 绩效反馈主表业务对象 tb_performance_feedback_main
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PerformanceFeedbackMainBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 事件ID
     */
    private Long eventId;

    /**
     * 反馈编码
     */
    private String feedbackCode;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 月份
     */
    private Integer month;

    /**
     * 反馈时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackTime;

    /**
     * 反馈时间-起
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackTimeBegin;
    /**
     * 反馈时间-止
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackTimeEnd;

    /**
     * 一类指标
     */
    private String primaryIndicator;

    /**
     * 二类指标
     */
    private String secondaryIndicator;

    /**
     * 事件标题
     */
    @NotBlank(message = "事件标题不能为空", groups = {AddGroup.class, EditGroup.class})
    private String eventTitle;

    /**
     * 事件明细
     */
    @NotBlank(message = "事件明细不能为空", groups = {AddGroup.class, EditGroup.class})
    private String eventDetail;

    /**
     * 事件发生时间-开始
     */
    @NotNull(message = "事件发生时间-开始不能为空", groups = {AddGroup.class, EditGroup.class})
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eventStartTime;

    /**
     * 事件发生时间-开始-起
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eventStartTimeBegin;
    /**
     * 事件发生时间-开始-止
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eventStartTimeEnd;

    /**
     * 事件发生时间-结束
     */
    @NotNull(message = "事件发生时间-结束不能为空", groups = {AddGroup.class, EditGroup.class})
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eventEndTime;
    /**
     * 事件发生时间-结束-起
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eventEndTimeBegin;
    /**
     * 事件发生时间-结束-止
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eventEndTimeEnd;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 提交状态
     */
    private String submitStatus;

    /**
     * 提交时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

    /**
     * 提交时间-起
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTimeBegin;
    /**
     * 提交时间-止
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTimeEnd;

    /**
     * 提交人
     */
    private String submitter;

    /**
     * 项管审核状态
     */
    private String projectManagerAuditStatus;

    /**
     * 项管审核人
     */
    private String projectManagerAuditor;

    /**
     * 项管审核时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date projectManagerAuditTime;

    /**
     * 项管审核时间-起
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date projectManagerAuditTimeBegin;
    /**
     * 项管审核时间-止
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date projectManagerAuditTimeEnd;

    /**
     * 项管审核备注
     */
    private String projectManagerRemark;

    /**
     * 最终审核状态
     */
    private String finalAudit;

    /**
     * 最终审核时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finalAuditTime;

    /**
     * 最终审核时间-起
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finalAuditTimeBegin;
    /**
     * 最终审核时间-止
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finalAuditTimeEnd;

    /**
     * 最终审核备注
     */
    private String finalRemark;

    /**
     * 备注
     */
    private String remark;

    @Valid
    @NotEmpty(message = "反馈列表不能为空", groups = {AddGroup.class, EditGroup.class})
    private List<PerformanceFeedbackBo> feedbackList;
}
