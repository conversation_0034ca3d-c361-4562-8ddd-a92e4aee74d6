package com.qmqb.imp.system.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 17:59 2025/5/13
 * @Description TODO
 * @MethodName
 * @return null
 */
@Data
public class GitStatisticsInfoExportVO {

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 提交编号
     */
    @ExcelProperty(value = "提交编号")
    private String gitShortId;

    /**
     * 代码库
     */
    @ExcelProperty(value = "代码库")
    private String project;

    /**
     * 分支
     */
    @ExcelProperty(value = "分支")
    private String branch;

    /**
     * 提交注释
     */
    @ExcelProperty(value = "提交说明")
    private String message;

    /**
     * 提交人
     */
    @ExcelProperty(value = "提交人姓名")
    private String committer;

    /**
     * 负责开发组名称
     */
    @ExcelProperty(value = "负责开发组")
    private String pDevDeptName;

    /**
     * 负责测试组名称
     */
    @ExcelProperty(value = "负责测试组")
    private String pTestDeptName;

/**
     * 大类业务名称
     */
    @ExcelProperty(value = "大类业务")
    private String pBroadBusinessName;

    /**
     * 小类业务名称
     */
    @ExcelProperty(value = "小类业务")
    private String pNarrowBusinessName;

    /**
     * 作者上传时间
     */
    @ExcelProperty(value = "提交时间")
    private Date commitDate;

    /**
     * 增加行数
     */
    @ExcelProperty(value = "增加行数")
    private Integer additionsLine;

    /**
     * 删除行数
     */
    @ExcelProperty(value = "删除行数")
    private Integer deleteLine;

    /**
     * 总修改行数
     */
    @ExcelProperty(value = "调整总行数")
    private Integer totalLine;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "采集时间")
    private Date createTime;
}
