package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 发布版本记录对象 tb_release_records
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@EqualsAndHashCode()
@TableName("tb_release_records")
public class ReleaseRecord {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 成果编号
     */
    private String resultCode;

    /**
     * 成果类型：1新系统、2重要功能模块、3一般功能模块、4事项支撑、5其它
     */
    private String resultType;

    /**
     * 数据来源：1禅道同步、2手工添加
     */
    private String dataSource;

    /**
     * 所属业务大类：1国内、2海外
     */
    private String businessCategoryMajor;

    /**
     * 所属业务小类：1风控、2营销、3资金、4资产、5贷后、6自营业务、7综合、8其它
     */
    private String businessCategoryMinor;

    /**
     * 耗时天数
     */
    private Integer durationDays;

    /**
     * 耗费人力
     */
    private Integer manpowerCost;

    /**
     * 主要开发组id,多个用逗号分隔
     */
    private String devDepts;

    /**
     * 测试组id,多个用逗号分隔
     */
    private String testDepts;

    /**
     * 其它组id,多个用逗号分隔
     */
    private String otherDepts;

    /**
     * 成果标题
     */
    private String resultTitle;

    /**
     * 成果简介,取发布单关联的所有需求名称，多个需求则进行分行展示
     */
    private String resultSummary;

    /**
     * 负责项目经理
     */
    private String projectManager;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 关联zt_release表ID
     */
    private Integer sourceReleaseId;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private Integer delFlag;
} 