package com.qmqb.imp.system.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 项目成果邮件导出VO
 *
 * <AUTHOR>
 * @date 2025-08-16
 */
@Data
public class ProjectResultEmailExportVo {

    /**
     * 序号
     */
    @ExcelProperty(value = "序号")
    private Integer serialNumber;

    /**
     * 项目/任务名称
     */
    @ExcelProperty(value = "项目/任务名称")
    private String projectTaskName;

    /**
     * 所属业务大类：1国内、2海外
     */
    @ExcelProperty(value = "所属业务大类")
    private String businessCategoryMajor;

    /**
     * 所属业务小类：1风控、2营销、3资金、4资产、5贷后、6自营业务、7综合、8其它
     */
    @ExcelProperty(value = "所属业务小类")
    private String businessCategoryMinor;

    /**
     * 产品负责人
     */
    @ExcelProperty(value = "产品负责人")
    private String productManagers;

    /**
     * 业务负责人
     */
    @ExcelProperty(value = "业务负责人")
    private String projectManagers;

    /**
     * 项目里程碑
     */
    @ExcelProperty(value = "项目里程碑")
    private String milestone;


}
