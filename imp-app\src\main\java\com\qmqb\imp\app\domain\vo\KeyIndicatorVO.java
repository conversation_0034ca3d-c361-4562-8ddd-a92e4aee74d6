package com.qmqb.imp.app.domain.vo;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 关键指标视图对象
 *
 * <AUTHOR>
 */

@Data
@Builder
public class KeyIndicatorVO {

    /**
     * 组指标集合
     */
    private List<Indicator> group;

    @Data
    @Builder
    public static class Indicator {
        /**
         * 姓名|部门
         */
        private String name;
        /**
         * 年
         */
        private Integer year;
        /**
         * 月
         */
        private Integer month;
        /**
         * 值
         */
        private BigDecimal value;
        /**
         * 个人指标集合
         */
        private List<Indicator> personal;
    }

}
