package com.qmqb.imp.web.controller.business;

import java.util.List;

import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.job.service.TaskStatisticService;
import com.qmqb.imp.system.domain.vo.TaskStatisVO;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.system.service.ITaskStatService;

/**
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/taskStat")
public class TaskStatController extends BaseController {

    private final ITaskStatService iTaskStatService;

    private final TaskStatisticService taskStatisticService;

    /**
     * 统计技术中心任务数
     * @param year
     * @return
     */
    @GetMapping("/statByYear")
    public R<List<TaskStatisVO>> list(String year) {
        if (StringUtils.isBlank(year)) {
            throw new IllegalArgumentException("年份不能为空");
        }
        return R.ok(iTaskStatService.selectTaskStatByYear(year));
    }



}
