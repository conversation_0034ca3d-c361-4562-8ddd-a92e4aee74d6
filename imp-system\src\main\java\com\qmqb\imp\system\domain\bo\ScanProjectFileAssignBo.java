package com.qmqb.imp.system.domain.bo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 指派/更换指派扫描项目文件操作业务对象
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
public class ScanProjectFileAssignBo {

    /**
     * 文件ID列表（支持批量操作）
     */
    @NotEmpty(message = "文件ID列表不能为空")
    private List<Long> fileIds;

    /**
     * 处理人ID（指派时必填）
     */
    private Long handleUserId;

}
