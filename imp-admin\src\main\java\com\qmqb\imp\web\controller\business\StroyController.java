package com.qmqb.imp.web.controller.business;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.dto.StoryQueryDTO;
import com.qmqb.imp.common.core.domain.dto.StoryStatQueryDTO;
import com.qmqb.imp.system.domain.vo.StoryStatVO;
import com.qmqb.imp.system.domain.vo.StoryVO;
import com.qmqb.imp.system.service.IStoryService;
import com.qmqb.imp.system.service.StoryStatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 禅道需求查询
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@RestController
@RequestMapping("/story/query")
public class StroyController extends BaseController {


    @Autowired
    private IStoryService storyService;

    @Autowired
    private StoryStatService storyStatService;

    @GetMapping("/page")
    public R<Page<StoryVO>> page(StoryQueryDTO request) {
        return R.ok(storyService.page(request));
    }

    /**
     * 查询禅道需求统计
     */
    @GetMapping("/storyStat")
    public R<List<StoryStatVO>> getStoryStat(StoryStatQueryDTO dto) {
        return R.ok(storyStatService.getStoryStat(dto));
    }
}
