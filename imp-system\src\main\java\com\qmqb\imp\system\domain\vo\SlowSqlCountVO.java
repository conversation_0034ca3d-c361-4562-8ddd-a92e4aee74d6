package com.qmqb.imp.system.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025-05-27 19:46
 */

@Data
public class SlowSqlCountVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "表名")
    private String dbName;

    @Schema(description = "年份")
    private Integer year;

    @Schema(description = "月份")
    private Integer month;

    @Schema(description = "慢SQL数量")
    private Integer slowSqlCount;
}
