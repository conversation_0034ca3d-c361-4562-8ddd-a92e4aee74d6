package com.qmqb.imp.web.controller.business;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.bo.ScanProjectDetailBo;
import com.qmqb.imp.system.domain.vo.ScanProjectDetailVo;
import com.qmqb.imp.system.service.IScanProjectDetailService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 扫描项目记录详情
 *
 * <AUTHOR>
 * @date 2024-12-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/scanProjectDetail")
public class ScanProjectDetailController extends BaseController {

    private final IScanProjectDetailService iScanProjectDetailService;

    /**
     * 查询扫描项目记录详情列表
     */
    @SaCheckPermission("system:scanProjectDetail:list")
    @GetMapping("/list")
    public TableDataInfo<ScanProjectDetailVo> list(ScanProjectDetailBo bo, PageQuery pageQuery) {
        return iScanProjectDetailService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出扫描项目记录详情列表
     */
    @SaCheckPermission("system:scanProjectDetail:export")
    @Log(title = "扫描项目记录详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ScanProjectDetailBo bo, HttpServletResponse response) {
        List<ScanProjectDetailVo> list = iScanProjectDetailService.queryList(bo);
        ExcelUtil.exportExcel(list, "扫描项目记录详情", ScanProjectDetailVo.class, response);
    }

    /**
     * 获取扫描项目记录详情详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:scanProjectDetail:query")
    @GetMapping("/{id}")
    public R<ScanProjectDetailVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iScanProjectDetailService.queryById(id));
    }
}
