package com.qmqb.imp.system.domain.bo;

import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 【请填写功能名称】业务对象 zt_storyspec
 *
 * <AUTHOR>
 * @date 2025-08-14
 */

@Data
public class ZtStoryspecBo {

    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Integer story;

    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String title;

    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String spec;

    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String verify;

    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String files;

    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docs;

    @NotBlank(message = "不能为空", groups = { AddGroup.class, EditGroup.class })
    private String docVersions;


}
