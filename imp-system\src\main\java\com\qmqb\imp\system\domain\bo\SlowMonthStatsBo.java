package com.qmqb.imp.system.domain.bo;

import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 当月慢SQL统计业务对象 t_slow_month_stats
 *
 * <AUTHOR>
 * @date 2025-06-26
 */

@Data
public class SlowMonthStatsBo extends BaseEntity {

    /**
     * 库名
     */
    private String dbName;
    /**
     * 客户端IP
     */
    private String ip;
    /**
     * 告警级别（P0、P1、P2）
     */
    private String warnLevel;
    /**
     * 慢查询规则标识<br>
     * returnNull：返回空值<br>
     * returnRowCounts：返回记录数过多<br>
     * parseRowCounts：解析记录数过多<br>
     * query_time：查询时间过长<br>
     * query_hot：慢查询且频繁
     */
    private String slowRule;
    /**
     * 指派人
     */
    private String assigner;
    /**
     * 状态：0未指派、1已指派未处理、2已指派已处理、3无需处理
     */
    private String status;
    /**
     * 处理人
     */
    private String processer;

    /**
     * 指派时间(开始)
     */
    private LocalDateTime assignTimeStart;

    /**
     * 指派时间(结束)
     */
    private LocalDateTime assignTimeEnd;

    /**
     * 处理时间(开始)
     */
    private LocalDateTime processTimeStart;

    /**
     * 处理时间(结束)
     */
    private LocalDateTime processTimeEnd;
    /**
     * 创建时间(开始)
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间(结束)
     */
    private LocalDateTime createTimeEnd;
    /**
     * 处理人id
     */
    private Long processerId;
    /**
     * id列表
     */
    private List<Long> idList;
    /**
     * db列表
     */
    private List<String> dbList;
    /**
     * 执行年
     */
    private String year;
    /**
     * 执行月
     */
    private String month;
}
