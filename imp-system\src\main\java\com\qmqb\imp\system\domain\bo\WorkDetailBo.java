package com.qmqb.imp.system.domain.bo;

import com.qmqb.imp.common.core.domain.dto.BasePageDTO;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.core.validate.QueryGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2025-04-23 14:45
 */
@Data
@Schema(description = "个人工作明细请求参数")
public class WorkDetailBo extends BasePageDTO {

    @Schema(description = "查询年份")
    @NotNull(message = "查询年份不能为空", groups = {QueryGroup.class})
    private Integer evalYear;

    @Schema(description = "查询月份")
    private Integer evalMonth;

    @Schema(description = "查询人")
    @NotBlank(message = "查询人不能为空", groups = {QueryGroup.class})
    private String workUsername;

    @Schema(description = "禅道名称")
    private String ztUserName;

    @Schema(description = "git提交人名")
    private String gitCommitterName;

}
