package com.qmqb.imp.system.domain.vo.performance;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 绩效点评主视图对象 tb_performance
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@ExcelIgnoreUnannotated
public class PerformanceVo {

    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 员工昵称
     */
    @ExcelProperty(value = "员工昵称")
    private String nickName;

    /**
     * 绩效年份
     */
    @ExcelProperty(value = "绩效年份")
    private Integer year;

    /**
     * 绩效月份，格式MM
     */
    @ExcelProperty(value = "绩效月份，格式MM")
    private Integer month;

    /**
     * 所属组ID
     */
    @ExcelProperty(value = "所属组ID")
    private Long groupId;

    /**
     * 所属组
     */
    @ExcelProperty(value = "所属组")
    private String groupName;

    /**
     * 角色ID
     */
    @ExcelProperty(value = "角色ID")
    private Long roleId;

    /**
     * 角色
     */
    @ExcelProperty(value = "角色")
    private String role;

    /**
     * 总评等级S/A/B/C/D
     */
    @ExcelProperty(value = "总评等级S/A/B/C/D")
    private String totalLevel;

    /**
     * 评审绩效等级S/A/B/C/D
     */
    @ExcelProperty(value = "评审绩效等级S/A/B/C/D")
    private String reviewLevel;

    /**
     * 最终绩效等级S/A/B/C/D
     */
    @ExcelProperty(value = "最终绩效等级S/A/B/C/D")
    private String finalLevel;

    /**
     * 核准时间
     */
    @ExcelProperty(value = "核准时间")
    private Date approvalTime;

    /**
     * 是否邮件报送，0为不发，1为发送
     */
    @ExcelProperty(value = "是否邮件报送，0为不发，1为发送")
    private String emailSentFlag;

    /**
     * 邮件报送时间
     */
    @ExcelProperty(value = "邮件报送时间")
    private Date emailSentTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;


}
