package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 慢sql日志
 * @date 2025/5/28 16:15
 */
@Data
@TableName("t_slow_query_log")
public class SlowQueryLog {

    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 该字段用于记录数据库的名称。
     */
    @TableField("DBName")
    private String dbName;

    /**
     * 该字段记录执行查询的 IP 地址。
     */
    @TableField("IP")
    private String ip;

    /**
     * 该字段记录执行查询的用户。
     */
    @TableField("User")
    private String user;

    /**
     * 该字段用于记录查询开始的时间，使用 LocalDateTime 类型来避免时区问题。
     */
    @TableField("ExecutionStartTime")
    private LocalDateTime executionStartTime;

    /**
     * 该字段记录查询解析的行数。
     */
    @TableField("ParseRowCounts")
    private String parseRowCounts;

    /**
     * 该字段记录查询返回的行数。
     */
    @TableField("ReturnRowCounts")
    private String returnRowCounts;

    /**
     * 该字段记录查询执行的时间（单位：毫秒）。
     */
    @TableField("QueryTimeMS")
    private Long queryTimeMs;

    /**
     * 该字段用于存储 SQL 查询的哈希值，便于快速检索和匹配。
     */
    @TableField("SQLHash")
    private String sqlHash;

    /**
     * 该字段存储实际执行的 SQL 查询文本。
     */
    @TableField("SQLText")
    private String sqlText;
}
