package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.domain.BusinessType;
import com.qmqb.imp.system.domain.ProjectResult;
import com.qmqb.imp.system.domain.bo.BusinessTypeBo;
import com.qmqb.imp.system.domain.vo.BusinessTypeVo;
import com.qmqb.imp.system.mapper.BusinessTypeMapper;
import com.qmqb.imp.system.mapper.ProjectResultMapper;
import com.qmqb.imp.system.service.IBusinessTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 业务类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@RequiredArgsConstructor
@Service
public class BusinessTypeServiceImpl implements IBusinessTypeService {

    private final BusinessTypeMapper baseMapper;

    private final ProjectResultMapper projectResultMapper;

    /**
     * 查询业务类型
     */
    @Override
    public BusinessTypeVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询业务类型列表
     */
    @Override
    public TableDataInfo<BusinessTypeVo> queryPageList(BusinessTypeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BusinessType> lqw = buildQueryWrapper(bo);
        Page<BusinessTypeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询业务类型列表
     */
    @Override
    public List<BusinessTypeVo> queryList(BusinessTypeBo bo) {
        LambdaQueryWrapper<BusinessType> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BusinessType> buildQueryWrapper(BusinessTypeBo bo) {
        LambdaQueryWrapper<BusinessType> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getBusinessTypeName()), BusinessType::getBusinessTypeName, bo.getBusinessTypeName());
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessCategoryMajor()), BusinessType::getBusinessCategoryMajor, bo.getBusinessCategoryMajor());
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessCategoryMinor()), BusinessType::getBusinessCategoryMinor, bo.getBusinessCategoryMinor());
        lqw.like(StringUtils.isNotBlank(bo.getBusinessManager()), BusinessType::getBusinessManager, bo.getBusinessManager());
        lqw.orderByAsc(BusinessType::getSort);
        return lqw;
    }

    /**
     * 新增业务类型
     */
    @Override
    public Boolean insertByBo(BusinessTypeBo bo) {
        BusinessType add = BeanUtil.toBean(bo, BusinessType.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改业务类型
     */
    @Override
    public Boolean updateByBo(BusinessTypeBo bo) {
        BusinessType update = BeanUtil.toBean(bo, BusinessType.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BusinessType entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除业务类型
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        List<ProjectResult> projectResults = projectResultMapper.selectList(new LambdaQueryWrapper<ProjectResult>()
            .in(ProjectResult::getBusinessTypeId, ids));
        if (!projectResults.isEmpty()) {
            throw new ServiceException("业务类型关联了项目结果，无法删除");
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
