package com.qmqb.imp.system.domain.dto;

import lombok.Data;

/**
 * 禅道创建任务响应DTO
 *
 * <AUTHOR>
 * @date 2024-12-26
 */
@Data
public class ZentaoTaskResponse {

    /**
     * 任务ID
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long project;

    /**
     * 执行ID
     */
    private Long execution;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务类型
     */
    private String type;

    /**
     * 状态
     */
    private String status;

    /**
     * 指派给
     */
    private Object assignedTo;

    /**
     * 创建人
     */
    private Object openedBy;

    /**
     * 创建时间
     */
    private String openedDate;
} 