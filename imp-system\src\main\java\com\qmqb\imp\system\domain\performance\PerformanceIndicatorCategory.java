package com.qmqb.imp.system.domain.performance;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 绩效指标分类对象 tb_performance_indicator_category
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tb_performance_indicator_category")
public class PerformanceIndicatorCategory extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 绩效主表ID
     */
    private Long performanceId;

    /**
     * 分类编码
     */
    private String categoryCode;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 分类评分等级S/A/B/C/D
     */
    private String categoryLevel;

    /**
     * 分类权重（百分比，如30表示30%）
     */
    private Integer categoryWeight;

    /**
     * 分类得分（数值，如85.5）
     */
    private Double categoryScore;

    /**
     * 分类计算日志
     */
    private String categoryLog;
} 