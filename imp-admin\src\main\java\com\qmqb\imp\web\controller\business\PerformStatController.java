package com.qmqb.imp.web.controller.business;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.constant.HttpStatus;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.domain.dto.PerfStatQueryDTO;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.QueryGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.DocStat;
import com.qmqb.imp.system.domain.bo.WorkDetailBo;
import com.qmqb.imp.system.domain.bo.WorkTotalBO;
import com.qmqb.imp.system.domain.vo.*;
import com.qmqb.imp.system.service.IDocStatService;
import com.qmqb.imp.system.service.IWorkStatService;
import com.qmqb.imp.system.service.IZtDocService;
import com.qmqb.imp.system.service.PerformStatService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Date 2022/12/6
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/business/imsPerformStat")
@Tag(name = "perform-controller", description = "绩效")
public class PerformStatController {

    private final IWorkStatService workStatService;
    private final IDocStatService docStatService;
    private final IZtDocService ztDocService;
    private final PerformStatService performStatService;

    /**
     * 按照分组查询任务列表
     */
    @GetMapping("/getPerfStatList")
    public TableDataInfo<PerfStatVO> getPerfStatList(PerfStatQueryDTO request) {
        return workStatService.getPerfStatList(request);
    }

    /**
     * 导出绩效统计列表
     */
    @PostMapping("/exportPerfStatList")
    public void exportPerfStatList(PerfStatQueryDTO request, HttpServletResponse response) {
        request.setPageSize(Integer.MAX_VALUE);
        TableDataInfo<PerfStatVO> perfStatList = workStatService.getPerfStatList(request);
        List<PerfStatVO> records = perfStatList.getRows();
        List<PerfStatExportVO> exportList = BeanUtil.copyToList(records, PerfStatExportVO.class);
        AtomicInteger index = new AtomicInteger(1);
        exportList.forEach(item -> item.setIndex(index.getAndIncrement()));
        ExcelUtil.exportCsv(exportList, "绩效综合统计", PerfStatExportVO.class, response);
    }

    /**
     * 根据年月获取各组文档统计
     */
    @GetMapping("/getDocStatList")
    public R<List<DocStat>> getDocStatList(Integer doneYear, Integer doneMonth) {
        return R.ok(docStatService.getDocStatList(doneYear, doneMonth));
    }

    /**
     * 根据年月和组名获取组下对应的文档列表
     */
    @GetMapping("/getDocListByGroup")
    public R<List<DocVO>> getDocListByGroup(Integer doneYear, Integer doneMonth, Long groupId) {
        return R.ok(ztDocService.getDocListByGroup(doneYear, doneMonth, groupId));
    }


    /**
     * 根据年月获取每个人的文档数
     */
    @GetMapping("/getDocCountList")
    public R<List<DocCountVO>> getDocCountList(Integer doneYear, Integer doneMonth) {
        return R.ok(ztDocService.selectDocCountVOList(doneYear, doneMonth));
    }

    /**
     * 获取个人文档记录
     * @param workDetail
     * @return
     */
    @GetMapping("/getUserDocList")
    public R<Page<DocVO>> getUserDocList(@Validated(QueryGroup.class) WorkDetailBo workDetail) {
        return R.ok(ztDocService.getUserDocList(workDetail));
    }

    /**
     * 导出个人文档记录
     * @param workDetail
     * @param response
     */
    @PostMapping("/exportUserDocList")
    public void exportUserDocList(WorkDetailBo workDetail, HttpServletResponse response) {
        workDetail.setPageSize(Integer.MAX_VALUE);
        Page<DocVO> page = ztDocService.getUserDocList(workDetail);
        List<DocVO> records = page.getRecords();
        List<DocExportVO> exportList = records.stream().map(item -> {
            DocExportVO docExportVO = new DocExportVO();
            BeanUtils.copyProperties(item, docExportVO);
            return docExportVO;
        }).collect(Collectors.toList());
        ExcelUtil.exportCsv(exportList, "个人文档记录", DocExportVO.class, response);
    }

    /**
     * 根据年月获取每月各组绩效合计
     *
     * @param doneYear  完成年份
     * @param doneMonth 完成月份
     * @return
     */
    @GetMapping("/getWorkTotal")
    public R<List<WorkTotalVO>> getWorkTotal(@RequestParam Integer doneYear, @RequestParam Integer doneMonth) {
        return performStatService.getWorkTotal(doneYear, doneMonth);
    }

    /**
     * 获取统计文档库的信息
     */
    @GetMapping("/getDocLibStatistics")
    public TableDataInfo<DocLibStatisticsVO> getDocLibStatistics(PageQuery pageQuery) {
        return ztDocService.getDocLibStatistics(pageQuery);
    }

    /**
     * 导出每月各组绩效合计列表
     */
    @Log(title = "导出每月各组绩效合计列表", businessType = BusinessType.EXPORT)
    @SaCheckPermission("business:performanceGroup:group:export")
    @PostMapping("/export")
    public void export(WorkTotalBO request, HttpServletResponse response) {
        R<List<WorkTotalVO>> workTotal = performStatService.getWorkTotal(request.getDoneYear(),
            request.getDoneMonth());
        List<WorkTotalExportVO> list;
        if (workTotal.getCode() == HttpStatus.SUCCESS && ObjectUtil.isNotEmpty(workTotal.getData())) {
            list = BeanUtil.copyToList(workTotal.getData(), WorkTotalExportVO.class);
        } else {
            list = Collections.emptyList();
        }
        ExcelUtil.exportExcel(list, "每月各组绩效合计列表数据", WorkTotalExportVO.class, response);
    }
}
