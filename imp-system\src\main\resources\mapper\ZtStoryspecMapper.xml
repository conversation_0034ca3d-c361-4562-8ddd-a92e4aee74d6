<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.ZtStoryspecMapper">

    <resultMap type="com.qmqb.imp.system.domain.ZtStoryspec" id="ZtStoryspecResult">
        <result property="story" column="story"/>
        <result property="version" column="version"/>
        <result property="title" column="title"/>
        <result property="spec" column="spec"/>
        <result property="verify" column="verify"/>
        <result property="files" column="files"/>
        <result property="docs" column="docs"/>
        <result property="docVersions" column="docVersions"/>
    </resultMap>


</mapper>
