package com.qmqb.imp.job.indicator.indicators.impl;

import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.core.domain.entity.SysRole;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.GroupEnum;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.PersonTypeEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.common.utils.ScoreLevelUtil;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.job.indicator.GroupIndicatorCalculateManager;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 工作效率管理指标等级计算
 * <p>
 * 针对技术经理角色，根据其管理团队的工作效率情况和绩效事件登记来评估该指标：
 * - D级：组内有任何一个得D绩效或连续2月本指标无任何绩效事件登记
 * - C级：组内得C的人占一半或以上或本月本指标无任何绩效事件登记
 * - B级：组内人数（含组长）>=4，要求半数以上为B以上；组内人数<4,要求全部为B以上
 * - A级：组内人数（含组长）>=4，要求半数以上为A或S；组内人数<4,要求全部为A或S
 * - S级：组内人数（含组长）>=4，要求半数以上为S；组内人数<4,要求全部为S
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class GroupWorkEfficiencyManageIndicatorServiceImpl implements IndicatorLevelCalcService {

    @Autowired
    ISysUserService userService;
    @Autowired
    GroupIndicatorCalculateManager groupIndicatorCalculateManager;

    @Override
    public String getIndicatorCode() {
        return PerformanceIndicatorEnum.WORK_EFFICIENCY_MANAGE.getCode();
    }

    @Override
    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults) {
        SysUser manager = userService.selectUserByNickName(nickName);
        if (manager == null) {
            throw new ServiceException(String.format("未找到技术经理信息: %s", nickName));
        }
        String roleName = manager.getRoles().stream().findFirst().map(SysRole::getRoleName).orElse(null);
        if (PersonTypeEnum.TECHNICAL_MANAGER.getDesc().equals(roleName)) {
            String deptName = manager.getDept().getDeptName();
            //运维岗位需要合并指标计算：生产操作管理 & 生产操作管理
            if (deptName.equals(GroupEnum.SHUJU.getGroupName()) ||
                deptName.equals(GroupEnum.YUNWEI.getGroupName()) ||
                deptName.equals(GroupEnum.BI.getGroupName())) {
                GroupIndicatorCalculateManager.CaculateDate caculateDate = setYunweiCaculateDate(workResult, manager);
                return  groupIndicatorCalculateManager.calculateLevel(caculateDate,PerformanceIndicatorEnum.WORK_EFFICIENCY_MANAGE.getCode(),manager);
            }
            //其他岗位：工作效率指标
            return groupIndicatorCalculateManager.caculateIndicator(workResult.getWorkYear(), workResult.getWorkMonth(),
                PerformanceIndicatorEnum.DEV_EFFICIENCY.getCode(),manager);
        }
        return ScoreLevelEnum.SCORE_B.getCode();
    }

    public GroupIndicatorCalculateManager.CaculateDate setYunweiCaculateDate(TrackWorkResultVO workResult,SysUser manager) {
        //生产操作管理
        GroupIndicatorCalculateManager.CaculateDate operationDate = groupIndicatorCalculateManager.setCaculateDate(workResult.getWorkYear(), workResult.getWorkMonth(),
            PerformanceIndicatorEnum.PROD_OPERATION_MANAGE.getCode(), manager);
        Map<String, String> operationLevelMap = operationDate.getMemberLevelMap();
        //生产安全管理
        GroupIndicatorCalculateManager.CaculateDate securityDate = groupIndicatorCalculateManager.setCaculateDate(workResult.getWorkYear(), workResult.getWorkMonth(),
            PerformanceIndicatorEnum.PROD_SECURITY_MANAGE.getCode(), manager);
        Map<String, String> securityLevelMap = securityDate.getMemberLevelMap();

        GroupIndicatorCalculateManager.CaculateDate caculateDate = new GroupIndicatorCalculateManager.CaculateDate();
        BeanUtils.copyProperties(operationDate, caculateDate);

        Map<String,Long> levelCountMap = new HashMap<>(5);
        List<SysUser> userList =  operationDate.getTeamMembers().stream().filter(user -> !user.getNickName().equals(manager.getNickName())).collect(Collectors.toList());
        for (SysUser user : userList) {
            List<String> levels = new ArrayList<>();
            levels.add(operationLevelMap.getOrDefault(user.getNickName(),ScoreLevelEnum.SCORE_B.getCode()));
            levels.add(securityLevelMap.getOrDefault(user.getNickName(), ScoreLevelEnum.SCORE_B.getCode()));
            String level = mergeMultipleLevels(levels);
            levelCountMap.put(level, levelCountMap.getOrDefault(level, 0L) + 1L);
        }
        caculateDate.setLevelCountMap(levelCountMap);
        caculateDate.setCurrentRegisterCount(operationDate.getCurrentRegisterCount() + securityDate.getCurrentRegisterCount());
        caculateDate.setLastRegisterCount(operationDate.getLastRegisterCount() + securityDate.getLastRegisterCount());

        return caculateDate;
    }

    /**
     * 合并多个指标等级
     */
    private String mergeMultipleLevels(List<String> levels) {
        double totalA = 0.0;
        int cCount = 0;
        for (String level : levels) {
            totalA += ScoreLevelUtil.convertLevelToAvalue(level);
            if (ScoreLevelEnum.SCORE_C.getCode().equals(level)) {
                cCount++;
            }
        }
        return ScoreLevelUtil.determineCategoryLevel(totalA, cCount,levels);
    }

    @Override
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level) {
        SysUser sysUser = userService.selectUserByNickName(nickName);
        String roleName = sysUser.getRoles().stream().findFirst().map(SysRole::getRoleName).orElse(null);
        if (PersonTypeEnum.TECHNICAL_MANAGER.getDesc().equals(roleName)) {
            GroupIndicatorCalculateManager.CaculateDate caculateDate;

            String deptName = sysUser.getDept().getDeptName();
            //运维岗位需要合并指标计算：生产操作管理 & 生产操作管理
            if (deptName.equals(GroupEnum.SHUJU.getGroupName()) ||
                deptName.equals(GroupEnum.YUNWEI.getGroupName()) ||
                deptName.equals(GroupEnum.BI.getGroupName())) {
                caculateDate = setYunweiCaculateDate(workResult, sysUser);
            } else {
                // 获取计算数据
                caculateDate = groupIndicatorCalculateManager.setCaculateDate(
                    workResult.getWorkYear(),
                    workResult.getWorkMonth(),
                    PerformanceIndicatorEnum.DEV_EFFICIENCY.getCode(),
                    sysUser);
            }

            // 生成日志内容
            StringBuilder logContent = new StringBuilder();
            logContent.append(String.format("[%s]组在%s年%s月份工作效率管理情况：",
                nickName, workResult.getWorkYear(), workResult.getWorkMonth()));

            // 添加团队规模信息
            logContent.append(String.format("团队人数=%d，", caculateDate.getTeamMembers().size()));

            // 添加组员等级分布信息
            Map<String, Long> levelCounts = caculateDate.getLevelCountMap();
            logContent.append("组员等级分布[");
            levelCounts.forEach((k, v) -> logContent.append(String.format("%s=%d ", k, v)));
            logContent.append("]");

            // 添加评级和原因
            logContent.append(String.format("，评级：%s", level));
            String reason = groupIndicatorCalculateManager.getRatingReason(caculateDate, level);
            if (StringUtils.isNotEmpty(reason)) {
                logContent.append(String.format("，原因：%s", reason));
            }
            return logContent.toString();
        }
        return null;
    }


}
