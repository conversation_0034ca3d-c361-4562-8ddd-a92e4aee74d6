package com.qmqb.imp.web.controller.business.process;

import lombok.RequiredArgsConstructor;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.system.domain.vo.process.ProcessCycleInspectionVo;
import com.qmqb.imp.system.domain.bo.process.ProcessCycleInspectionBo;
import com.qmqb.imp.system.service.process.IProcessCycleInspectionService;
import com.qmqb.imp.common.core.page.TableDataInfo;

/**
 * 生产应用每周巡检流程
 *
 * <AUTHOR>
 * @date 2024-12-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/processCycleInspection")
public class ProcessCycleInspectionController extends BaseController {

    private final IProcessCycleInspectionService iProcessCycleInspectionService;

    /**
     * 查询生产应用每周巡检流程列表
     */
    @SaCheckPermission("system:processCycleInspection:list")
    @GetMapping("/list")
    public TableDataInfo<ProcessCycleInspectionVo> list(ProcessCycleInspectionBo bo, PageQuery pageQuery) {
        return iProcessCycleInspectionService.queryPageList(bo, pageQuery);
    }


}
