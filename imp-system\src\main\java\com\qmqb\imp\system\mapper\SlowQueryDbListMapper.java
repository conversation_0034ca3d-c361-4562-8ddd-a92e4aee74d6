package com.qmqb.imp.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.qmqb.imp.common.constant.DataSource;
import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import com.qmqb.imp.system.domain.SlowQueryDbList;
import com.qmqb.imp.system.domain.vo.SlowQueryDbListVO;
import com.qmqb.imp.system.domain.vo.SlowQueryTopVO;
import com.qmqb.imp.system.domain.vo.SlowSqlCountVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/8 17:38
 */
@DS(DataSource.SLOWSQL)
public interface SlowQueryDbListMapper extends BaseMapperPlus<SlowQueryDbListMapper, SlowQueryDbList, SlowQueryDbListVO> {




    /**
     * 慢sqltop信息
     *
     * @param topName
     * @param sqlHashList
     * @return
     */
    List<SlowQueryTopVO> slowSqlTop(@Param("topName") String topName, @Param("sqlHashList")List<String> sqlHashList);


}
