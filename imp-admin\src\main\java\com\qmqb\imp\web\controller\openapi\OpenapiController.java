package com.qmqb.imp.web.controller.openapi;

import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.system.domain.bo.PerformanceReportBo;
import com.qmqb.imp.system.domain.bo.TrackWorkResultBO;
import com.qmqb.imp.system.domain.vo.PerformanceDataVO;
import com.qmqb.imp.system.service.IPerformanceReportService;
import com.qmqb.imp.web.aop.IpWhiteListCheck;
import com.qmqb.imp.system.service.ITrackWorkResultService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @className: OpenapiController
 * @author: yangjiangqian
 * @description:
 * @date: 2025/6/18 10:57
 * @version: 1.0
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/openapi")
public class OpenapiController {

    private final ITrackWorkResultService trackWorkResultService;

    private final IPerformanceReportService iPerformanceReportService;



    @IpWhiteListCheck
    @GetMapping("/trackWorkResult/list")
    public PerformanceDataVO list(TrackWorkResultBO trackWorkResultBO, HttpServletRequest request) {
        trackWorkResultBO.setPageNum(1);
        trackWorkResultBO.setPageSize(1000);
        return trackWorkResultService.listOpenApi(trackWorkResultBO);
    }


    /**
     * 保存绩效分析报告
     */
    @IpWhiteListCheck
    @Log(title = "保存绩效分析报告", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/performanceReport/save")
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PerformanceReportBo bo) {
        return iPerformanceReportService.saveReport(bo) ? R.ok() : R.fail();
    }



}
