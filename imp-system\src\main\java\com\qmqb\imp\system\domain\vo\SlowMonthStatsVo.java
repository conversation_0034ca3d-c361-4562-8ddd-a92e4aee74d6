package com.qmqb.imp.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;


/**
 * 当月慢SQL统计视图对象 t_slow_month_stats
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@ExcelIgnoreUnannotated
public class SlowMonthStatsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * PK
     */
    private Long id;

    /**
     * 库名
     */
    private String dbName;
    /**
     * 数据库备注
     */
    private String dbRemark;
    /**
     * 客户端IP
     */
    private String ip;
    /**
     * 最近执行开始时间
     */
    private LocalDateTime lastExecutionStartTime;
    /**
     * 告警级别（P0、P1、P2）
     */
    private String warnLevel;
    /**
     * 慢查询规则标识<br>
     * returnNull：返回空值<br>
     * returnRowCounts：返回记录数过多<br>
     * parseRowCounts：解析记录数过多<br>
     * query_time：查询时间过长<br>
     * query_hot：慢查询且频繁
     */
    private String slowRule;
    /**
     * 状态：0未指派、1已指派未处理、2已指派已处理、3无需处理
     */
    private String status;
    /**
     * 处理人
     */
    private String processer;
    /**
     * 处理时间
     */
    private LocalDateTime processTime;
    /**
     * 处理说明
     */
    private String remark;
    /**
     * 指派人
     */
    private String assigner;
    /**
     * 指派时间
     */
    private Date assignTime;
    /**
     * 总查询次数（近7天）
     */
    private Long totalQueryTimes;
    /**
     * 总查询时间（秒，近7天）
     */
    private Long totalSumTime;

    /**
     * 平均查询时间（秒，近7天）
     */
    private Long avgQueryTime;
    /**
     * SQLHash值
     */
    private String sqlHash;
    /**
     * SQL文本
     */
    private String sqlText;
    /**
     * 指派人id
     */
    private Long assignerId;
    /**
     * 处理人id
     */
    private Long processerId;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
