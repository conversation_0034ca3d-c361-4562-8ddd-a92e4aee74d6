package com.qmqb.imp.web.controller.business;

import java.util.List;
import java.util.Arrays;

import com.qmqb.imp.system.domain.vo.PerformanceStatisticVO;
import com.qmqb.imp.system.service.IPerformanceRegistrationService;
import lombok.RequiredArgsConstructor;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.vo.PerformanceRegistrationVo;
import com.qmqb.imp.system.domain.bo.PerformanceRegistrationBo;
import com.qmqb.imp.common.core.page.TableDataInfo;

/**
 * 绩效登记
 *
 * <AUTHOR>
 * @date 2023-05-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/performanceRegistration")
public class PerformanceRegistrationController extends BaseController {

    private final IPerformanceRegistrationService iPerformanceRegistrationService;

    /**
     * 查询绩效登记列表
     */
    @SaCheckPermission("system:performanceRegistration:list")
    @GetMapping("/list")
    public TableDataInfo<PerformanceRegistrationVo> list(PerformanceRegistrationBo bo, PageQuery pageQuery) {
        return iPerformanceRegistrationService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询绩效统计列表
     */
    @SaCheckPermission("system:performanceRegistration:statistic")
    @GetMapping("/statistic")
    public TableDataInfo<PerformanceStatisticVO> statistic(PerformanceRegistrationBo bo, PageQuery pageQuery) {
        return iPerformanceRegistrationService.getPerformanceStatistic(bo, pageQuery);
    }

    /**
     * 导出绩效登记列表
     */
    @SaCheckPermission("system:performanceRegistration:export")
    @Log(title = "绩效登记", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PerformanceRegistrationBo bo, HttpServletResponse response) {
        List<PerformanceRegistrationVo> list = iPerformanceRegistrationService.queryList(bo);
        ExcelUtil.exportExcel(list, "绩效登记", PerformanceRegistrationVo.class, response);
    }

    /**
     * 获取绩效登记详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:performanceRegistration:query")
    @GetMapping("/{id}")
    public R<PerformanceRegistrationVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(iPerformanceRegistrationService.queryById(id));
    }

    /**
     * 新增绩效登记
     */
    @SaCheckPermission("system:performanceRegistration:add")
    @Log(title = "绩效登记", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PerformanceRegistrationBo bo) {
        return toAjax(iPerformanceRegistrationService.insertByBo(bo));
    }

    /**
     * 修改绩效登记
     */
    @SaCheckPermission("system:performanceRegistration:edit")
    @Log(title = "绩效登记", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PerformanceRegistrationBo bo) {
        return toAjax(iPerformanceRegistrationService.updateByBo(bo));
    }

    /**
     * 删除绩效登记
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:performanceRegistration:remove")
    @Log(title = "绩效登记", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iPerformanceRegistrationService.deleteWithValidByIds(Arrays.asList(ids), true));
    }
}
