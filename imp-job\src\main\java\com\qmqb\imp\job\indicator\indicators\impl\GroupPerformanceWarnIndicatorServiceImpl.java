package com.qmqb.imp.job.indicator.indicators.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.common.enums.WarnLevelEnum;
import com.qmqb.imp.job.indicator.indicators.IndicatorLevelCalcService;
import com.qmqb.imp.system.domain.WarnRecord;
import com.qmqb.imp.system.domain.performance.Performance;
import com.qmqb.imp.system.domain.performance.PerformanceIndicator;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.mapper.WarnRecordMapper;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.indicator.IPerformanceIndicatorService;
import com.qmqb.imp.system.service.indicator.IPerformanceService;
import com.qmqb.imp.system.service.IUserKqStatService;
import com.qmqb.imp.system.domain.UserKqStat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 组月绩效预警数指标等级计算
 * <p>
 * 针对技术经理角色，根据其管理团队的预警情况来评估该指标：
 * - 计算组内所有成员的预警总数（P0、P1、P2）
 * - 特殊规则：如果组内有任何一个人的预警指标是D级，则技术经理该指标直接评为D级
 * - 否则根据组员预警总数确定等级
 *
 * 等级标准：
 * - S级：无P0、P1预警
 * - A级：无P0预警
 * - B级：无P0预警并且P1预警<5次并且P2预警<8次
 * - C级：P0预警>=2次
 * - D级：组内有成员预警指标为D级（一票否决）
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class GroupPerformanceWarnIndicatorServiceImpl implements IndicatorLevelCalcService {

    @Autowired
    private ISysUserService sysUserService;

    @Resource
    private WarnRecordMapper warnRecordMapper;

    @Autowired
    private IPerformanceIndicatorService performanceIndicatorService;

    @Autowired
    private IPerformanceService performanceService;

    @Autowired
    private IUserKqStatService userKqStatService;

    /**
     * S级标准：无P0、P1预警
     */
    private static final int S_LEVEL_P0_COUNT = CommConstants.CommonVal.ZERO;
    private static final int S_LEVEL_P1_COUNT = CommConstants.CommonVal.ZERO;

    /**
     * A级标准：无P0预警
     */
    private static final int A_LEVEL_P0_COUNT = CommConstants.CommonVal.ZERO;

    /**
     * B级标准：无P0预警并且P1预警<5次并且P2预警<8次
     */
    private static final int B_LEVEL_P0_COUNT = CommConstants.CommonVal.ZERO;
    private static final int B_LEVEL_P1_THRESHOLD = CommConstants.CommonVal.FIVE;
    private static final int B_LEVEL_P2_THRESHOLD = CommConstants.CommonVal.EIGHT;

    /**
     * C级标准：P0预警>=2次
     */
    private static final int C_LEVEL_P0_THRESHOLD = CommConstants.CommonVal.TWO;

    @Override
    public String getIndicatorCode() {
        return PerformanceIndicatorEnum.GROUP_PERFORMANCE_WARN.getCode();
    }

    @Override
    public IndicatorCalcResult calcLevel(String nickName, Integer month, List<TrackWorkResultVO> trackWorkResults) {
        Optional<TrackWorkResultVO> userWorkResultOpt = trackWorkResults.stream()
            .filter(r -> nickName.equals(r.getWorkUsername()))
            .findFirst();

        if (!userWorkResultOpt.isPresent()) {
            String indicatorName = PerformanceIndicatorEnum.GROUP_PERFORMANCE_WARN.getName();
            return new IndicatorCalcResult(ScoreLevelEnum.SCORE_C.getCode(),
                String.format("员工[%s]在%s月份的%s数据未找到", nickName, month, indicatorName));
        }

        TrackWorkResultVO userWorkResult = userWorkResultOpt.get();

        try {
            // 构建团队计算数据
            TeamCalculationData teamData = buildTeamData(userWorkResult, nickName);

            // 检查一票否决
            if (checkDlevelVeto(teamData)) {
                teamData.hasDlevelMember = true;
                String logContent = generateLogContent(teamData, ScoreLevelEnum.SCORE_D.getCode());
                return new IndicatorCalcResult(ScoreLevelEnum.SCORE_D.getCode(), logContent);
            }

            // 统计团队预警数据
            calculateTeamWarnStatistics(teamData);

            // 计算最终等级
            String finalLevel = calculateFinalLevel(teamData);

            // 生成日志
            String logContent = generateLogContent(teamData, finalLevel);

            return new IndicatorCalcResult(finalLevel, logContent);

        } catch (Exception e) {
            log.error("计算技术经理 {} 组月绩效预警数指标失败", nickName, e);
            throw new ServiceException(String.format("技术经理[%s]组月绩效预警数指标计算异常：%s", nickName, e.getMessage()), e);
        }
    }

    @Override
    public String calculateLevel(TrackWorkResultVO workResult, String nickName, List<TrackWorkResultVO> trackWorkResults) {
        return "";
    }

    /**
     * 构建团队计算数据
     */
    private TeamCalculationData buildTeamData(TrackWorkResultVO workResult, String nickName) {
        Integer year = workResult.getWorkYear();
        Integer month = workResult.getWorkMonth();

        TeamCalculationData data = new TeamCalculationData();
        data.managerName = nickName;
        data.year = year;
        data.month = month;

        // 获取技术经理信息
        data.manager = sysUserService.selectUserByNickName(nickName);
        if (data.manager == null) {
            throw new ServiceException(String.format("未找到技术经理信息: %s", nickName));
        }

        // 获取所有团队成员
        data.allMembers = sysUserService.selectUserByDeptId(data.manager.getDeptId());
        if (data.allMembers.isEmpty()) {
            throw new ServiceException(String.format("技术经理 %s 的团队无成员", nickName));
        }

        // 过滤请假成员
        filterAbsentMembers(data);

        // 获取团队绩效记录
        data.performanceIds = getTeamPerformanceIds(data);

        return data;
    }

    /**
     * 过滤请假成员（出勤天数为0）
     */
    private void filterAbsentMembers(TeamCalculationData data) {
        List<String> allMemberNames = data.allMembers.stream()
            .map(SysUser::getNickName)
            .collect(Collectors.toList());

        // 获取考勤数据
        List<UserKqStat> attendanceStats = userKqStatService.listByUserNamesAndKqYearAndKqMonth(
            allMemberNames, String.valueOf(data.year), String.valueOf(data.month));

        // 找出请假成员
        Set<String> absentMemberNames = attendanceStats.stream()
            .filter(stat -> stat.getKqAttendanceDays() != null && stat.getKqAttendanceDays().intValue() == CommConstants.CommonVal.ZERO)
            .map(UserKqStat::getKqUserName)
            .collect(Collectors.toSet());

        // 分离有效成员和请假成员
        data.activeMembers = data.allMembers.stream()
            .filter(member -> !absentMemberNames.contains(member.getNickName()))
            .collect(Collectors.toList());

        data.absentMembers = data.allMembers.stream()
            .filter(member -> absentMemberNames.contains(member.getNickName()))
            .collect(Collectors.toList());

        log.info("技术经理 {} 团队成员过滤：总人数{}，请假人数{}，有效人数{}",
            data.managerName, data.allMembers.size(), data.absentMembers.size(), data.activeMembers.size());
    }

    /**
     * 获取团队绩效记录ID
     */
    private List<Long> getTeamPerformanceIds(TeamCalculationData data) {
        List<Long> performanceIds = performanceService.list(new LambdaQueryWrapper<Performance>()
                .eq(Performance::getYear, data.year)
                .eq(Performance::getMonth, data.month)
                .eq(Performance::getGroupId, data.manager.getDeptId()))
                .stream()
                .map(Performance::getId)
                .collect(Collectors.toList());

        if (performanceIds.isEmpty()) {
            throw new ServiceException(String.format("技术经理 %s 的团队无绩效记录", data.managerName));
        }
        return performanceIds;
    }

    /**
     * 检查一票否决（是否有D级预警指标）
     */
    private boolean checkDlevelVeto(TeamCalculationData data) {
        List<PerformanceIndicator> warnIndicators = performanceIndicatorService.list(
            new LambdaQueryWrapper<PerformanceIndicator>()
                .in(PerformanceIndicator::getPerformanceId, data.performanceIds)
                .eq(PerformanceIndicator::getIndicatorCode, PerformanceIndicatorEnum.PERFORMANCE_WARN.getCode()));

        for (PerformanceIndicator indicator : warnIndicators) {
            String memberLevel = indicator.getScoreLevel();
            if (ScoreLevelEnum.SCORE_D.getCode().equals(memberLevel)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 统计团队预警数据
     */
    private void calculateTeamWarnStatistics(TeamCalculationData data) {
        if (data.activeMembers.isEmpty()) {
            log.warn("技术经理 {} 的团队无有效成员（全部请假）", data.managerName);
            data.warnStatistics = new WarnStatistics(CommConstants.CommonVal.ZERO, CommConstants.CommonVal.ZERO,
                CommConstants.CommonVal.ZERO, CommConstants.CommonVal.ZERO);
            data.memberWarnCounts = new HashMap<>(16);
            return;
        }

        YearMonth yearMonth = YearMonth.of(data.year, data.month);
        data.memberWarnCounts = new HashMap<>(CommConstants.CommonVal.SIXTEEN);
        int totalP0 = CommConstants.CommonVal.ZERO;
        int totalP1 = CommConstants.CommonVal.ZERO;
        int totalP2 = CommConstants.CommonVal.ZERO;
        int totalAll = CommConstants.CommonVal.ZERO;

        for (SysUser member : data.activeMembers) {
            String memberName = member.getNickName();
            List<WarnRecord> memberWarns = getMemberWarnRecords(member.getUserId(), yearMonth);

            // 统计该成员的各级别预警数
            int p0Count = countWarnRecordsByLevel(memberWarns, WarnLevelEnum.P0);
            int p1Count = countWarnRecordsByLevel(memberWarns, WarnLevelEnum.P1);
            int p2Count = countWarnRecordsByLevel(memberWarns, WarnLevelEnum.P2);
            int allCount = memberWarns.size();

            WarnCount warnCount = new WarnCount(p0Count, p1Count, p2Count, allCount);
            data.memberWarnCounts.put(memberName, warnCount);

            totalP0 += p0Count;
            totalP1 += p1Count;
            totalP2 += p2Count;
            totalAll += allCount;

            log.debug("成员 {} 预警统计：P0={}, P1={}, P2={}, 总计={}", memberName, p0Count, p1Count, p2Count, allCount);
        }

        data.warnStatistics = new WarnStatistics(totalP0, totalP1, totalP2, totalAll);
    }

    /**
     * 获取成员某月的预警记录
     */
    private List<WarnRecord> getMemberWarnRecords(Long userId, YearMonth yearMonth) {
        LocalDateTime start = yearMonth.atDay(CommConstants.CommonVal.ONE).atStartOfDay();
        LocalDateTime end = yearMonth.atEndOfMonth().atTime(23, 59, 59);

        return warnRecordMapper.selectList(new LambdaQueryWrapper<WarnRecord>()
            .eq(WarnRecord::getUserId, userId)
            .between(WarnRecord::getCreateTime, start, end));
    }

    /**
     * 按级别统计预警记录数量
     */
    private int countWarnRecordsByLevel(List<WarnRecord> records, WarnLevelEnum level) {
        return (int) records.stream()
            .filter(r -> r.getWarnLevel().equals(level.getValue()))
            .count();
    }

    /**
     * 计算最终等级
     */
    private String calculateFinalLevel(TeamCalculationData data) {
        WarnStatistics stats = data.warnStatistics;

        // D级：组内有成员预警指标为D级（已在一票否决中处理）

        // C级：P0预警>=2次
        if (stats.totalP0Count >= C_LEVEL_P0_THRESHOLD) {
            return ScoreLevelEnum.SCORE_C.getCode();
        }

        // S级：无P0、P1预警
        if (stats.totalP0Count == S_LEVEL_P0_COUNT && stats.totalP1Count == S_LEVEL_P1_COUNT) {
            return ScoreLevelEnum.SCORE_S.getCode();
        }

        // A级：无P0预警
        if (stats.totalP0Count == A_LEVEL_P0_COUNT) {
            return ScoreLevelEnum.SCORE_A.getCode();
        }

        // B级：无P0预警并且P1预警<5次并且P2预警<8次
        if (stats.totalP0Count == B_LEVEL_P0_COUNT
            && stats.totalP1Count < B_LEVEL_P1_THRESHOLD
            && stats.totalP2Count < B_LEVEL_P2_THRESHOLD) {
            return ScoreLevelEnum.SCORE_B.getCode();
        }

        // 默认返回C级
        return ScoreLevelEnum.SCORE_C.getCode();
    }

    @Override
    public String createLogContent(TrackWorkResultVO workResult, String nickName, String level) {
        return "";
    }

    /**
     * 生成详细日志内容
     */
    private String generateLogContent(TeamCalculationData data, String level) {
        StringBuilder logContent = new StringBuilder();

        logContent.append(String.format("[%s]在%s年%s月份的组月绩效预警数指标",
            data.managerName, data.year, data.month));

        // 基本统计信息
        logContent.append(String.format("，团队总人数：%d人", data.allMembers.size()));
        logContent.append(String.format("，有效成员数(排除请假)：%d人", data.activeMembers.size()));

        if (data.warnStatistics != null) {
            logContent.append(String.format("，P0预警：%d次", data.warnStatistics.totalP0Count));
            logContent.append(String.format("，P1预警：%d次", data.warnStatistics.totalP1Count));
            logContent.append(String.format("，P2预警：%d次", data.warnStatistics.totalP2Count));
            logContent.append(String.format("，总预警：%d次", data.warnStatistics.totalAllCount));
        }

        logContent.append("，评级：").append(level);

        // 成员预警分布
        appendMemberWarnDistribution(logContent, data);

        // 请假成员信息
        if (!data.absentMembers.isEmpty()) {
            logContent.append("\n  请假成员（已排除）：");
            for (SysUser absentMember : data.absentMembers) {
                logContent.append(String.format("\n    %s：当月请假未上班", absentMember.getNickName()));
            }
        }

        // 评级原因
        String reason = getRatingReason(level, data);
        if (reason != null) {
            logContent.append("\n  计算原因：").append(reason);
        }

        return logContent.toString();
    }

    /**
     * 添加成员预警分布统计
     */
    private void appendMemberWarnDistribution(StringBuilder logContent, TeamCalculationData data) {
        if (data.memberWarnCounts != null && !data.memberWarnCounts.isEmpty()) {
            logContent.append("\n  成员预警分布：");

            // 按总预警数排序显示
            List<Map.Entry<String, WarnCount>> sortedEntries = data.memberWarnCounts.entrySet().stream()
                .sorted(Map.Entry.<String, WarnCount>comparingByValue((w1, w2) -> Integer.compare(w2.allCount, w1.allCount)))
                .collect(Collectors.toList());

            for (Map.Entry<String, WarnCount> entry : sortedEntries) {
                String memberName = entry.getKey();
                WarnCount warnCount = entry.getValue();
                logContent.append(String.format("\n    %s：P0=%d，P1=%d，P2=%d，总计=%d",
                    memberName, warnCount.p0Count, warnCount.p1Count, warnCount.p2Count, warnCount.allCount));
            }
        }
    }

    /**
     * 获取评级原因
     */
    private String getRatingReason(String level, TeamCalculationData data) {
        if (data.hasDlevelMember) {
            return "组内有成员预警指标为D级";
        }

        if (data.warnStatistics == null) {
            return "预警统计数据异常";
        }

        WarnStatistics stats = data.warnStatistics;
        int teamSize = data.activeMembers.size();

        switch (level) {
            case "S":
                return String.format("团队%d人无P0、P1预警，达到S级标准", teamSize);
            case "A":
                return String.format("团队%d人无P0预警，达到A级标准", teamSize);
            case "B":
                return String.format("团队%d人无P0预警且P1预警(%d)<5次且P2预警(%d)<8次，达到B级标准",
                    teamSize, stats.totalP1Count, stats.totalP2Count);
            case "C":
                if (stats.totalP0Count >= C_LEVEL_P0_THRESHOLD) {
                    return String.format("团队%d人P0预警数(%d)≥2次，达到C级标准", teamSize, stats.totalP0Count);
                } else {
                    return String.format("团队%d人预警情况未达到更高等级要求", teamSize);
                }
            case "D":
                return "组内有成员预警指标为D级";
            default:
                return null;
        }
    }

    /**
     * 预警统计数据类
     */
    private static class WarnStatistics {
        final int totalP0Count;
        final int totalP1Count;
        final int totalP2Count;
        final int totalAllCount;

        WarnStatistics(int totalP0Count, int totalP1Count, int totalP2Count, int totalAllCount) {
            this.totalP0Count = totalP0Count;
            this.totalP1Count = totalP1Count;
            this.totalP2Count = totalP2Count;
            this.totalAllCount = totalAllCount;
        }
    }

    /**
     * 成员预警数统计类
     */
    private static class WarnCount {
        final int p0Count;
        final int p1Count;
        final int p2Count;
        final int allCount;

        WarnCount(int p0Count, int p1Count, int p2Count, int allCount) {
            this.p0Count = p0Count;
            this.p1Count = p1Count;
            this.p2Count = p2Count;
            this.allCount = allCount;
        }
    }

    /**
     * 团队计算数据类
     */
    private static class TeamCalculationData {
        String managerName;
        Integer year;
        Integer month;
        SysUser manager;
        List<SysUser> allMembers;
        List<SysUser> activeMembers;
        List<SysUser> absentMembers;
        List<Long> performanceIds;

        boolean hasDlevelMember;
        Map<String, WarnCount> memberWarnCounts;
        WarnStatistics warnStatistics;
    }
}
