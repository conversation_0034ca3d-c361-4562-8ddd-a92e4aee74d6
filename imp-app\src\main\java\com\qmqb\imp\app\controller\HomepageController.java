package com.qmqb.imp.app.controller;

import com.qmqb.imp.app.domain.bo.DateBO;
import com.qmqb.imp.app.domain.bo.KeyIndicatorBO;
import com.qmqb.imp.app.domain.vo.KeyIndicatorVO;
import com.qmqb.imp.app.domain.vo.MessageInfoVO;
import com.qmqb.imp.app.domain.vo.PerformanceEvaluateBizVO;
import com.qmqb.imp.app.domain.vo.PerformanceReportVO;
import com.qmqb.imp.app.service.HomepageService;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.helper.LoginHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 首页
 *
 * <AUTHOR> Li
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/homepage")
public class HomepageController {

    private final HomepageService homepageService;

    /**
     * 消息处理情况
     *
     * @return 结果
     */
    @GetMapping("/message-info")
    public R<MessageInfoVO> messageInfo() {
        return homepageService.messageInfo();
    }

    /**
     * 个人绩效报告
     *
     * @return 结果
     */
    @GetMapping("/personal-performance-report")
    public R<PerformanceReportVO> personalPerformanceReport(@Valid DateBO bo) {
        Long userId = LoginHelper.getUserId();
        return homepageService.personalPerformanceReport(bo, userId);
    }

    /**
     * 组绩效报告
     *
     * @return 结果
     */
    @GetMapping("/team-performance-report")
    public R<PerformanceReportVO> teamPerformanceReport(@Valid DateBO bo) {
        return homepageService.teamPerformanceReport(bo);
    }

    /**
     * 最新绩效点评
     *
     * @return 结果
     */
    @GetMapping("/performance-evaluate")
    public R<PerformanceEvaluateBizVO> performanceEvaluate() {
        return homepageService.performanceEvaluate();
    }

    /**
     * 关键指标
     *
     * @return 结果
     */
    @GetMapping("/key-indicator")
    public R<KeyIndicatorVO> keyIndicator(@Valid KeyIndicatorBO bo) {
        return homepageService.keyIndicator(bo, null);
    }

}
