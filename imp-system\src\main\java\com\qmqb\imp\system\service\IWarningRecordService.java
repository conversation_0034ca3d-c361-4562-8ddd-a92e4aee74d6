package com.qmqb.imp.system.service;

import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.bo.WarningRecordBo;
import com.qmqb.imp.system.domain.vo.WarningRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
public interface IWarningRecordService {

    /**
     * 查询【请填写功能名称】
     *
     * @param id
     * @return
     */
    WarningRecordVo queryById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<WarningRecordVo> queryPageList(WarningRecordBo bo, PageQuery pageQuery);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param bo
     * @return
     */
    List<WarningRecordVo> queryList(WarningRecordBo bo);

    /**
     * 新增【请填写功能名称】
     *
     * @param bo
     * @return
     */
    Boolean insertByBo(WarningRecordBo bo);

    /**
     * 修改【请填写功能名称】
     *
     * @param bo
     * @return
     */
    Boolean updateByBo(WarningRecordBo bo);

    /**
     * 校验并批量删除【请填写功能名称】信息
     *
     * @param ids
     * @param isValid
     * @return
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
