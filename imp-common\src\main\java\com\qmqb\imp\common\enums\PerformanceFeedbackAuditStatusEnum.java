package com.qmqb.imp.common.enums;

import lombok.Getter;

/**
 * 绩效反馈审核状态枚举
 * <p>
 * 定义了绩效反馈的审核状态，包括：
 * - 未审核：等待审核
 * - 同意：审核通过
 * - 拒绝：审核拒绝
 * </p>
 *
 * <AUTHOR>
 */
@Getter
public enum PerformanceFeedbackAuditStatusEnum {
    /**
     * 未审核
     */
    NOT_AUDITED("NOT_AUDITED", "未审核"),

    /**
     * 同意
     */
    APPROVED("APPROVED", "同意"),

    /**
     * 拒绝
     */
    REJECTED("REJECTED", "拒绝");

    /**
     * 状态编码
     */
    private final String code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 构造方法
     *
     * @param code 状态编码
     * @param name 状态名称
     */
    PerformanceFeedbackAuditStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static PerformanceFeedbackAuditStatusEnum getByCode(String code) {
        for (PerformanceFeedbackAuditStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为有效状态
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidStatus(String code) {
        return getByCode(code) != null;
    }

    /**
     * 判断是否为最终状态（同意或拒绝）
     *
     * @param code 编码
     * @return 是否为最终状态
     */
    public static boolean isFinalStatus(String code) {
        return APPROVED.getCode().equals(code) || REJECTED.getCode().equals(code);
    }
}
