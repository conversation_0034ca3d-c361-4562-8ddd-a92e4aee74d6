package com.qmqb.imp.system.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 慢sql日志
 * @date 2025/5/28 16:15
 */
@Data
public class SlowQueryLogVo {


    private Long id;

    /**
     * 该字段用于记录数据库的名称。
     */
    private String dbName;

    /**
     * 该字段记录执行查询的 IP 地址。
     */
    private String ip;

    /**
     * 该字段记录执行查询的用户。
     */
    private String user;

    /**
     * 该字段用于记录查询开始的时间，使用 LocalDateTime 类型来避免时区问题。
     */
    private LocalDateTime executionStartTime;

    /**
     * 该字段记录查询解析的行数。
     */
    private String parseRowCounts;

    /**
     * 该字段记录查询返回的行数。
     */
    private String returnRowCounts;

    /**
     * 该字段记录查询执行的时间（单位：毫秒）。
     */
    private Long queryTimeMs;

    /**
     * 该字段用于存储 SQL 查询的哈希值，便于快速检索和匹配。
     */
    private String sqlHash;

    /**
     * 该字段存储实际执行的 SQL 查询文本。
     */
    private String sqlText;
}
